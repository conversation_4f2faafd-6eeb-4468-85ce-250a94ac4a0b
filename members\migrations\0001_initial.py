# Generated by Django 5.1.7 on 2025-03-20 01:17

import django.contrib.auth.validators
import django.utils.timezone
import members.models
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='LoginLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.CharField(blank=True, max_length=200, null=True, verbose_name='用户名')),
                ('status', models.CharField(choices=[('0', '否'), ('1', '是')], default=1, max_length=5, verbose_name='登录状态')),
                ('create_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='登录时间')),
            ],
            options={
                'db_table': 'ai_login_Log',
            },
        ),
        migrations.CreateModel(
            name='Members',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('password', models.CharField(max_length=255, verbose_name='登录密码')),
                ('real_name', models.CharField(blank='', default='', max_length=20, null='', verbose_name='成员姓名')),
                ('nick', models.CharField(blank='', default='', max_length=20, null='', verbose_name='昵称')),
                ('phone', models.CharField(blank='', default='', max_length=20, null='', verbose_name='联系电话')),
                ('status', models.CharField(choices=[('0', '正常'), ('2', '删除'), ('1', '锁定')], default=1, max_length=5, verbose_name='用户状态')),
                ('roles', models.CharField(blank=True, choices=[('0', '超级管理员'), ('1', '管理员'), ('2', '普通用户'), ('3', '新用户')], default=3, max_length=5, null=True, verbose_name='用户权限')),
                ('provider', models.CharField(default='local', max_length=5, verbose_name='用户类型')),
                ('company_id', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('user_id', models.CharField(blank=True, default='', max_length=255, null=True)),
                ('user_available', models.BooleanField(default=False, verbose_name='账户是否可用')),
                ('gpt_available', models.BooleanField(default=False, verbose_name='GPT是否可用')),
                ('access_token', models.DecimalField(decimal_places=5, default=580, max_digits=15, verbose_name='Authing的AccessToken')),
                ('uuid_user', models.UUIDField(blank=True, default=uuid.uuid4, editable=False, unique=True, verbose_name='用户唯一标识码')),
                ('custom_id', models.CharField(default=members.models.generate_custom_id, max_length=24, unique=True)),
                ('is_active', models.BooleanField(default=True, verbose_name='是否要激活')),
                ('is_staff', models.BooleanField(default=True, verbose_name='是否激活员工')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='data/avatars/', verbose_name='头像')),
                ('openID', models.CharField(blank=True, max_length=200, null=True, unique=True, verbose_name='用户微信唯一凭证')),
                ('tmp_scene', models.CharField(blank=True, max_length=100, null=True, verbose_name='登录临时凭证')),
                ('depart_name', models.CharField(blank='', default='', max_length=20, null='', verbose_name='部门名称')),
                ('departments', models.ManyToManyField(blank=True, related_name='departments_members', to=settings.AUTH_USER_MODEL)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': '成员',
                'db_table': 'ai_members',
                'abstract': False,
                'swappable': 'AUTH_USER_MODEL',
            },
        ),
    ]
