aiohappyeyeballs==2.5.0
aiohttp==3.11.13
aiosignal==1.3.2
amqp==5.3.1
annotated-types==0.7.0
anyio==4.8.0
asgiref==3.8.1
attrs==25.1.0
beautifulsoup4==4.13.3
billiard==4.2.1
celery==5.4.0
certifi==2025.1.31
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.1
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cobble==0.1.4
colorama==0.4.6
coloredlogs==15.0.1
cryptography==44.0.2
dataclasses-json==0.6.7
Deprecated==1.2.18
dirtyjson==1.0.8
distro==1.9.0
Django==5.1.7
django-environ==0.12.0
django-redis==5.4.0
djangorestframework==3.15.2
djangorestframework_simplejwt==5.5.0
et_xmlfile==2.0.0
filelock==3.17.0
filetype==1.2.0
flatbuffers==25.2.10
frozenlist==1.5.0
fsspec==2025.3.0
greenlet==3.1.1
grpcio==1.67.1
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.29.2
humanfriendly==10.0
idna==3.10
jieba==0.42.1
Jinja2==3.1.6
jiter==0.9.0
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
kombu==5.4.2
langchain==0.3.20
langchain-community==0.3.19
langchain-core==0.3.43
langchain-huggingface==0.1.2
langchain-text-splitters==0.3.6
langsmith==0.3.13
llama-cloud==0.1.14
llama-cloud-services==0.6.5
llama-index==0.12.23
llama-index-agent-openai==0.4.6
llama-index-cli==0.4.1
llama-index-core==0.12.23.post2
llama-index-embeddings-openai==0.3.1
llama-index-indices-managed-llama-cloud==0.6.8
llama-index-llms-openai==0.3.25
llama-index-multi-modal-llms-openai==0.4.3
llama-index-program-openai==0.3.1
llama-index-question-gen-openai==0.3.0
llama-index-readers-file==0.4.6
llama-index-readers-llama-parse==0.4.0
llama-parse==0.6.4.post1
llvmlite==0.44.0
loguru==0.7.3
lxml==5.3.1
magika==0.6.2
mammoth==1.9.0
markdownify==1.1.0
markitdown==0.1.1
MarkupSafe==3.0.2
marshmallow==3.26.1
more-itertools==10.7.0
mpmath==1.3.0
multidict==6.1.0
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numba==0.61.2
numpy==2.2.3
onnxruntime==1.22.0
openai==1.65.5
openai-whisper==20250625
openpyxl==3.1.5
orjson==3.10.15
packaging==24.2
pandas==2.2.3
pillow==11.1.0
playwright==1.53.0
prompt_toolkit==3.0.50
propcache==0.3.0
protobuf==6.30.0
pycparser==2.22
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
pyee==13.0.0
PyJWT==2.9.0
pymilvus==2.5.5
PyMuPDF==1.25.3
PyMySQL==1.1.1
pypdf==5.3.1
pyreadline3==3.5.4
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.1
python-pptx==1.0.2
pytz==2025.1
PyYAML==6.0.2
redis==5.2.1
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
sentence-transformers==3.4.1
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.38
sqlparse==0.5.3
striprtf==0.0.26
sympy==1.13.1
tenacity==9.0.0
threadpoolctl==3.5.0
tiktoken==0.9.0
tokenizers==0.21.0
torch==2.6.0
tqdm==4.67.1
transformers==4.49.0
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
ujson==5.10.0
urllib3==2.3.0
vine==5.1.0
wcwidth==0.2.13
webvtt-py==0.5.1
win32_setctime==1.2.0
wrapt==1.17.2
xlrd==2.0.1
XlsxWriter==3.2.2
yarl==1.18.3
yt-dlp==2025.6.30
zstandard==0.23.0
