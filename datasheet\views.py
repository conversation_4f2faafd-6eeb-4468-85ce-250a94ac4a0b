import os,ast
from rest_framework import  viewsets
from untils.untils import message as mg, Mixin, MixinID, CheckPermissions, DestroyMixin,PaginationMixin
from .models import DataSheet, DataFrame, Page
from .sheetserializers import SheetSerializer, FrameSerializer
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from django.http import JsonResponse
from rest_framework.response import Response
from rest_framework import status
from loguru import logger
from rest_framework.exceptions import PermissionDenied
from datasheet.tasks import insert_to_vector,split_to_mysql,delete_from_vector,extract_preview

from rest_framework.views import APIView
from untils.common import milvus_naming
from django.db.models import Q
import time
# Create your views here.
from .verctor_db import VectorDB
from untils.permission import VerifyAPIkey
from langchain.docstore.document import Document as DOC

import environ

env = environ.Env()
# # reading .env file
# environ.Env.read_env()

# # Assuming you have DEBUG=True in your .env file
class SheetByUserViewSet(APIView):
    queryset = DataSheet.objects.all()
    serializer_class = SheetSerializer
    def get(self,request):
        """
        根据请求中的type参数过滤查询集。
        """        
        
        # queryset = DataSheet.objects.all()
        queryset = self.queryset.filter(uuid_user = request.user)
        logger.info(queryset)# 使用序列化器序列化数据
        serializer = self.serializer_class(queryset, many=True)
        
        return JsonResponse(mg(data=serializer.data))
class SheetByNotebookViewSet(APIView):
    queryset = DataSheet.objects.all().filter(sheet_type ='notebook')
    serializer_class = SheetSerializer
    def get(self,request):
        """
        根据请求中的type参数过滤查询集。
        """        
        
        # queryset = DataSheet.objects.all()
        queryset = self.queryset.filter(uuid_user = request.user).annotate(
            total=Count('sheet_frame')
        )
        logger.info(queryset)# 使用序列化器序列化数据
        serializer = self.serializer_class(queryset, many=True)
        
        return JsonResponse(mg(data=serializer.data))
    
class SheetSummaryViewSet(APIView):
    def post(self, request, *args, **kwargs):
        from untils.ai_client import AIClient
        try:
            content = self.request.data.get('content')
            result =  AIClient().analyze_content(content, 'summary')
            if result.get("success"):
                data = json.loads(result.get("data", {}))
                return JsonResponse(mg(data=data))
            return JsonResponse(mg(code=400,msg=str(result.get("msg"))))
        except Exception as e:
            logger.error(f"Error extracting preview: {str(e)}")
            return JsonResponse(mg(code=400,msg=str(e)))

class SheetViewSet(Mixin):
    queryset = DataSheet.objects.all()
    serializer_class = SheetSerializer

    def perform_create(self, serializer):
        # 获取当前用户
        user = self.request.user# 和当前用户关联
        logger.info(user)
        serializer.save(uuid_user=user,username=user.username)

    def get_queryset(self):
        """
        根据请求中的type参数过滤查询集。
        """
        queryset = DataSheet.objects.all()
        # 检查请求是否来自管理端
        # 创建一个可变的字典副本
        query_params = self.request.query_params.copy()
        
        # 检查请求是否来自管理端
        is_admin = query_params.pop('is_admin', 'false')[0].lower() == 'true'
        # 构建查询条件
        query = Q()
        for key, value in query_params.lists():
            # 这里可以加入一些逻辑来验证key是否是有效的数据库字段名
            if key == 'id':
            # 尝试解析 ID 列表
                try:
                    # 检查值是否为空
                    if not value[0]:
                        return None
                    # 如果值是一个字符串，尝试将其解析为 JSON
                    # ids = json.loads(value[0])
                    # 确保解析后的值是一个列表
                    if not isinstance(value, list):
                        raise ValueError("ID参数格式不正确")
                    ids = [int(id) for id in value[0].split(',')]
                    # 过滤 ID 列表
                    query &= Q(**{f'{key}__in': ids})
                except json.JSONDecodeError:
                    raise ValueError("ID参数格式不正确")
            else:
                query &= Q(**{key: value[0]})
        logger.debug(query)
        
        # query |= Q(sheet_type ='laws')
         # 根据请求来源决定是否过滤 sheet_level
        if not is_admin:
            queryset = queryset.filter(query).filter(sheet_level__gt=0)
        else:
            logger.debug('-------------all-------------')
            queryset = queryset.filter(query)  # 管理端不需要额外过滤
        
        return queryset

class SheetViewUpdate(MixinID):#CheckPermissions,
    queryset = DataSheet.objects.all()
    serializer_class = SheetSerializer
    lookup_field = 'id'

class FrameUpdateViewSet(DestroyMixin,MixinID,viewsets.GenericViewSet):#CheckPermissions,
    queryset = DataFrame.objects.all()
    serializer_class = FrameSerializer
    lookup_field = 'id'

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        # 获取文件路径
        file_path = instance.file.path
        logger.info(f'delete:{file_path}')
        # 删除文件
        if os.path.exists(file_path):
            os.remove(file_path)

        # 调用父类的 destroy 方法删除数据库记录
        return super().destroy(request, *args, **kwargs)
    
    def multiple_destroy(self, request, *args, **kwargs):
        # 从 URL 参数获取 id 列表
        ids = kwargs.get('ids', '').split(',')
        ids = [int(id_) for id_ in ids if id_.isdigit()]
        queryset = self.get_queryset().filter(id__in=ids)
        
        # 删除关联的文件
        for instance in queryset:
            file_path = instance.file.path
            logger.info(f'delete file:{file_path}')
            if os.path.exists(file_path):
                os.remove(file_path)

        # 调用父类方法执行批量删除
        return super().multiple_destroy(request, *args, **kwargs)
    
    def group_destroy(self, request, *args, **kwargs):
        cache_id = kwargs.get('cache_id')
        queryset = self.get_queryset().filter(cache_id=cache_id)
        
        for instance in queryset:
            file_path = instance.file.path
            logger.info(f'delete file:{file_path}')
            if os.path.exists(file_path):
                os.remove(file_path)

        return super().group_destroy(request, *args, **kwargs)
    
from django.core.cache import cache
def update_cache(cache_key,data,timeout=14400):
    # 使用原子操作
    with cache.lock(cache_key):  # 如果支持
        existing_data = cache.get(cache_key, [])
        existing_data.extend(data)
        cache.set(cache_key, existing_data, timeout=timeout)

class FrameUploadViewSet(    PaginationMixin, CheckPermissions,   Mixin):
    parser_classes = (MultiPartParser, FormParser)
    queryset = DataFrame.objects.all()
    serializer_class = FrameSerializer  
        
    def list(self, request, *args, **kwargs):
        # 获取当前登录用户的数据
        user = self.request.user
        # uid_sheet = self.request.GET.get('uid_sheet') if self.request.GET.get('uid_sheet') else 500
        uid_sheet,keyword,available,unavailable = self.get_request_params(request,'uid_sheet','keyword','available','unavailable')
        query =''
        
        # logger.warning(f'关键字:{keyword},用户:{user.uuid_user}')
        if not keyword:
            query = self.queryset.filter(uid_sheet=uid_sheet).order_by('-create_time')
            # return self.get_paginated_response(data, self.serializer_class)
        elif user.is_superuser:
            query= self.queryset.filter(file_name__icontains=keyword,uid_sheet=uid_sheet).order_by('-create_time')
        else:
            query = self.queryset.filter(file_name__icontains=keyword,uid_sheet=uid_sheet).order_by('-create_time')#,uuid_user=user.uuid_user
        if available:
            query = query.filter(available=available).order_by('-create_time')
        if unavailable:
            query = query.exclude(available=unavailable).order_by('-create_time')
        # 使用分页逻辑
        # page = self.paginate_data(query)
        # serializer = self.serializer_class(page, many=True)
        return self.get_paginated_response(query, self.serializer_class)

    def perform_create(self, serializer):
        user = self.request.user
        logger.info(user.username)
        if not user.is_authenticated:
            raise PermissionDenied('鉴权失败！请重新登录或联系管理员')
        
        # serializer.save(uuid_user = user)

        file_obj = self.request.FILES.getlist('file')
        uid_sheet = self.request.POST.get('uid_sheet')
        source = self.request.POST.get('source')
        
        RAW_FOLDER = os.getenv("TEM_FOLDER", "data/tempy")
        # 生成一串随机字符串作为文件夹名
        timestr = str(time.time()).replace(".", "")
        folder_name = self.request.POST.get('cache_id',timestr)
        raw_dir = os.path.join(RAW_FOLDER, str(user.username)+'/'+folder_name)
        if not os.path.exists(raw_dir):
            os.makedirs(raw_dir)
        if source:
            raw_dir = os.path.join(RAW_FOLDER, str(user.username),folder_name+'_'+source)
            print('文件夹路径:',raw_dir)
            if not os.path.exists(raw_dir):
                os.makedirs(raw_dir)
        res = []
        sheetobj=DataSheet.objects.get(id = uid_sheet)
        sector_name = sheetobj.sector_name
        # if user.is_staff:
        #     sector_name = user.uuid_company.company_name
        # 获取根目录
        BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        for file in file_obj:
            if file:
                rename = str(time.time()).replace(".", "")+os.path.splitext(file.name)[1]#重命名
                try:
                    data = {
                        'cache_id':folder_name,
                        'file': file,
                        'file_size': file.size,     # 获取文件大小
                        'file_name': file.name,     # 获取文件名称
                        'uid_sheet': uid_sheet,     # 跟知识库绑定
                        'rename':rename,
                        'sector':sector_name,
                        'desc':'',
                        'uuid_user':    str(user.uuid_user),
                    }
                    # 创建一个新的serializer实例，将文件数据传递给它
                    new_serializer = FrameSerializer(data=data)
                    if new_serializer.is_valid():
                        instance = new_serializer.save()
                        res.append(new_serializer.data)

                        # 文件复制操作
                        target_file_path = os.path.join(raw_dir, file.name)
                        with open(target_file_path, 'wb+') as destination:
                            for chunk in file.chunks():
                                destination.write(chunk)
                        # auth_headers = {
                        #     k.lower(): v for k, v in self.request.headers.items() 
                        #     if k.lower() in ['authorization', 'x-api-key', 'x-auth-token']
                        # }  
                        
                        # token = self.request.headers.get('Authorization', '') 
                        # logger.info(f'token:{token}')
                        extract_preview.delay(target_file_path, instance.id)
                except Exception as e:
                    # 如果无效，抛出异常或进行适当的处理
                    logger.error(str(e))
        # logger.info(f"Upload {len(res)} files：{res}")  
        logger.info(f'{sheetobj},上传：{len(res)} 个文件')
        # up_info = []
        # for d in res:
        #     data={
        #         'id':d.get('id'),
        #         'file_name':d.get('file_name'),
        #         'rename':d.get('rename'),
        #     } 
        #     # 额外的验证
        #     if not data['id']:
        #         logger.warning(f"Skipping entry without ID: {d}")
        #         continue
        #     up_info.append(data) 
        self.target_folder = raw_dir
        self.sheetid = uid_sheet
        self.folder_name = folder_name
        self.frameid = res[0].get('id')
        
        #将数据保存到缓存中
        # cache_key = f'upload_{folder_name}'
        # update_cache(cache_key,up_info)
        
    def create(self, request, *args, **kwargs):
        response =  super().create(request, *args, **kwargs)
        # return result.id
        if hasattr(self, 'target_folder'):
            data = {}  # 获取原始响应数据
            data['target_folder'] = self.folder_name#self.target_folder 
            # data['fileslist']=self.data
            data['id'] = self.sheetid# 添加id字段
            data['frameid'] = self.frameid
            response.data = data  # 更新响应数据
        return response  # 返回带有id的响应
        

from untils.parsers.parser import DocumentParser
import shutil,re,uuid,os,fitz
from typing import Union,List
from untils.vector.textsplit import TextSpilt

class BaseView(TextSpilt,APIView):    
    def __init__(self, request=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.request = request
    # 根据ID返回包含index的和不包含index的
    def separate_edit_data(self,edit_data, index):
        """
        将 edit_data 列表根据 'id' 分为两个列表：
        - del_edit_data: 包含 id 等于 index 的数据项    - up_edit_data: 包含 id 不等于 index 的数据项

        :param edit_data: 需要处理的数据列表，每个项是一个包含 'metadata' 的字典
        :param index: 用于比较的 ID 值
        :return: 包含两个列表的元组 (del_edit_data, up_edit_data)
        """
        del_edit_data = [item for item in edit_data if item['metadata'].get('id') == int(index)]
        up_edit_data = [item for item in edit_data if item['metadata'].get('id') != int(index)]
        
        return del_edit_data, up_edit_data
    
    # 从文件夹获取文件切片list
    def folder_get_docs(self,target_folder,request,original=True):
        """
        从指定文件夹获取文件切片列表。

        1. 使用 `DocumentParser` 解析文件夹中的文件，生成保存的文件夹目录。
        2. 根据请求构建解析参数，并将生成的文件夹目录添加到参数中。
        3. 使用 `split_from_folder` 方法对文件夹中的文件进行切割，获取切片文档。
        4. 过滤掉切片文档中的空子列表。
        5. 解析完成后，删除临时生成的解析目录。
        
        :param target_folder: 目标文件夹的路径，包含需要切割的文件。
        :param request: 请求对象，通常包含用户信息和其他请求参数。
        :return: 包含切片文档的列表。如果文件夹中没有文件或切割失败，则返回空列表。
        """
        saved_folder_dir = ''
        doc_parser = DocumentParser()
        if original:
            saved_folder_dir = doc_parser.parse(target_folder,request.user.username)
        
        else:
            if  os.path.isdir(target_folder):
                logger.warning(f'这是从提取文本后保存的文件中提取：{target_folder}')
                saved_folder_dir = target_folder
            else:
                logger.warning(f'这是从原文件中提取：{target_folder}')
                saved_folder_dir = doc_parser.parse(target_folder,request.user.username,False)
                
        # 构建解析参数，包括文件夹路径
        parser_params = self.build_params(request)
        parser_params['folder_path'] = saved_folder_dir
        
        docs = self.split_from_folder(**parser_params)  
        # 返回包含切片文档的列表
        docs = [doc for doc in docs if doc]  # 过滤掉空子列表      
        
        if original or not os.path.isdir(target_folder):
            # 解析完成后删除解析目录
            if os.path.exists(saved_folder_dir) and os.path.isdir(saved_folder_dir):
                # logger.warning(f'remove parser folder:{saved_folder_dir}/*')
                shutil.rmtree(saved_folder_dir)
        
        # 返回包含切片文档的列表
        return docs       
        
        # 解析完成后删除解析目录
        # if os.path.exists(target_folder) and os.path.isdir(target_folder):
        #     logger.warning(f'remove parser folder{target_folder}/*')
        #     shutil.rmtree(target_folder)
        # 从文件夹获取文件切片list
    def import_cache(self,folder,index=None):
        key =f':1:import_{folder}'
        import redis
        import json

        # 直接连接 Redis
        r = redis.Redis.from_url(os.environ.get('CACHE_REDIS_URL'))  # 或者 r = redis.Redis(host='localhost', port=6379, db=0)

        # 获取键对应的值
        raw_value = r.get(key)
        if raw_value is not None:
            raw_data = raw_value.decode('utf-8')
            data=json.loads(raw_data)
            cache=data["value"]            
            if index:
                updated_cache_list = [item for item in cache if item.get("meta", {}).get("id") != int(index)]
                logger.warning(f'根据ID删除导入缓存中的数据:{index}')
                # 将更新后的列表重新序列化为 JSON
                updated_data = {"value": updated_cache_list}
                updated_raw_data = json.dumps(updated_data).encode('utf-8')
                # 将更新后的 JSON 存储回 Redis
                r.set(key, updated_raw_data)
                return {'delete':True}
            else:
                return cache
        else:
            return None
    def cache_get_docs(self,request,cache,index=None):
        """
        从缓存获取文件内容进行切片。

        1. 使用 `DocumentParser` 解析文件夹中的文件，生成保存的文件夹目录。
        2. 根据请求构建解析参数，并将生成的文件夹目录添加到参数中。
        3. 使用 `split_from_cache` 方法对文件夹中的文件进行切割，获取切片文档。
        4. 过滤掉切片文档中的空子列表。
        :param request: 请求对象，通常包含用户信息和其他请求参数。
        :return: 包含切片文档的列表。如果文件夹中没有文件或切割失败，则返回空列表。
        """
        # 构建解析参数，包括文件夹路径
        parser_params = self.build_params(request)
        if index:
            parser_params['cache'] = [ch for ch in cache if ch['meta'].get('id') == int(index)]
        else:
            parser_params['cache'] = cache
        docs = self.split_from_cache(**parser_params)
        # 返回包含切片文档的列表
        docs = [doc for doc in docs if doc]  # 过滤掉空子列表
        # 返回包含切片文档的列表
        return docs
    # 更新缓存区
    def update_cache(self,folder,docs,result):
        """
        更新缓存中的数据。

        1. 生成两个缓存键，一个用于存储切片文档 (`split_cache_key`)，另一个用于存储编辑结果 (`edit_cache_key`)。
        2. 将切片文档 (`docs`) 存储到缓存中，并设置缓存的过期时间为1小时。
        3. 将编辑结果 (`result`) 存储到缓存中，并设置缓存的过期时间为1小时。
        
        :param folder: 文件夹的名称，用于生成缓存键。
        :param docs: 要缓存的切片文档列表。
        :param result: 要缓存的编辑结果列表。
        """
        split_cache_key = f'split_{folder}'
        edit_cache_key = f'edit_{folder}'
        cache.set(split_cache_key, docs, timeout=3600)  # 数据保留1小时
        
        cache.set(edit_cache_key, result, timeout=3600)
    
    #将Documents转成json返回给前端            
    def docs_to_json(self,docs):
        """
        将文档列表转换为 JSON 格式的字典列表。

        1. 遍历文档列表，将每个文档转换为包含元数据和数据的字典。
        2. 每个字典包含两个部分：
        - 'metadata'：文档的元数据，包括 'file_name'、'file_path' 和 'id'。
        - 'data'：文档的内容数据，包含每个页面的索引、大小和页面内容。
        
        :param docs: 文档列表，每个文档是一个列表，包含多个文档片段。
        :return: JSON 格式的字典列表，每个字典包含文档的元数据和数据。
        
        # docs = []
        # for item in data:
        #     metadata = item['metadata']
        #     t = item['data']
        #     document_list = []
        #     for page in t:
        #         doc_item =Document(page_content=page['page_content'],metadata = metadata)
        #         document_list.append(doc_item)
        #     docs.append(document_list)
        #['file_name','file_path','id']
        #if doc and hasattr(doc[0], 'metadata') else {},
        """
        return [{        
            'metadata' : {key:doc[0].metadata.get(key) for key in doc[0].metadata},
            'data' : [{
                'index':index,'size': len(doc_item.page_content),'page_content': doc_item.page_content
            } for index,doc_item in enumerate(doc)]
        } for doc in docs]

    #将前端数据转成Documents类型        
    def json_to_docs(self,data):
        """
        将 JSON 格式的数据转换为文档对象列表。

        1. 遍历 JSON 数据，将每个项目转换为包含多个文档对象的列表。
        2. 每个文档对象由页内容和元数据组成，来自于 JSON 数据中的 'data' 和 'metadata' 部分。

        :param data: JSON 格式的字典列表，每个字典包含文档的 'metadata' 和 'data'。
                    'data' 是包含页内容和其他信息的列表，'metadata' 是文档的元数据。
        :return: 文档对象的列表，每个文档对象包含页面内容和元数据。
        """
        return  [[DOC(page_content=page['page_content'], metadata=item['metadata']) for page in item['data']] for item in data]

    # 根据Id过滤,并返回当前数据和索引
    def filter_edit_data_by_id(self,edit_data, index):
        """
        根据 ID 过滤编辑数据，并返回包含索引和数据项的元组列表。

        1. 遍历 `edit_data` 列表，根据每个数据项的 'metadata' 中的 'id' 是否等于 `index` 进行过滤。
        2. 返回符合条件的每个数据项和其索引的元组列表。

        :param edit_data: 编辑数据的列表，每个数据项是一个包含 'metadata' 的字典。
        :param index: 用于过滤的 ID 值。
        :return: 包含元组的列表，每个元组包括数据项的索引和数据项本身。
        """
        return [(i, item) for i, item in enumerate(edit_data) if item['metadata'].get('id') == int(index)]

    def handle_split_folder(self,folder):
        docs=result=docs1=docs2=result1=result2 = ''
        
        # 从云盘导入
        try:
            data = self.import_cache(folder)  # 获取缓存数据
            # 从文件夹获取所有文件的切片数据
            if data:
                docs1 = self.cache_get_docs(self.request,data)
                result1 = self.docs_to_json(docs1)
        except Exception as e:
            logger.error(f'解析import_cache：{str(e)}')
            raise Exception(str(e))
        
        # 从文件夹中提取，构建新的文件路径
        target_folder = os.path.join('media', 'data', 'tempy', self.request.user.username, folder )
        if not os.path.exists(target_folder) :
            raise Exception('文件不存在请重新上传')
        
        try:
            # 从文件夹获取所有文件的切片数据
            docs2 = self.folder_get_docs(target_folder,self.request)
        except Exception as e:
            logger.error(f'解析target_folder：{str(e)}')
            raise Exception(str(e))
        DataFrame.objects.filter(cache_id= folder).update(sql_available=3)
        # 将Documents转成Json返回给前端
        result2 = self.docs_to_json(docs2)
        docs = (docs1 or []) + (docs2 or [])
        result = (result1 or []) + (result2 or [])
        # 更新缓存
        self.update_cache(folder,docs,result)
        return {'status': 'completed','cache_id':folder,'data':result}

    def handle_mysql(self, folder):
        upload_cache_key, split_cache_key, processed_cache_key = (f"{key}_{folder}" for key in ["upload", "split", "processed"])

        try:
            result = split_to_mysql.delay(folder)
            DataFrame.objects.filter(cache_id=folder).update(sql_available=1)

            cache.set(processed_cache_key, "true", timeout=3600)  # 设置缓存，有效期1小时
            return {"status": "completed", "task_id": result.id}  # 返回带有id的响应
        except Exception as e:
            logger.error(f"post milvus :{str(e)}")
            raise Exception("切片保存失败")

    def handle_milvus(self, sheet_id, cache_id, doc_id=None):
        split_cache_key = f"split_{cache_id}"
        processed_cache_key = f"processed_{cache_id}"

        if not cache.get(split_cache_key):
            raise Exception("没有可上传的数据")
        if doc_id:
            doc_id = ast.literal_eval(doc_id)
        try:
            is_processed = cache.get(processed_cache_key)
            if not is_processed:
                logger.warning(f"is_processed:{is_processed}")
                DataFrame.objects.filter(cache_id=cache_id).update(sql_available=2)

            DataFrame.objects.filter(cache_id=cache_id).update(available=3)
            database = os.environ.get("MILVUS_DATABASE")
            sector_collecte = os.environ.get("MILVUS_PREFIX") + str(sheet_id)
            result = insert_to_vector.delay(split_cache_key, database, sector_collecte, doc_id)  # taskid :{result.id}

            logger.info(f"save to milvus path :{database}/{sector_collecte} ")
            return {"status": "completed", "taskid": result.id}
        except Exception as e:
            logger.error(f"处理Milvus存储失败: {str(e)}")
            raise Exception("向量保存失败")

    def cleanup_tempy(self, folder_name: str, username: str):
        """第五步：清理临时文件"""
        import gc

        try:
            # 清理临时目录
            target_folder = os.path.join("media", "data", "tempy", username, folder_name)
            if os.path.exists(target_folder) and os.path.isdir(target_folder):
                logger.info(f"清理临时文件夹: {target_folder}")
                shutil.rmtree(target_folder)

            # 清理缓存
            cache_keys = [
                f"split_{folder_name}",
                f"processed_{folder_name}",
                f"upload_{folder_name}",
            ]
            for key in cache_keys:
                cache.delete(key)

            # 回收内存
            gc.collect()

            logger.info(f"临时文件清理完成: {folder_name}")

        except Exception as e:
            logger.error(f"清理临时文件失败: {str(e)}")
    def cleanup_prefrom(self, folder_name: str, username: str):
        print("清理临时文件",self.request.user.uuid_user)
        try:
            # 查询所有临时文件记录
            temp_files = DataFrame.objects.filter(
                cache_id=folder_name,
                uuid_user=self.request.user.uuid_user,
                is_temp=True
            )

            # 删除对应的文件
            for file_record in temp_files:
                file_path = os.path.join("media", "data", "raw", username, file_record.file_name)
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.info(f"已删除预创建的临时文件: {file_path}")

            # 删除数据库中的这些记录
            temp_files.delete()

            logger.info(f"临时文件数据库记录已清理，folder: {folder_name}, user: {username}")

        except Exception as e:
            logger.error(f"清理临时文件失败: {str(e)}")


class VBSplitsView(BaseView):
    # 解析并切片 文本 
    def post(self,request):
        folder = self.request.POST.get('target_folder')
        result_data = self.handle_split_folder(folder)
        return JsonResponse(mg(data=result_data))

class VBSplitOneView(BaseView):
    # 解析单个文件并切片成文本 
    def post(self,request):
        user = self.request.user        
        target_file = self.request.POST.get('target_file')          
        folder = self.request.POST.get('target_folder')  
        index = self.request.POST.get('id')  
        docs1=[]
        try:
            data = self.import_cache(folder)  # 获取缓存数据
            if data:
                # 从文件夹获取所有文件的切片数据
                docs1 = self.cache_get_docs(self.request,data,index)  
        except Exception as e:
            return JsonResponse(mg(code= 500 ,msg=str(e)),status=500)
        print('-'*30)
        # 构建新的文件路径        
        target_folder = os.path.join('media', 'data', 'extra', user.username, str(index),folder )
        # 是否传入文件名，构建文件全路径
        if target_file:
            target_folder = os.path.join('media', 'data', 'tempy', user.username,folder, target_file)
        
        if not os.path.exists(target_folder) :
            return JsonResponse(mg(code= 500 ,msg='文件不存在请重新上传'),status=500)
        try:
            # 获取文件切片数据        
            docs = self.folder_get_docs(target_folder,self.request,False)      
        except Exception as e:
            return JsonResponse(mg(code= 500 ,msg='{e}'),status=500)
        split_data = cache.get(f'split_{folder}')
        if split_data is None:
            return JsonResponse({'error': '缓存数据不存在或已过期'}, status=500)
        
        # 过滤掉旧数据中与新数据具有相同id的记录
        filter_documents = [[d for d in doc  if d.metadata.get('id') != int(index)] for doc in split_data   ]
        filter_documents = [doc for doc in filter_documents if doc]  # 过滤掉空子列表
        filter_documents.extend(docs)
        filter_documents.extend(docs1)
        # DataFrame.objects.filter(cache_id= folder).update(sql_available=3)
        result = self.docs_to_json(filter_documents)
        
        self.update_cache(folder,filter_documents,result)

        return JsonResponse(mg(data={'cache_id':folder,'data':result}))
    
class VBeditView(BaseView):
    def post(self,request,*args,**kwargs):
        result = json.loads(self.request.POST.get('result'))
        
        folder = result['cache_id']
        data = result['data']
        try:
            
            docs = self.json_to_docs(data)

            self.update_cache(folder,docs,data)
            cache.set(f'is_edit_{folder}', 'true', timeout=3600)  # 设置缓存，有效期1小时
            
            return JsonResponse(mg(msg='修改成功'))  # 返回带有id的响应
        except Exception as e:
            logger.error(f'post edit :{str(e)}')
            return JsonResponse(mg(code=400,msg=f'切片修改失败:{str(e)}'),status=500)

# 从缓存中获取数据，回到切片页面，实现重新上传
class VBSaveFromCacheView(APIView):
    def post(self,request,*args,**kwargs):
        folder = self.request.POST.get('cache_id')  
                  
        try:            
            result = cache.get(f'edit_{folder}')
            is_edit = cache.get(f'is_edit_{folder}')
            return JsonResponse(mg(data={'cache_id':folder,'data':result,'is_edit':is_edit}))  # 返回带有id的响应
        except Exception as e:
            logger.error(f'post save from cache :{str(e)}')
            return JsonResponse(mg(code=400,msg=f'重新上传失败:{str(e)}'),status=500)
        
class VBDeleteFromCacheView(BaseView):
    queryset = DataFrame.objects.all()
    serializer_class = FrameSerializer
    lookup_field = 'id'
    def post(self,request,*args,**kwargs):
        folder = self.request.POST.get('cache_id')  
        index = self.request.POST.get('id')  
        if not folder or not index:
            return JsonResponse({'error': '缺少必要的参数'}, status=500)
        try:            
            edit_data = cache.get(f'edit_{folder}')
            split_data = cache.get(f'split_{folder}')
            
            instance = self.queryset.filter(id=index).first()
            if instance is None:
                logger.warning(f'根据ID删除缓存中的数据:{index}')
                return JsonResponse({'error': '数据不存在'}, status=500)
            # 获取文件路径
            file_path = instance.file.path
            logger.info(f'delete:{file_path},{instance.file_name}')
            
            file_path = os.path.join('media', 'data', 'tempy', self.request.user.username, folder,instance.file_name )
    
            logger.info(f'delete:{file_path}')
            
            import_data = self.import_cache(folder,index)  # 获取缓存数据
            if import_data and import_data.get('delete',False):
                file_path = os.path.join('media', 'data', 'tempy', self.request.user.username, folder+'_'+'cloud',instance.file_name )
    
                logger.info(f'delete cloud file:{file_path}')
            # 删除文件
            if os.path.exists(file_path):
                os.remove(file_path)
            
             # 更新缓存数据
            if edit_data and split_data:  # 只有当两个缓存都存在时才处理
                del_edit_data, up_edit_data = self.separate_edit_data(edit_data, index)
                if del_edit_data:  # 确保找到了要删除的数据
                    docs = self.json_to_docs(up_edit_data)
                    self.update_cache(folder, docs, up_edit_data)
                else:
                    logger.warning(f'未找到ID为 {index} 的缓存数据')
                    up_edit_data = edit_data  # 如果没找到数据，保持原有数据不变
            else:
                logger.warning('缓存数据不存在')
                up_edit_data = []  # 如果缓存不存在，返回空列表
            
            
            return JsonResponse(mg(data={'cache_id':folder,'data':up_edit_data}))  # 返回带有id的响应
        except Exception as e:
            logger.error(f'post delete from cache :{str(e)}')
            return JsonResponse(mg(code=400,msg=f'根据ID删除缓存中的数据:{str(e)}'),status=500)
class VBSaveToFileFromCacheView(BaseView):
    def post(self,request,*args,**kwargs):
        folder = self.request.POST.get('cache_id')  
        index = self.request.POST.get('id')  
        if not folder or not index:
            return JsonResponse({'error': '缺少必要的参数'}, status=500)
        try:            
            edit_cache_key = f'edit_{folder}'
            edit_data = cache.get(edit_cache_key)
            
            if edit_data is None :
                return JsonResponse({'error': '缓存数据不存在或已过期'}, status=500)
            
            # 更新edit_cache_data
            del_edit_data ,up_edit_data = self.separate_edit_data(edit_data,index)
            
            # 假设原始文件路径中包含的文件名可能有任何类型的后缀
            file_name = del_edit_data[0]['metadata'].get('file_name')
            # 去除原有的后缀并添加.txt后缀
            base_name, _ = os.path.splitext(file_name)
            new_file_name = base_name + '.json'

            # 构建新的文件路径
            file_path = os.path.join('media', 'data', 'extra', self.request.user.username,  str(index), folder,new_file_name)
            logger.info(f'保存提取后的内容：{file_path}')
            # 处理被删除的数据，保存page_content，并在末尾添加两个换行符
            deleted_contents = ""
            for item in del_edit_data:
                for page in item['data']:
                    # 将每个page_content加上两个换行符并追加到字符串
                    deleted_contents += page['page_content'] + "\n\n"

            # 这里可以选择将deleted_contents保存到文件或进行其他处理
            # 例如保存到文件:
            directory = os.path.dirname(file_path)
            
            data={
                "meta":del_edit_data[0]['metadata'],
                "content":deleted_contents
            }

            # 创建所需的所有中间目录
            os.makedirs(directory, exist_ok=True)  # 如果目录已存在，不会抛出异常
            with open(file_path, "w",encoding='utf-8') as file:
                file.write(json.dumps(data, ensure_ascii=False, indent=4))
            
            return JsonResponse(mg(data={'file_path':file_path}))  # 返回带有id的响应
        except Exception as e:
            logger.error(f'post delete from cache :{str(e)}')
            return JsonResponse(mg(code=400,msg=f'保存提取的内容失败:{str(e)}'),status=500)
        
class VBMySQLView(BaseView):
    def post(self,request,*args,**kwargs):
        folder = self.request.POST.get('cache_id')
        
        try:
            result_data = self.handle_mysql(folder)
            return JsonResponse(mg(data=result_data))
        except Exception as e:
            logger.error(f'post mysql processing :{str(e)}')
            return JsonResponse(mg(code=400,msg=f'处理MySQL数据失败:{str(e)}'),status=200)
            
class VBMilvusView(BaseView):
    def post(self,request, *args, **kwargs):
        sheet_id = self.request.POST.get('id')        
        cache_id = self.request.POST.get('cache_id')
        doc_id = self.request.POST.get('doc_id')
        
        try:
            result = self.handle_milvus(sheet_id, cache_id, doc_id)
                
            return JsonResponse(mg(data=result))  # 返回带有id的响应
        except Exception as e:
            logger.error(f'post milvus :{str(e)}')
            return JsonResponse(mg(code=400,msg='向量保存失败'),status=500)
    
    def delete(self,request, *args, **kwargs):
        sheet_id = self.request.GET.get('id')        
        doc_id = self.request.GET.get('doc_id')
        
        if not doc_id:
            return JsonResponse(mg(code=400,msg=f"缺少必须参数，请传入删除的ID列表"),status=500)    
        database = os.environ.get('MILVUS_DATABASE')
        sector_collecte = os.environ.get('MILVUS_PREFIX')+str(sheet_id) 
        try:
            result = delete_from_vector.delay(ast.literal_eval(doc_id) ,database,sector_collecte)#taskid :{result.id}
                
            logger.info(f"delete from milvus path :{database}/{sector_collecte}/{doc_id}")
                
            return JsonResponse(mg(data={'task_id':result.id}))  # 返回带有id的响应
        except Exception as e:
            logger.error(f'post milvus :{str(e)}')
            return JsonResponse(mg(code=400,msg='向量删除失败'),status=500)    
        
from untils.vector.vbllm import VBllmBase
from django.http import StreamingHttpResponse
class VBllmView(BaseView,VBllmBase):

    def post(self,request, *args, **kwargs):        
        folder = self.request.POST.get('target_folder')
        if folder is None:
            # 处理 folder 为 None 的情况，可以返回一个错误响应或者赋予一个默认值
            return JsonResponse({'error': 'target_folder is required'}, status=400)
        
        # 构建新的文件路径        
        target_folder = os.path.join('media', 'data', 'tempy', self.request.user.username, folder )
        index = self.request.POST.get('id')    
        
        edit_cache_key = f'edit_{folder}'
        
        edit_data = cache.get(edit_cache_key)
        if edit_data is None :
                return JsonResponse({'error': '缓存数据不存在或已过期'}, status=500)
        
        up_edit_data = self.filter_edit_data_by_id(edit_data,index)
        target_file = target_folder + '/'+up_edit_data[0][1]['metadata'].get('file_name')
            
        logger.warning(f'大模型正在提取:{target_file}')
        if not os.path.exists(target_file) :
            return JsonResponse(mg(code= 500 ,msg='pdf文件不存在请重新上传'),status=500)        
        
        data = edit_data        
        
        # 将PDF转换为图像
        images = self.pdf_to_images(target_file)
        
        # # 方案一：流式输出
        return StreamingHttpResponse(streaming_content=self.process_with_gpt4o_streaming(images),content_type='text/event-stream')
        

class VBllmOneView(BaseView,VBllmBase):
    def post(self,request, *args, **kwargs):
        
        folder = self.request.POST.get('target_folder')
        # 构建新的文件路径        
        target_folder = os.path.join('media', 'data', 'tempy', self.request.user.username, folder )
        index = self.request.POST.get('id')          
        i = int(request.POST.get('index'))  # 从POST数据获取索引    
           
        edit_data = cache.get(f'edit_{folder}')
        if edit_data is None :
                return JsonResponse({'error': '缓存数据不存在或已过期'}, status=500)
        
        up_edit_data = self.filter_edit_data_by_id(edit_data,index)
        target_file = target_folder + '/'+up_edit_data[0][1]['metadata'].get('file_name')
            
        logger.warning(f'大模型正在按页提取:{target_file}')
        if not os.path.exists(target_file) :
            return JsonResponse(mg(code= 500 ,msg='pdf文件不存在请重新上传'),status=500)        
        # images_cached = cache.get(f'images_cached_{index}')
        # # 检查是否已缓存图像
        # if images_cached is None:
        #     # 将PDF转换为图像
        #     logger.warning('第一次提取')
        #     images_cached = self.pdf_to_images(target_file)  # 缓存图像
        #     logger.warning('已缓存')
        #     cache.set(f'images_cached_{index}', images_cached, timeout=3600)
        # # 使用GPT-4o处理图像
        # # processed_text = self.process_with_gpt4o([images_cached[i]])
        images_cached= self.pdf_to_images(target_file,[i])
        logger.info(images_cached)
        processed_text = self.process_image_server(images_cached[0],i)
        
        return JsonResponse(mg(data=processed_text))  # 返回带有id的响应
    
class VBllmTextView(BaseView,VBllmBase):

    def post(self,request, *args, **kwargs):
        folder = self.request.POST.get('target_folder')
        index = self.request.POST.get('id')          
        # 构建新的文件路径        
        target_folder = os.path.join('media', 'data', 'tempy', self.request.user.username, folder )   
        edit_data = cache.get(f'edit_{folder}')
        
        if not edit_data:
            return JsonResponse(mg(code= 500 ,msg='文件不存在请重新上传'),status=500)
        
        up_edit_data = self.filter_edit_data_by_id(edit_data,index)
        target_file = target_folder + '/'+up_edit_data[0][1]['metadata'].get('file_name')
            
        logger.warning(f'获取原文件的文本数据:{target_file}')
        if not os.path.exists(target_file) :
            return JsonResponse(mg(code= 500 ,msg='pdf文件不存在请重新上传'),status=500)        
        
        text = self.get_text_from_file(target_file)
        return JsonResponse(mg(data={'data':text}))  # 返回带有id的响应


import json
# from transformers import pipeline
class VBSearchView(APIView):
    
    permission_classes = [VerifyAPIkey]
    def doc_summarize(self,que,ans):
        pass
        # # 加载问答pipeline
        # qa_pipeline = pipeline("question-answering")
        # result = qa_pipeline(question=que, context=ans)
        # print('fffff',result)

        
    # 转换文档为 JSON
    def documents_to_json(self,documents):
        documents = sorted(documents, key=lambda doc: doc.metadata.get("score", 0), reverse=True)
        # 将文档对象列表转换为字典列表
        docs_dict = [{'page_content': doc.page_content,'metadata': doc.metadata} for doc in documents]
        
        # 将字典列表转换为 JSON 字符串
        # return json.dumps(docs_dict, ensure_ascii=False, indent=4)
         # 拼接所有文档的 page_content
        # all_page_contents = ' '.join(doc['page_content'] for doc in docs_dict)
        # docs_dict.append({'content':all_page_contents})
    
    # 返回字典列表和拼接后的 page_content 字符串
        return docs_dict,'all_page_contents'

    def post(self,request):
        text = request.data.get('text')
        db  = request.data.get('database')
        col = request.data.get('collection')
        top_k  = request.data.get('top_k',10)
        score_threshold = request.data.get('score_threshold',0.45)
        logger.info(f'{text},{db},{col},{top_k},{score_threshold}')
        vdb=VectorDB()
        ret = vdb.simple_retriever(db, col, top_k, score_threshold)
        res = ret.invoke(text)
        data ,content= self.documents_to_json(res)
        
        
        # self.doc_summarize(text,content){'data':data,'content':content}
        # logger.warning(content)
        return JsonResponse(mg(data=data))


class VBFileSearchView(APIView):
    """
    增强的文件检索API视图 - 支持多种优化策略
    """
    permission_classes = [VerifyAPIkey]

    def documents_to_json(self, documents):
        """转换文档为JSON格式，增加更多元数据"""
        documents = sorted(documents, key=lambda doc: doc.metadata.get("score", 0), reverse=True)

        docs_dict = []
        file_stats = {}

        for doc in documents:
            metadata = doc.metadata
            file_name = metadata.get('file_name', 'Unknown')

            # 统计文件分布
            if file_name not in file_stats:
                file_stats[file_name] = {'count': 0, 'avg_score': 0, 'scores': []}

            score = metadata.get('score', 0)
            file_stats[file_name]['count'] += 1
            file_stats[file_name]['scores'].append(score)

            # 增强元数据
            enhanced_metadata = {
                **metadata,
                'content_length': len(doc.page_content),
                'relevance_level': self._get_relevance_level(score),
                'file_type': self._get_file_type(file_name)
            }

            docs_dict.append({
                'page_content': doc.page_content,
                'metadata': enhanced_metadata
            })

        # 计算文件统计
        for file_name, stats in file_stats.items():
            stats['avg_score'] = sum(stats['scores']) / len(stats['scores'])

        return docs_dict, file_stats

    def _get_relevance_level(self, score):
        """根据分数确定相关性等级"""
        if score >= 0.8:
            return 'high'
        elif score >= 0.6:
            return 'medium'
        elif score >= 0.4:
            return 'low'
        else:
            return 'very_low'

    def _get_file_type(self, file_name):
        """获取文件类型"""
        if not file_name or '.' not in file_name:
            return 'unknown'
        return file_name.split('.')[-1].lower()

    def _build_smart_filter(self, selected_files, selected_file_ids, uid_sheet, query_text):
        """智能构建过滤表达式"""
        filter_conditions = []
        file_info = {}

        # 处理文件名过滤
        if selected_files:
            file_names_escaped = [name.replace("'", "\\'") for name in selected_files]
            file_names_str = "', '".join(file_names_escaped)
            filter_conditions.append(f"file_name in ['{file_names_str}']")

            # 记录文件信息
            for file_name in selected_files:
                file_info[file_name] = {'source': 'filename', 'type': self._get_file_type(file_name)}

        # 处理DataFrame ID过滤
        if selected_file_ids:
            from datasheet.models import DataFrame
            dataframes = DataFrame.objects.filter(id__in=selected_file_ids)
            if dataframes.exists():
                file_names = [df.file_name for df in dataframes]
                file_names_escaped = [name.replace("'", "\\'") for name in file_names]
                file_names_str = "', '".join(file_names_escaped)
                filter_conditions.append(f"file_name in ['{file_names_str}']")

                # 记录文件信息
                for df in dataframes:
                    file_info[df.file_name] = {
                        'source': 'dataframe_id',
                        'id': df.id,
                        'type': self._get_file_type(df.file_name),
                        'size': getattr(df, 'file_size', 0)
                    }

        # 智能知识库过滤
        if uid_sheet:
            # 可以根据知识库添加额外的上下文过滤
            pass

        # 组合过滤条件
        filter_expr = " and ".join(filter_conditions) if filter_conditions else None

        return filter_expr, file_info

    def _optimize_search_params(self, query_text, file_info, top_k, score_threshold):
        """根据查询和文件信息优化搜索参数"""
        optimized_params = {
            'top_k': top_k,
            'score_threshold': score_threshold
        }

        # 根据文件数量调整top_k
        file_count = len(file_info)
        if file_count == 1:
            # 单文件搜索，可以降低阈值，增加结果数
            optimized_params['score_threshold'] = max(0.3, score_threshold - 0.1)
            optimized_params['top_k'] = min(top_k * 2, 20)
        elif file_count > 5:
            # 多文件搜索，提高阈值，保证质量
            optimized_params['score_threshold'] = min(0.7, score_threshold + 0.1)

        # 根据查询长度调整
        query_length = len(query_text.split())
        if query_length <= 2:
            # 短查询，降低阈值
            optimized_params['score_threshold'] = max(0.3, optimized_params['score_threshold'] - 0.1)
        elif query_length >= 10:
            # 长查询，可以提高阈值
            optimized_params['score_threshold'] = min(0.8, optimized_params['score_threshold'] + 0.1)

        return optimized_params

    def post(self, request):
        """
        增强的文件检索接口

        新增功能:
        - 智能参数优化
        - 文件统计分析
        - 多策略检索
        - 结果质量评估
        """
        try:
            # 基础参数解析
            text = request.data.get('text')
            db = request.data.get('database', ['default'])
            col = request.data.get('collection', [])
            top_k = request.data.get('top_k', 50)  # 提高默认值，支持更多结果
            score_threshold = request.data.get('score_threshold', 0.45)

            # 文件过滤参数
            selected_files = request.data.get('selected_files', [])
            selected_file_ids = request.data.get('selected_file_ids', [])
            uid_sheet = request.data.get('uid_sheet')

            # 高级选项
            enable_smart_optimization = request.data.get('smart_optimization', True)
            enable_fallback = request.data.get('enable_fallback', True)

            if not text:
                return JsonResponse({'error': '搜索文本不能为空'}, status=400)

            logger.info(f'增强文件检索: {text}, files:{selected_files},files_ids:{selected_file_ids}, optimization:{enable_smart_optimization}')

            # 智能构建过滤表达式
            filter_expr, file_info = self._build_smart_filter(
                selected_files, selected_file_ids, uid_sheet, text
            )
            logger.info(f'过滤表达式: {filter_expr}')

            # 优化搜索参数
            if enable_smart_optimization:
                optimized_params = self._optimize_search_params(text, file_info, top_k, score_threshold)
                top_k = optimized_params['top_k']
                score_threshold = optimized_params['score_threshold']
                logger.info(f'参数优化: top_k={top_k}, threshold={score_threshold}')

            # 查询增强（可选）
            enable_query_enhancement = request.data.get('query_enhancement', True)
            search_strategy = request.data.get('search_strategy', 'adaptive')

            if enable_query_enhancement:
                try:
                    from untils.search.query_enhancer import QueryEnhancer
                    enhancer = QueryEnhancer()

                    # 构建上下文
                    context = {
                        'file_types': [self._get_file_type(f) for f in selected_files],
                        'file_count': len(file_info)
                    }

                    # 增强查询
                    enhanced_result = enhancer.enhance_query(text, context)
                    enhanced_queries = enhanced_result['enhanced_queries']

                    logger.info(f'查询增强: {text} -> {enhanced_queries}')

                    # 创建一次VectorDB实例，复用连接
                    vdb = VectorDB()

                    # 使用增强查询进行多轮检索
                    all_results = []
                    for i, enhanced_query in enumerate(enhanced_queries):
                        try:
                            # 复用VectorDB实例，使用快速检索器提升增强检索速度
                            if search_strategy == "fast" or not search_strategy:
                                ret = vdb.fast_retriever(
                                    db, col, top_k, score_threshold,
                                    filter_expr=filter_expr
                                )
                            elif search_strategy == "simple":
                                ret = vdb.simple_retriever(
                                    db, col, top_k, score_threshold
                                )
                            elif search_strategy == "precise":
                                ret = vdb.precise_retriever(
                                    db, col, top_k, score_threshold,
                                    filter_expr=filter_expr
                                )
                            else:
                                ret = vdb.retriever(
                                    db, col, top_k, score_threshold,
                                    filter_expr=filter_expr,
                                    search_strategy=search_strategy
                                )
                            query_results = ret.invoke(enhanced_query)

                            # 为结果添加查询来源标记
                            for result in query_results:
                                result.metadata['query_source'] = f'enhanced_{i}'
                                result.metadata['original_query'] = text
                                result.metadata['enhanced_query'] = enhanced_query

                            all_results.extend(query_results)

                        except Exception as e:
                            logger.warning(f'增强查询 {enhanced_query} 失败: {str(e)}')

                    # 去重和排序
                    res = self._deduplicate_and_rank(all_results, enhanced_result)

                except Exception as e:
                    logger.warning(f'查询增强失败，使用原始查询: {str(e)}')
                    # 回退到原始查询，复用或创建VectorDB实例
                    if 'vdb' not in locals():
                        vdb = VectorDB()
                    ret = vdb.retriever(db, col, top_k, score_threshold, filter_expr=filter_expr)
                    res = ret.invoke(text)
            else:
                # 执行原始检索 - 使用快速检索器提升响应速度
                vdb = VectorDB()
                if search_strategy == "fast" or not search_strategy:
                    # 使用快速检索器，响应速度最快
                    ret = vdb.fast_retriever(
                        db, col, top_k, score_threshold,
                        filter_expr=filter_expr
                    )
                elif search_strategy == "simple":
                    # 使用简单检索器，无额外判断
                    ret = vdb.simple_retriever(
                        db, col, top_k, score_threshold
                    )
                elif search_strategy == "precise":
                    # 使用精确检索器，高质量结果
                    ret = vdb.precise_retriever(
                        db, col, top_k, score_threshold,
                        filter_expr=filter_expr
                    )
                else:
                    # 使用增强检索器，支持多种策略
                    ret = vdb.retriever(
                        db, col, top_k, score_threshold,
                        filter_expr=filter_expr,
                        search_strategy=search_strategy
                    )
                res = ret.invoke(text)

            # 如果结果不足且启用回退策略
            if len(res) < 3 and enable_fallback and filter_expr:
                logger.info('结果不足，启用回退策略')
                # 降低阈值重试
                fallback_threshold = max(0.2, score_threshold - 0.2)
                fallback_ret = vdb.retriever(db, col, top_k * 2, fallback_threshold, filter_expr=filter_expr)
                fallback_res = fallback_ret.invoke(text)

                if len(fallback_res) > len(res):
                    res = fallback_res
                    logger.info(f'回退策略生效，获得{len(res)}个结果')

            # 处理结果
            data, file_stats = self.documents_to_json(res)

            # 构建增强响应
            response_data = {
                'results': data,
                'query': text,
                'filter_expr': filter_expr,
                'total_results': len(data),
                'file_info': file_info,
                'file_stats': file_stats,
                'search_params': {
                    'top_k': top_k,
                    'score_threshold': score_threshold,
                    'optimized': enable_smart_optimization
                },
                'quality_metrics': self._calculate_quality_metrics(data)
            }

            return JsonResponse(mg(data=response_data))

        except Exception as e:
            logger.error(f'增强文件检索错误: {str(e)}')
            return JsonResponse({'error': str(e)}, status=500)

    def _calculate_quality_metrics(self, results):
        """计算结果质量指标"""
        if not results:
            return {'avg_score': 0, 'score_distribution': {}, 'content_diversity': 0}

        scores = [r.get('metadata', {}).get('score', 0) for r in results]
        avg_score = sum(scores) / len(scores)

        # 分数分布
        score_distribution = {'high': 0, 'medium': 0, 'low': 0, 'very_low': 0}
        for score in scores:
            if score >= 0.8:
                score_distribution['high'] += 1
            elif score >= 0.6:
                score_distribution['medium'] += 1
            elif score >= 0.4:
                score_distribution['low'] += 1
            else:
                score_distribution['very_low'] += 1

        # 内容多样性（不同文件的数量）
        unique_files = set()
        for result in results:
            file_name = result.get('metadata', {}).get('file_name')
            if file_name:
                unique_files.add(file_name)

        return {
            'avg_score': round(avg_score, 3),
            'score_distribution': score_distribution,
            'content_diversity': len(unique_files),
            'total_results': len(results)
        }

    def _deduplicate_and_rank(self, all_results, enhanced_result):
        """去重和重新排序结果"""
        if not all_results:
            return []

        # 基于内容去重
        seen_content = set()
        unique_results = []

        for result in all_results:
            # 使用内容的前100个字符作为去重标识
            content_key = result.page_content[:100]
            if content_key not in seen_content:
                seen_content.add(content_key)
                unique_results.append(result)

        # 重新排序：综合考虑原始分数、查询匹配度、内容质量
        def calculate_final_score(result):
            base_score = result.metadata.get('score', 0)

            # 查询来源权重
            query_source = result.metadata.get('query_source', 'enhanced_0')
            if query_source == 'enhanced_0':  # 原始查询
                source_weight = 1.0
            elif query_source == 'enhanced_1':  # 第一个增强查询
                source_weight = 0.9
            else:  # 其他增强查询
                source_weight = 0.8

            # 内容长度权重（适中长度的内容更有价值）
            content_length = len(result.page_content)
            if 100 <= content_length <= 500:
                length_weight = 1.0
            elif 50 <= content_length < 100 or 500 < content_length <= 1000:
                length_weight = 0.9
            else:
                length_weight = 0.8

            # 综合分数
            final_score = base_score * source_weight * length_weight
            return final_score

        # 按综合分数排序
        unique_results.sort(key=calculate_final_score, reverse=True)

        return unique_results


class VBFullTextSearchView(APIView):
    """
    通用全文检索视图 - 完全通用的文档内容分析

    功能特点:
    - 不依赖特定领域的硬编码规则
    - 自动识别文档中的关键主题和实体
    - 智能聚类和结构化输出
    - 适用于任何类型的文档分析
    """
    permission_classes = [VerifyAPIkey]

    def post(self, request):
        """
        通用全文检索接口

        请求参数:
        - text: 搜索文本/主题（必需）
        - selected_files: 目标文件列表（必需）
        - analysis_type: 分析类型，默认'general'
        - batch_size: 每批检索数量，默认50
        - min_score: 最低相关度分数，默认0.2
        - max_results: 最大结果数量，默认300
        - enable_deep_analysis: 是否启用深度分析，默认True
        """
        try:
            # 基础参数验证
            text = request.data.get('text')
            selected_files = request.data.get('selected_files', ['道路交通.txt'])

            if not text:
                return JsonResponse({'error': '搜索文本不能为空'}, status=400)
            if not selected_files:
                return JsonResponse({'error': '请至少选择一个文件'}, status=400)

            # 检索参数
            db = request.data.get('database', ['default'])
            col = request.data.get('collection', ['chzngk228'])
            analysis_type = request.data.get('analysis_type', 'general')
            batch_size = request.data.get('batch_size', 50)
            min_score = request.data.get('min_score', 0.4)
            max_results = request.data.get('max_results', 1000)  # 大幅提高默认值，支持真正的全文检索
            enable_deep_analysis = request.data.get('enable_deep_analysis', True)

            logger.info(f'通用全文检索: 查询="{text}", 文件={selected_files}, 类型={analysis_type}')

            # 执行全文检索
            all_documents = self._perform_comprehensive_search(
                text, selected_files, db, col, batch_size, min_score, max_results
            )

            if not all_documents:
                return JsonResponse(mg(data={
                    'query': text,
                    'message': '未找到相关内容',
                    'total_segments': 0,
                    'suggestions': [
                        '尝试降低min_score参数',
                        '检查文件名是否正确',
                        '使用更通用的搜索词'
                    ]
                }))

            # 根据深度分析设置选择分析方法
            if enable_deep_analysis:
                # 深度分析：使用通用分析器，包含实体识别、聚类、结构化总结
                analysis_result = self._analyze_with_universal_analyzer(
                    all_documents, text, analysis_type
                )
            else:
                # 基础分析：只做简单统计和内容预览，速度更快
                analysis_result = self._basic_analysis_only(all_documents, text)

            return JsonResponse(mg(data={
                'query': text,
                'analysis_type': analysis_type,
                'total_segments': len(all_documents),
                'files_analyzed': selected_files,
                'search_params': {
                    'batch_size': batch_size,
                    'min_score': min_score,
                    'max_results': max_results,
                    'deep_analysis': enable_deep_analysis
                },
                **analysis_result
            }))

        except Exception as e:
            logger.error(f'通用全文检索错误: {str(e)}')
            return JsonResponse({'error': f'检索失败: {str(e)}'}, status=500)

    def _perform_full_text_search(self, text, selected_files, db, col, batch_size, min_score, max_results):
        """执行全文检索，获取所有相关片段"""
        all_results = []
        current_offset = 0

        # 构建文件过滤表达式
        file_names_escaped = [name.replace("'", "\\'") for name in selected_files]
        file_names_str = "', '".join(file_names_escaped)
        filter_expr = f"file_name in ['{file_names_str}']"

        logger.info(f'开始全文检索，过滤表达式: {filter_expr}')

        # 创建一次VectorDB实例，复用连接
        vdb = VectorDB()

        # 分批检索，直到获取足够的结果或没有更多结果
        while len(all_results) < max_results:
            try:
                # 复用VectorDB实例，只创建新的retriever
                ret = vdb.retriever(
                    db, col,
                    top_k=batch_size,
                    score_threshold=min_score,
                    filter_expr=filter_expr
                )

                batch_results = ret.invoke(text)

                if not batch_results:
                    logger.info(f'第{current_offset//batch_size + 1}批检索无结果，停止检索')
                    break

                # 过滤重复内容
                new_results = self._filter_duplicates(batch_results, all_results)
                all_results.extend(new_results)

                logger.info(f'第{current_offset//batch_size + 1}批: 获得{len(batch_results)}个结果，新增{len(new_results)}个')

                # 如果新结果很少，说明已经检索得差不多了
                if len(new_results) < batch_size * 0.1:
                    logger.info('新结果数量较少，停止检索')
                    break

                current_offset += batch_size

                # 防止无限循环
                if current_offset > max_results * 2:
                    break

            except Exception as e:
                logger.warning(f'批次检索失败: {str(e)}')
                break

        logger.info(f'全文检索完成，共获得{len(all_results)}个相关片段')
        return all_results[:max_results]

    def _filter_duplicates(self, new_results, existing_results):
        """过滤重复内容"""
        if not existing_results:
            return new_results

        existing_contents = set()
        for result in existing_results:
            # 使用内容的前100个字符作为去重标识
            content_key = result.page_content[:100].strip()
            existing_contents.add(content_key)

        filtered_results = []
        for result in new_results:
            content_key = result.page_content[:100].strip()
            if content_key not in existing_contents:
                filtered_results.append(result)
                existing_contents.add(content_key)

        return filtered_results

    def _process_full_text_results(self, all_results, analysis_type, enable_clustering):
        """处理全文检索结果"""
        if not all_results:
            return {'clusters': [], 'summary': '未找到相关内容'}

        processed = {
            'total_segments': len(all_results),
            'avg_score': sum(r.metadata.get('score', 0) for r in all_results) / len(all_results),
            'score_distribution': self._analyze_score_distribution(all_results),
            'content_analysis': self._analyze_content_themes(all_results, analysis_type)
        }

        if enable_clustering:
            processed['clusters'] = self._cluster_by_themes(all_results, analysis_type)

        return processed

    def _analyze_score_distribution(self, results):
        """分析分数分布"""
        scores = [r.metadata.get('score', 0) for r in results]
        return {
            'min_score': min(scores),
            'max_score': max(scores),
            'avg_score': sum(scores) / len(scores),
            'high_quality_count': sum(1 for s in scores if s >= 0.7),
            'medium_quality_count': sum(1 for s in scores if 0.4 <= s < 0.7),
            'low_quality_count': sum(1 for s in scores if s < 0.4)
        }

    def _generate_structured_output(self, processed_results, query, analysis_type):
        """生成结构化输出"""
        if analysis_type == 'summary':
            return self._generate_battle_summary(processed_results, query)
        elif analysis_type == 'extract':
            return self._generate_character_analysis(processed_results, query)
        else:
            return self._generate_general_summary(processed_results, query)

    def _generate_battle_summary(self, processed_results, query):
        """生成战役总结"""
        clusters = processed_results.get('clusters', [])
        content_analysis = processed_results.get('content_analysis', {})

        summary = {
            'title': f'《三国演义》{query}相关内容总结',
            'overview': {
                'total_segments': processed_results.get('total_segments', 0),
                'main_themes_count': len(content_analysis.get('main_themes', [])),
                'clusters_count': len(clusters)
            },
            'main_battles_and_stories': [],
            'key_themes': content_analysis.get('main_themes', [])[:10],
            'detailed_clusters': clusters[:5]  # 前5个最重要的聚类
        }

        # 提取主要战役和典故
        for cluster in clusters:
            if cluster['count'] >= 2:  # 至少有2个片段的主题
                summary['main_battles_and_stories'].append({
                    'name': cluster['theme'],
                    'segment_count': cluster['count'],
                    'relevance_score': round(cluster['avg_score'], 3),
                    'content_preview': cluster['segments'][0]['content'][:200] + '...'
                })

        return summary

    def _perform_comprehensive_search(self, text, selected_files, db, col, batch_size, min_score, max_results):
        """执行真正的全文检索 - 分批获取所有相关片段"""
        all_results = []

        # 构建文件过滤表达式
        file_names_escaped = [name.replace("'", "\\'") for name in selected_files]
        file_names_str = "', '".join(file_names_escaped)
        filter_expr = f"file_name in ['{file_names_str}']"

        logger.info(f'开始真正的全文检索，过滤表达式: {filter_expr}')

        # 创建一次VectorDB实例，复用连接
        vdb = VectorDB()

        # 分批检索策略：逐步降低阈值，获取更多结果
        current_threshold = min_score
        seen_content = set()
        round_count = 0

        while len(all_results) < max_results and current_threshold >= 0.1 and round_count < 10:
            round_count += 1

            try:
                logger.info(f'第{round_count}轮检索: threshold={current_threshold:.3f}, 已获得{len(all_results)}个结果')

                # 复用VectorDB实例，只创建新的retriever
                ret = vdb.retriever(
                    db, col,
                    top_k=batch_size,
                    score_threshold=current_threshold,
                    filter_expr=filter_expr
                )

                batch_results = ret.invoke(text)

                # 去重并添加新结果
                new_results_count = 0
                for result in batch_results:
                    content_key = result.page_content[:100].strip()
                    if content_key not in seen_content:
                        seen_content.add(content_key)
                        all_results.append(result)
                        new_results_count += 1

                        if len(all_results) >= max_results:
                            break

                logger.info(f'第{round_count}轮获得{len(batch_results)}个结果，新增{new_results_count}个，累计{len(all_results)}个')

                # 如果这轮没有新结果，降低阈值继续
                if new_results_count == 0:
                    current_threshold = max(0.1, current_threshold - 0.1)
                else:
                    # 有新结果，稍微降低阈值
                    current_threshold = max(0.1, current_threshold - 0.05)

            except Exception as e:
                logger.warning(f'第{round_count}轮检索失败: {str(e)}')
                current_threshold = max(0.1, current_threshold - 0.1)
                continue

        # 如果还没达到目标数量，尝试使用更宽松的策略
        if len(all_results) < max_results // 2:
            logger.info('结果数量不足，尝试扩展查询策略')
            expanded_results = self._expand_search_with_keywords(
                text, selected_files, db, col, batch_size, max_results - len(all_results), seen_content
            )
            all_results.extend(expanded_results)

        logger.info(f'全文检索完成，共获得{len(all_results)}个文档片段')
        return all_results

    def _expand_search_with_keywords(self, text, selected_files, db, col, batch_size, remaining_count, seen_content):
        """使用关键词扩展搜索"""
        expanded_results = []

        try:
            # 提取查询中的关键词
            try:
                import jieba
                keywords = jieba.lcut(text)
                keywords = [kw for kw in keywords if len(kw) > 1]
            except ImportError:
                # 如果jieba不可用，使用简单的空格分割
                keywords = [kw.strip() for kw in text.split() if len(kw.strip()) > 1]

            # 构建文件过滤表达式
            file_names_escaped = [name.replace("'", "\\'") for name in selected_files]
            file_names_str = "', '".join(file_names_escaped)
            filter_expr = f"file_name in ['{file_names_str}']"

            # 创建一次VectorDB实例，复用连接
            vdb = VectorDB()

            # 使用单个关键词进行检索
            for keyword in keywords[:10]:  # 使用更多关键词进行扩展检索
                if len(expanded_results) >= remaining_count:
                    break

                try:
                    # 复用VectorDB实例，只创建新的retriever
                    ret = vdb.retriever(
                        db, col,
                        top_k=batch_size // 2,
                        score_threshold=0.15,  # 使用更低的阈值
                        filter_expr=filter_expr
                    )

                    keyword_results = ret.invoke(keyword)

                    # 去重并添加
                    for result in keyword_results:
                        content_key = result.page_content[:100].strip()
                        if content_key not in seen_content:
                            seen_content.add(content_key)
                            expanded_results.append(result)

                            if len(expanded_results) >= remaining_count:
                                break

                except Exception as e:
                    logger.warning(f'关键词"{keyword}"扩展检索失败: {str(e)}')
                    continue

            logger.info(f'关键词扩展检索获得{len(expanded_results)}个额外结果')

        except Exception as e:
            logger.warning(f'关键词扩展检索失败: {str(e)}')

        return expanded_results

    def _analyze_with_universal_analyzer(self, documents, query, analysis_type):
        """使用通用分析器进行深度分析"""
        try:
            from untils.search.universal_analyzer import UniversalDocumentAnalyzer

            analyzer = UniversalDocumentAnalyzer()
            analysis_result = analyzer.analyze_documents(documents, query, analysis_type)

            return {
                'analysis_method': 'universal_analyzer',
                'total_segments': len(documents),
                'basic_statistics': analysis_result['basic_stats'],
                'key_themes': analysis_result['keywords'],
                'entities': analysis_result['entities'],
                'content_clusters': analysis_result['clusters'],
                'structured_summary': analysis_result['structured_output'],
                'note': '深度分析模式：包含实体识别、主题聚类、结构化总结等高级功能'
            }

        except ImportError as e:
            logger.warning(f'通用分析器模块不可用: {str(e)}')
            # 如果模块不可用，提供一个简化的深度分析
            return self._fallback_deep_analysis(documents, query, analysis_type)

        except Exception as e:
            logger.warning(f'通用分析器运行失败: {str(e)}')
            # 如果运行失败，提供一个简化的深度分析
            return self._fallback_deep_analysis(documents, query, analysis_type)

    def _fallback_deep_analysis(self, documents, query, analysis_type):
        """备用深度分析 - 当通用分析器不可用时使用"""
        if not documents:
            return {'analysis_method': 'fallback_deep', 'message': '无文档可分析'}

        # 基础统计
        total_chars = sum(len(doc.page_content) for doc in documents)
        scores = [doc.metadata.get('score', 0) for doc in documents]
        avg_score = sum(scores) / len(scores) if scores else 0

        # 简单的关键词提取（基于词频）
        all_content = ' '.join([doc.page_content for doc in documents])

        # 使用简单的词频分析
        try:
            import jieba
            words = jieba.lcut(all_content)
        except ImportError:
            words = all_content.split()

        # 过滤和统计
        word_freq = {}
        for word in words:
            if len(word) > 1 and word not in ['的', '了', '在', '是', '有', '和', '与', '或']:
                word_freq[word] = word_freq.get(word, 0) + 1

        # 提取关键词
        key_themes = [
            {'word': word, 'frequency': freq, 'weight': freq / len(words)}
            for word, freq in sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:20]
        ]

        # 简单的实体识别（基于模式匹配）
        entities = self._simple_entity_extraction(all_content, analysis_type)

        # 内容预览
        content_previews = []
        for i, doc in enumerate(documents[:10]):
            content_previews.append({
                'index': i + 1,
                'content': doc.page_content[:300] + '...' if len(doc.page_content) > 300 else doc.page_content,
                'score': doc.metadata.get('score', 0),
                'file_name': doc.metadata.get('file_name', 'unknown')
            })

        return {
            'analysis_method': 'fallback_deep',
            'total_segments': len(documents),
            'basic_statistics': {
                'total_documents': len(documents),
                'total_characters': total_chars,
                'avg_score': round(avg_score, 3)
            },
            'key_themes': key_themes,
            'entities': entities,
            'content_previews': content_previews,
            'note': '备用深度分析模式：提供基础的关键词提取和实体识别功能'
        }

    def _simple_entity_extraction(self, content, analysis_type):
        """简单的实体提取"""
        import re

        entities = {
            'person': [],
            'place': [],
            'organization': []
        }

        # 基础的中文实体模式
        patterns = {
            'person': [
                r'[\u4e00-\u9fa5]{2,3}(?:先生|女士|老师|教授|博士|主任|经理|总监)',
                r'[\u4e00-\u9fa5]{2,4}(?:说|道|表示|认为|指出|强调)'
            ],
            'place': [
                r'[\u4e00-\u9fa5]{2,6}(?:市|省|县|区|镇|村|路|街|大学|学院)',
                r'[\u4e00-\u9fa5]{2,8}(?:医院|银行|公司|集团)'
            ],
            'organization': [
                r'[\u4e00-\u9fa5]{2,10}(?:公司|集团|组织|机构|部门|委员会)'
            ]
        }

        # 法律特定模式
        if analysis_type == 'legal':
            patterns['legal_article'] = [r'第[\d一二三四五六七八九十]+条']
            patterns['penalty'] = [r'罚款[\d]+元', r'扣[\d]+分']

        # 技术特定模式
        elif analysis_type == 'technical':
            patterns['api'] = [r'[A-Z][a-zA-Z]*API', r'[a-z_]+\(\)']
            patterns['technology'] = [r'[A-Z]{2,}', r'[a-zA-Z]+\.[a-zA-Z]+']

        # 提取实体
        for entity_type, pattern_list in patterns.items():
            found_entities = set()
            for pattern in pattern_list:
                matches = re.findall(pattern, content)
                found_entities.update(matches)

            entities[entity_type] = [
                {'text': entity, 'frequency': content.count(entity)}
                for entity in list(found_entities)[:10]
            ]

        return entities

    def _basic_analysis_only(self, documents, query):
        """纯基础分析 - 只做统计，不做深度分析"""
        if not documents:
            return {'analysis_method': 'basic_only', 'message': '无文档可分析'}

        # 基础统计
        total_chars = sum(len(doc.page_content) for doc in documents)
        scores = [doc.metadata.get('score', 0) for doc in documents]
        avg_score = sum(scores) / len(scores) if scores else 0

        # 简单的内容预览（只显示前10个）
        content_previews = []
        for i, doc in enumerate(documents[:10]):
            content_previews.append({
                'index': i + 1,
                'content': doc.page_content[:200] + '...' if len(doc.page_content) > 200 else doc.page_content,
                'score': doc.metadata.get('score', 0),
                'file_name': doc.metadata.get('file_name', 'unknown')
            })

        return {
            'analysis_method': 'basic_only',
            'total_segments': len(documents),
            'basic_statistics': {
                'total_documents': len(documents),
                'total_characters': total_chars,
                'avg_score': round(avg_score, 3),
                'score_range': {
                    'min': round(min(scores), 3) if scores else 0,
                    'max': round(max(scores), 3) if scores else 0
                }
            },
            'content_previews': content_previews,
            'note': '基础分析模式：仅提供统计信息和内容预览，不包含实体识别、主题聚类等深度分析功能'
        }

    def _basic_analysis(self, documents, query):
        """基础分析（备用方案）- 当深度分析失败时使用"""
        if not documents:
            return {'analysis_method': 'basic_fallback', 'message': '无文档可分析'}

        # 基础统计
        total_chars = sum(len(doc.page_content) for doc in documents)
        scores = [doc.metadata.get('score', 0) for doc in documents]
        avg_score = sum(scores) / len(scores) if scores else 0

        # 简单的内容预览
        content_previews = []
        for i, doc in enumerate(documents[:5]):
            content_previews.append({
                'index': i + 1,
                'preview': doc.page_content[:200] + '...',
                'score': doc.metadata.get('score', 0),
                'length': len(doc.page_content)
            })

        return {
            'analysis_method': 'basic',
            'basic_statistics': {
                'total_documents': len(documents),
                'total_characters': total_chars,
                'average_score': round(avg_score, 3),
                'score_range': [min(scores), max(scores)] if scores else [0, 0]
            },
            'content_previews': content_previews,
            'summary': {
                'title': f'关于「{query}」的基础分析结果',
                'description': f'找到{len(documents)}个相关文档片段，平均相关度{avg_score:.3f}',
                'recommendation': '建议启用深度分析以获得更详细的主题和实体信息'
            }
        }


class LawView(APIView):
    def get(self,request,*args,**kwargs):
        data = DataFrame.objects.filter(sector='法律').order_by('-create_time')[:20]
        ser = FrameSerializer(data,many=True)
        return JsonResponse(mg(data=ser.data))


class LawSearchView(PaginationMixin,    Mixin):
    parser_classes=(MultiPartParser,FormParser)
    queryset = DataFrame.objects.all()
    serializer_class = FrameSerializer
       
    def searchlist(self,request,*args,**kwargs):
        keyword,sector=self.get_request_params(request,'keyword','sector')
        user = self.request.user
        sector_name = {
            'educations': '教育',
            'laws': '法律',
        }
        # print(user.uuid_company.company_name,sector_name.get(sector))
        data = DataFrame.objects.filter(
            (Q(file_name__icontains=keyword)| Q(desc__icontains=keyword))&
            # Q(sector = sector_name.get(sector))
            (Q(sector = user.uuid_company.company_name) | Q(sector = sector_name.get(sector))  |
            Q(uuid_user = self.request.user))
            )

        return self.get_paginated_response(data,self.serializer_class)

class SheetByDataFrameView(APIView):
    def get(self, request, fid,format=None):
        try:
            dataframe_instance = DataFrame.objects.get(id=fid)
            # Access the related DataSheet instance
            datasheet_instance = dataframe_instance.uid_sheet

            if datasheet_instance:
                serializer = SheetSerializer(datasheet_instance)
                return JsonResponse(mg(data=serializer.data))
            else:
                return Response({"detail": "No associated knowledge base found for this DataFrame."},
                                status=status.HTTP_404_NOT_FOUND)
        except DataFrame.DoesNotExist:
            return Response({"detail": "DataFrame not found."},
                            status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


from django.db.models import Count
class SegmentsView(Mixin):
    """
    获取同一时间上传的文件个数及每个文件的片段数
    """
    queryset = Page.objects.all()

    def get1(self, request, *args, **kwargs):
        
        folder_id = kwargs.get('folder_id')
        print  (folder_id)
        # 根据 folder_id 查询相关的文件
        pages = Page.objects.filter(folder_id=folder_id)

        if not pages.exists():
            return Response({"message": "未找到相关文件"}, status=400)

        # 获取文件个数
        file_count = pages.values("file_name").distinct().count()

        # 统计每个文件的片段数
        # 统计每个文件的片段数，并获取每片的内容
        file_segments = (
            pages.values("file_name", "uid_frame")
            .annotate(segment_count=Count("id"))
            .order_by("uid_frame")
        )

        # 构造返回数据，包括每片的内容
        data = {
            "file_count": file_count,
            "file_segments": []
        }

        for item in file_segments:
            # 获取当前文件的所有片段内容
            segments = pages.filter(uid_frame=item["uid_frame"]).values("id", "page_content")
            data["file_segments"].append({
                "file_name": item["file_name"],
                "segment_count": item["segment_count"],
                "segments": list(segments)  # 每片的内容
            })

        return JsonResponse(data)

    def segments(self, request, *args, **kwargs):
        uid_frame = kwargs.get('uid_frame')  # 从 URL 参数中获取 uid_frame

        if not uid_frame:
            return JsonResponse({"message": "缺少必要的参数 uid_frame"}, status=400)

        # 根据 uid_frame 查询相关的文件片段
        pages = Page.objects.filter(uid_frame=uid_frame)

        if not pages.exists():
            return JsonResponse({"message": "未找到相关文件"}, status=400)

        # 获取文件名
        file_name = pages.first().file_name

        # 获取片段数
        segment_count = pages.count()

        # 获取每片的内容
        segments = pages.values("id", "page_content")

        # 构造返回数据
        data = {
            "file_name": file_name,
            "segment_count": segment_count,
            "segments": list(segments),  # 每片的内容
        }

        return JsonResponse(data, status=200)
    
from llama_index.core import Document, VectorStoreIndex, ServiceContext,load_index_from_storage
from llama_index.core.storage.storage_context import StorageContext
from llama_index.core.vector_stores import SimpleVectorStore
from llama_index.llms.openai import OpenAI
# 设置OpenAI API密钥
import os,openai
openai.api_key = os.getenv("OPENAI_API_KEY")
openai.base_url=os.getenv("OPENAI_BASE_URL")
# os.environ["OPENAI_API_KEY"] = "sk-BJbWEN6FcYL32olwC40367A6D1Ee4b51A7Ae87CcFa2c6392"
# os.environ["OPENAI_API_BASE_URL"] = "https://api.xi-ai.cn/v1"
class LlamaindexView:
    def __init__(self):
        self._index = None
        self.index_path = 'media/data/index'
        # self.llm = OpenAI(model="gpt-4o-2024-05-13")
    def create_documents_from_chunks(self):
        
        """从数据库中的文本切片创建Document对象"""
        chunks = Page.objects.all()
        documents = []
        
        for chunk in chunks:
            doc = Document(
                text=chunk.page_content,
                metadata={
                    'id':chunk.id,
                    'filename' :chunk.file_name,
                    'filepath':chunk.file_path
                }
            )
            documents.append(doc)
        
        return documents  
    def get_local_index(self):
        # 检查是否存在持久化的索引
        if os.path.exists(self.index_path):
            # 从本地加载索引
            storage_context = StorageContext.from_defaults(persist_dir=self.index_path)
            self._index = load_index_from_storage(storage_context)
            return self._index
        
    def save_index(self,index):
        os.makedirs(self.index_path, exist_ok=True)
        index.storage_context.persist(persist_dir=self.index_path)
    def create_or_update_index(self):
        """创建或更新向量索引"""
        if self._index is not None:
            return self._index
        local_index = self.get_local_index()
        if local_index is not None:
            self._index = local_index
            return self._index
        documents = self.create_documents_from_chunks()
        # 创建索引
        self._index = VectorStoreIndex.from_documents(documents)
        self.save_index(self._index)
        return self._index
    
    def search(self, query_text, top_k=10):
        """执行搜索"""
        index = self.create_or_update_index()
        query_engine = index.as_query_engine(
            similarity_top_k=top_k
        )
        
        response = query_engine.query(query_text)
        # 从响应中提取源文档信息
        source_nodes = response.source_nodes
        results = []
        
        for node in source_nodes:
            doc_id = node.metadata.get('id')
            file_name = node.metadata.get('filename')
            
            # 获取原始文档片段
            chunk = Page.objects.get(
                id=doc_id,
                file_name=file_name
            )
            results.append({
                'page_content': chunk.page_content,
                'metadata':{
                    'file_name': chunk.file_name,
                    'file_path': chunk.file_path,
                    'id': doc_id,
                    },
                'score': node.score if hasattr(node, 'score') else None
            })
        return {
                'query': query_text,
                'results': results,
                'response': str(response)
            } 
    
class LlamaindexSearchView(APIView):
    def get(self,request,*args,**kwargs):
        query = request.GET.get('query', '')
        top_k = int(request.GET.get('top_k', 20))
        
        if not query:
            return JsonResponse({
                'error': 'Query parameter is required'
            }, status=400)
        
        service = LlamaindexView()
        
        results = service.search(query, top_k=top_k)        

        return JsonResponse(results,safe=False)
        
    
class KnowledgeSearchView(PaginationMixin,    Mixin):
    parser_classes=(MultiPartParser,FormParser)
    queryset = DataFrame.objects.all()
    serializer_class = FrameSerializer
       
    def searchlist(self,request,*args,**kwargs):
        keyword,sheet_id=self.get_request_params(request,'keyword','sheet_id')
        user = self.request.user

        # print(user.uuid_company.company_name,sector_name.get(sector))
        data = DataFrame.objects.filter(
            (Q(file_name__icontains=keyword)| Q(desc__icontains=keyword))&
            # Q(sector = sector_name.get(sector))
            # (Q(sector = user.uuid_company.company_name) | Q(sector = sector_name.get(sector))  |
            Q(uid_sheet = sheet_id)
            )

        return self.get_paginated_response(data,self.serializer_class)
    
    # def get_queryset(self):
    #     # 获取当前登录用户的数据
    #     user = self.request.user
    #     # print('current user is',user.uuid_company)
    #     query = DataFrame.objects.filter(uuid_user=user.uuid_user)
    #     return self.get_paginated_response(query,self.serializer_class)

    # def create(self, request, *args, **kwargs):
    #     file_obj = request.FILES.get('file')  # 根据字段名称获取文件对象
    #     print('上传文件大小',file_obj.size)
    #     if file_obj:  self.create(request, *args, **kwargs)
    #         file_size = file_obj.size  # 获取文件大小
    #         # 你可以根据需要处理文件大小，例如验证文件是否超过了最大允许的大小等
    #     response = super().create(request, *args, **kwargs)  # 使用父类的create方法创建模型实例

    #     if file_size is not None:
    #         serializer = self.get_serializer()
    #         if hasattr(serializer,'instance') and serializer.instance:
    #             instance = serializer.instance  # 从serializer获取对象
    #             instance.file_size = file_size
    #             instance.save()

    #     return response
# def display_progress(task):
#     while not task.ready():
#         if 'current' in task.info:
#             current = task.info['current']
#             total = task.info['total']
#             progress = (current / total) * 100
#             print(f"\rProgress: [{'#' * int(progress / 10)}{'.' * (10 - int(progress / 10))}] {progress:.2f}%", end="")
#             time.sleep(0.1)
# from datasheet.tasks import long_running_task   
# from celery import current_app  
def get_task_status(request, task_id):
    """
    Get the status of a task.
    """
    # task = long_running_task.delay(100)
    # display_progress(task)
    task = insert_to_vector.AsyncResult(task_id)
    print(f'pro...{task.state},{task.info}')
    # if task.state == 'FAILURE':
    #     print('fdasfdas')
    #     return JsonResponse(mg(data=str(task.info)))
        
        
    response_data = {
        'status':str(task.state),
        'result': str(task.result),
        'info':str(task.info),
    }
    # if task.state == 'PROGRESS':
    # response_data['progress'] = task.info.get('progress', 0)
    
    return JsonResponse(mg(data=response_data))