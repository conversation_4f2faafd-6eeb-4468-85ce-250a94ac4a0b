#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询增强器 - 提升检索效果的智能查询处理

功能:
1. 查询扩展和同义词替换
2. 关键词提取和权重分析
3. 多语言查询处理
4. 查询意图识别
"""

import re
from typing import List, Dict, Tuple, Optional
from loguru import logger

# 可选依赖
try:
    import jieba
    import jieba.analyse
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    logger.warning("jieba不可用，将使用简化的文本处理")


class QueryEnhancer:
    """查询增强器"""
    
    def __init__(self):
        # 初始化jieba（如果可用）
        if JIEBA_AVAILABLE:
            jieba.initialize()
        
        # 扩展的同义词词典
        self.synonyms = {
            # 技术相关
            "API": ["接口", "应用程序接口", "编程接口", "服务接口"],
            "接口": ["API", "interface", "端点", "服务"],
            "文档": ["文件", "资料", "说明书", "手册", "指南"],
            "系统": ["平台", "软件", "程序", "应用"],
            "配置": ["设置", "配制", "设定", "参数"],
            "错误": ["异常", "故障", "问题", "bug", "报错"],
            "安装": ["部署", "安装部署", "配置安装", "搭建"],
            "代码": ["程序", "脚本", "源码", "编码"],
            "数据": ["信息", "资料", "内容", "数据库"],
            "服务": ["服务器", "后端", "接口", "API"],

            # 用户相关
            "用户": ["使用者", "客户", "终端用户", "操作员"],
            "登录": ["登陆", "sign in", "login", "认证", "授权"],
            "使用": ["操作", "应用", "运用", "执行"],
            "方法": ["方式", "途径", "手段", "办法", "步骤"],
            "流程": ["过程", "步骤", "程序", "工序"],

            # 法律相关
            "交通": ["道路", "车辆", "运输", "驾驶"],
            "违章": ["违法", "违规", "处罚", "违反"],
            "处理": ["处置", "解决", "办理", "执行"],
            "法律": ["法规", "条例", "规定", "制度"],
            "条文": ["条款", "规定", "法条", "条例"],
            "处罚": ["罚款", "惩罚", "制裁", "处分"],
            "责任": ["义务", "职责", "负责", "承担"],

            # 商业相关
            "合同": ["协议", "契约", "协定", "约定"],
            "产品": ["商品", "货物", "物品", "服务"],
            "价格": ["费用", "成本", "金额", "报价"],
            "客户": ["用户", "顾客", "买方", "委托方"],
            "服务": ["业务", "项目", "工作", "任务"],

            # 学术相关
            "研究": ["调研", "分析", "探索", "考察"],
            "方法": ["方式", "手段", "途径", "技术"],
            "结果": ["成果", "效果", "产出", "结论"],
            "分析": ["解析", "研究", "评估", "检查"],
            "实验": ["测试", "试验", "检验", "验证"],
        }
        
        # 扩展的停用词
        self.stop_words = {
            # 基础停用词
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都",
            "一", "一个", "上", "也", "很", "到", "说", "要", "去", "你",
            "会", "着", "没有", "看", "好", "自己", "这", "那", "什么", "怎么",
            # 扩展停用词
            "可以", "能够", "应该", "需要", "进行", "实现", "完成", "开始",
            "结束", "包括", "包含", "具有", "存在", "发生", "出现", "产生",
            "得到", "获得", "取得", "达到", "来", "去", "过", "来过", "去过",
            "等", "等等", "之类", "以及", "或者", "还是", "但是", "然而",
            "因为", "所以", "如果", "那么", "这样", "那样", "这里", "那里"
        }
        
        # 扩展的领域关键词权重
        self.domain_weights = {
            "技术": ["API", "接口", "代码", "程序", "系统", "配置", "部署", "开发",
                    "服务器", "数据库", "框架", "算法", "架构", "模块", "组件", "库",
                    "编程", "脚本", "函数", "方法", "类", "对象", "变量", "参数"],
            "法律": ["法规", "条例", "违章", "处罚", "法律", "规定", "条文", "法院",
                    "判决", "起诉", "辩护", "证据", "合同", "协议", "责任", "义务",
                    "权利", "诉讼", "仲裁", "调解", "执行", "上诉", "审理"],
            "用户": ["用户", "客户", "使用", "操作", "手册", "指南", "教程", "帮助",
                    "说明", "介绍", "演示", "示例", "步骤", "流程", "方法", "技巧"],
            "管理": ["管理", "流程", "制度", "规范", "标准", "要求", "政策", "规定",
                    "程序", "办法", "措施", "方案", "计划", "安排", "组织", "协调"],
            "商业": ["合同", "协议", "价格", "费用", "成本", "利润", "收入", "支出",
                    "投资", "融资", "市场", "销售", "采购", "供应", "客户", "服务"],
            "学术": ["研究", "分析", "实验", "测试", "理论", "假设", "结论", "方法",
                    "数据", "统计", "模型", "算法", "论文", "文献", "引用", "参考"],
            "教育": ["课程", "教学", "学习", "培训", "考试", "作业", "练习", "知识",
                    "技能", "能力", "素质", "教育", "学校", "老师", "学生", "课堂"],
            "医疗": ["医院", "医生", "患者", "疾病", "治疗", "药物", "手术", "检查",
                    "诊断", "症状", "病历", "护理", "康复", "预防", "健康", "医疗"]
        }
    
    def enhance_query(self, query: str, context: Optional[Dict] = None) -> Dict[str, any]:
        """
        增强查询
        
        Args:
            query: 原始查询
            context: 上下文信息（文件类型、领域等）
            
        Returns:
            增强后的查询信息
        """
        result = {
            "original_query": query,
            "enhanced_queries": [],
            "keywords": [],
            "intent": None,
            "domain": None,
            "confidence": 0.0
        }
        
        try:
            # 1. 基础处理
            cleaned_query = self._clean_query(query)
            
            # 2. 关键词提取
            keywords = self._extract_keywords(cleaned_query)
            result["keywords"] = keywords
            
            # 3. 意图识别
            intent = self._identify_intent(cleaned_query, keywords)
            result["intent"] = intent
            
            # 4. 领域识别
            domain = self._identify_domain(keywords, context)
            result["domain"] = domain
            
            # 5. 查询扩展
            enhanced_queries = self._expand_query(cleaned_query, keywords, domain)
            result["enhanced_queries"] = enhanced_queries
            
            # 6. 计算置信度
            confidence = self._calculate_confidence(keywords, intent, domain)
            result["confidence"] = confidence
            
            logger.info(f"查询增强完成: {query} -> {len(enhanced_queries)}个扩展查询")
            
        except Exception as e:
            logger.error(f"查询增强失败: {str(e)}")
            result["enhanced_queries"] = [query]  # 回退到原始查询
        
        return result
    
    def _clean_query(self, query: str) -> str:
        """清理查询文本"""
        # 移除特殊字符，保留中英文和数字
        cleaned = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', query)
        # 移除多余空格
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        return cleaned
    
    def _extract_keywords(self, query: str) -> List[Dict[str, any]]:
        """提取关键词"""
        keywords = []

        if JIEBA_AVAILABLE:
            # 使用jieba提取关键词
            try:
                # TF-IDF关键词提取
                tfidf_keywords = jieba.analyse.extract_tags(query, topK=10, withWeight=True)

                # TextRank关键词提取
                textrank_keywords = jieba.analyse.textrank(query, topK=10, withWeight=True)

                # 合并和去重
                all_keywords = {}

                for word, weight in tfidf_keywords:
                    if word not in self.stop_words and len(word) > 1:
                        all_keywords[word] = {
                            "word": word,
                            "tfidf_weight": weight,
                            "textrank_weight": 0.0,
                            "final_weight": weight
                        }

                for word, weight in textrank_keywords:
                    if word not in self.stop_words and len(word) > 1:
                        if word in all_keywords:
                            all_keywords[word]["textrank_weight"] = weight
                            # 综合权重
                            all_keywords[word]["final_weight"] = (
                                all_keywords[word]["tfidf_weight"] * 0.6 + weight * 0.4
                            )
                        else:
                            all_keywords[word] = {
                                "word": word,
                                "tfidf_weight": 0.0,
                                "textrank_weight": weight,
                                "final_weight": weight * 0.4
                            }

                # 按权重排序
                keywords = sorted(all_keywords.values(),
                                key=lambda x: x["final_weight"], reverse=True)

            except Exception as e:
                logger.warning(f"jieba关键词提取失败: {str(e)}")
                # 使用jieba简单分词作为备选
                try:
                    words = jieba.lcut(query)
                    keywords = [{"word": word, "final_weight": 1.0}
                               for word in words if word not in self.stop_words and len(word) > 1]
                except:
                    keywords = self._simple_keyword_extraction(query)
        else:
            # 不使用jieba的简单关键词提取
            keywords = self._simple_keyword_extraction(query)

        return keywords[:10]  # 返回前10个关键词

    def _simple_keyword_extraction(self, query: str) -> List[Dict[str, any]]:
        """简单的关键词提取（不依赖jieba）"""
        # 使用正则表达式分词
        # 匹配中文词汇、英文单词、数字
        words = re.findall(r'[\u4e00-\u9fa5]+|[a-zA-Z]+|[0-9]+', query)

        # 统计词频
        word_freq = {}
        for word in words:
            if word not in self.stop_words and len(word) > 1:
                word_freq[word] = word_freq.get(word, 0) + 1

        # 计算权重（基于词频和词长）
        keywords = []
        for word, freq in word_freq.items():
            weight = freq * (len(word) / 10.0)  # 长词权重更高
            keywords.append({
                "word": word,
                "frequency": freq,
                "final_weight": weight
            })

        # 按权重排序
        keywords.sort(key=lambda x: x["final_weight"], reverse=True)
        return keywords
    
    def _identify_intent(self, query: str, keywords: List[Dict]) -> str:
        """识别查询意图"""
        query_lower = query.lower()
        keyword_words = [kw["word"] for kw in keywords]
        
        # 意图模式匹配
        if any(word in query_lower for word in ["怎么", "如何", "怎样", "方法", "步骤"]):
            return "how_to"
        elif any(word in query_lower for word in ["什么", "是什么", "定义", "含义"]):
            return "definition"
        elif any(word in query_lower for word in ["为什么", "原因", "why"]):
            return "explanation"
        elif any(word in query_lower for word in ["哪里", "在哪", "位置", "where"]):
            return "location"
        elif any(word in query_lower for word in ["错误", "问题", "故障", "异常", "bug"]):
            return "troubleshooting"
        elif any(word in query_lower for word in ["配置", "设置", "安装", "部署"]):
            return "configuration"
        elif any(word in keyword_words for word in ["API", "接口", "调用"]):
            return "api_usage"
        else:
            return "general_search"
    
    def _identify_domain(self, keywords: List[Dict], context: Optional[Dict] = None) -> str:
        """识别查询领域"""
        keyword_words = [kw["word"] for kw in keywords]
        
        # 基于关键词的领域识别
        domain_scores = {}
        for domain, domain_keywords in self.domain_weights.items():
            score = sum(1 for word in keyword_words if word in domain_keywords)
            if score > 0:
                domain_scores[domain] = score
        
        # 基于上下文的领域识别
        if context:
            file_types = context.get("file_types", [])
            if "pdf" in file_types or "doc" in file_types:
                domain_scores["用户"] = domain_scores.get("用户", 0) + 1
            if any("法" in ft for ft in file_types):
                domain_scores["法律"] = domain_scores.get("法律", 0) + 2
        
        # 返回得分最高的领域
        if domain_scores:
            return max(domain_scores.items(), key=lambda x: x[1])[0]
        else:
            return "通用"
    
    def _expand_query(self, query: str, keywords: List[Dict], domain: str) -> List[str]:
        """扩展查询"""
        expanded_queries = [query]  # 包含原始查询
        
        try:
            # 1. 同义词扩展
            synonym_query = self._expand_with_synonyms(query)
            if synonym_query != query:
                expanded_queries.append(synonym_query)
            
            # 2. 关键词组合扩展
            if len(keywords) >= 2:
                top_keywords = [kw["word"] for kw in keywords[:3]]
                keyword_query = " ".join(top_keywords)
                if keyword_query not in expanded_queries:
                    expanded_queries.append(keyword_query)
            
            # 3. 领域特定扩展
            domain_query = self._expand_with_domain(query, domain)
            if domain_query and domain_query not in expanded_queries:
                expanded_queries.append(domain_query)
            
            # 4. 简化查询（只保留核心关键词）
            if len(keywords) >= 1:
                core_query = keywords[0]["word"]
                if len(keywords) >= 2:
                    core_query += " " + keywords[1]["word"]
                if core_query not in expanded_queries:
                    expanded_queries.append(core_query)
        
        except Exception as e:
            logger.warning(f"查询扩展失败: {str(e)}")
        
        return expanded_queries[:6]  # 最多返回6个查询，增加覆盖面
    
    def _expand_with_synonyms(self, query: str) -> str:
        """使用同义词扩展查询"""
        if JIEBA_AVAILABLE:
            try:
                words = jieba.lcut(query)
            except:
                words = self._simple_tokenize(query)
        else:
            words = self._simple_tokenize(query)

        expanded_words = []

        for word in words:
            if word in self.synonyms:
                # 选择第一个同义词
                synonyms = self.synonyms[word]
                if synonyms:
                    expanded_words.append(synonyms[0])
                else:
                    expanded_words.append(word)
            else:
                expanded_words.append(word)

        return " ".join(expanded_words)  # 用空格连接而不是直接连接

    def _simple_tokenize(self, text: str) -> List[str]:
        """简单分词（不依赖jieba）"""
        # 使用正则表达式分词
        words = re.findall(r'[\u4e00-\u9fa5]+|[a-zA-Z]+|[0-9]+', text)
        return [word for word in words if len(word) > 0]
    
    def _expand_with_domain(self, query: str, domain: str) -> Optional[str]:
        """基于领域扩展查询"""
        domain_expansions = {
            "技术": ["技术文档", "开发指南", "API文档"],
            "法律": ["法规条例", "法律条文", "规定制度"],
            "用户": ["使用说明", "操作指南", "用户手册"],
            "管理": ["管理规范", "流程制度", "标准要求"],
            "商业": ["商业合同", "协议条款", "商务文档"],
            "学术": ["研究论文", "学术资料", "理论分析"],
            "教育": ["教学资料", "课程内容", "学习材料"],
            "医疗": ["医疗文档", "诊疗指南", "医学资料"]
        }

        if domain in domain_expansions:
            # 随机选择一个扩展词
            expansion = domain_expansions[domain][0]  # 选择第一个
            return f"{query} {expansion}"
        else:
            return None

    def get_enhanced_queries_for_document_type(self, query: str, doc_type: str) -> List[str]:
        """根据文档类型获取增强查询"""
        context = {"document_type": doc_type}
        result = self.enhance_query(query, context)
        return result["enhanced_queries"]

    def is_available(self) -> bool:
        """检查查询增强器是否可用"""
        return True  # 现在总是可用，因为有备选方案
    
    def _calculate_confidence(self, keywords: List[Dict], intent: str, domain: str) -> float:
        """计算查询理解的置信度"""
        confidence = 0.0
        
        # 基于关键词质量
        if keywords:
            avg_weight = sum(kw["final_weight"] for kw in keywords) / len(keywords)
            confidence += min(avg_weight * 0.4, 0.4)
        
        # 基于意图识别
        if intent != "general_search":
            confidence += 0.3
        
        # 基于领域识别
        if domain != "通用":
            confidence += 0.3
        
        return min(confidence, 1.0)


def test_query_enhancer():
    """测试查询增强器"""
    enhancer = QueryEnhancer()
    
    test_queries = [
        "如何调用用户登录API接口",
        "交通违章处理流程",
        "系统配置文件在哪里",
        "API接口返回错误怎么办",
        "文档编辑方法"
    ]
    
    for query in test_queries:
        print(f"\n原始查询: {query}")
        result = enhancer.enhance_query(query)
        print(f"关键词: {[kw['word'] for kw in result['keywords']]}")
        print(f"意图: {result['intent']}")
        print(f"领域: {result['domain']}")
        print(f"置信度: {result['confidence']:.2f}")
        print(f"扩展查询: {result['enhanced_queries']}")


if __name__ == "__main__":
    test_query_enhancer()
