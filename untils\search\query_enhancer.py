#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询增强器 - 提升检索效果的智能查询处理

功能:
1. 查询扩展和同义词替换
2. 关键词提取和权重分析
3. 多语言查询处理
4. 查询意图识别
"""

import re
import jieba
import jieba.analyse
from typing import List, Dict, Tuple, Optional
from loguru import logger


class QueryEnhancer:
    """查询增强器"""
    
    def __init__(self):
        # 初始化jieba
        jieba.initialize()
        
        # 同义词词典
        self.synonyms = {
            "API": ["接口", "应用程序接口", "编程接口"],
            "接口": ["API", "interface", "端点"],
            "文档": ["文件", "资料", "说明书", "手册"],
            "用户": ["使用者", "客户", "终端用户"],
            "登录": ["登陆", "sign in", "login", "认证"],
            "交通": ["道路", "车辆", "运输"],
            "违章": ["违法", "违规", "处罚"],
            "处理": ["处置", "解决", "办理"],
            "方法": ["方式", "途径", "手段", "办法"],
            "系统": ["平台", "软件", "程序"],
            "配置": ["设置", "配制", "设定"],
            "错误": ["异常", "故障", "问题", "bug"],
            "安装": ["部署", "安装部署", "配置安装"],
            "使用": ["操作", "应用", "运用"],
        }
        
        # 停用词
        self.stop_words = {
            "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", 
            "一", "一个", "上", "也", "很", "到", "说", "要", "去", "你", 
            "会", "着", "没有", "看", "好", "自己", "这", "那", "什么", "怎么"
        }
        
        # 领域关键词权重
        self.domain_weights = {
            "技术": ["API", "接口", "代码", "程序", "系统", "配置", "部署", "开发"],
            "法律": ["法规", "条例", "违章", "处罚", "法律", "规定", "条文"],
            "用户": ["用户", "客户", "使用", "操作", "手册", "指南", "教程"],
            "管理": ["管理", "流程", "制度", "规范", "标准", "要求"]
        }
    
    def enhance_query(self, query: str, context: Optional[Dict] = None) -> Dict[str, any]:
        """
        增强查询
        
        Args:
            query: 原始查询
            context: 上下文信息（文件类型、领域等）
            
        Returns:
            增强后的查询信息
        """
        result = {
            "original_query": query,
            "enhanced_queries": [],
            "keywords": [],
            "intent": None,
            "domain": None,
            "confidence": 0.0
        }
        
        try:
            # 1. 基础处理
            cleaned_query = self._clean_query(query)
            
            # 2. 关键词提取
            keywords = self._extract_keywords(cleaned_query)
            result["keywords"] = keywords
            
            # 3. 意图识别
            intent = self._identify_intent(cleaned_query, keywords)
            result["intent"] = intent
            
            # 4. 领域识别
            domain = self._identify_domain(keywords, context)
            result["domain"] = domain
            
            # 5. 查询扩展
            enhanced_queries = self._expand_query(cleaned_query, keywords, domain)
            result["enhanced_queries"] = enhanced_queries
            
            # 6. 计算置信度
            confidence = self._calculate_confidence(keywords, intent, domain)
            result["confidence"] = confidence
            
            logger.info(f"查询增强完成: {query} -> {len(enhanced_queries)}个扩展查询")
            
        except Exception as e:
            logger.error(f"查询增强失败: {str(e)}")
            result["enhanced_queries"] = [query]  # 回退到原始查询
        
        return result
    
    def _clean_query(self, query: str) -> str:
        """清理查询文本"""
        # 移除特殊字符，保留中英文和数字
        cleaned = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', query)
        # 移除多余空格
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        return cleaned
    
    def _extract_keywords(self, query: str) -> List[Dict[str, any]]:
        """提取关键词"""
        keywords = []
        
        # 使用jieba提取关键词
        try:
            # TF-IDF关键词提取
            tfidf_keywords = jieba.analyse.extract_tags(query, topK=10, withWeight=True)
            
            # TextRank关键词提取
            textrank_keywords = jieba.analyse.textrank(query, topK=10, withWeight=True)
            
            # 合并和去重
            all_keywords = {}
            
            for word, weight in tfidf_keywords:
                if word not in self.stop_words and len(word) > 1:
                    all_keywords[word] = {
                        "word": word,
                        "tfidf_weight": weight,
                        "textrank_weight": 0.0,
                        "final_weight": weight
                    }
            
            for word, weight in textrank_keywords:
                if word not in self.stop_words and len(word) > 1:
                    if word in all_keywords:
                        all_keywords[word]["textrank_weight"] = weight
                        # 综合权重
                        all_keywords[word]["final_weight"] = (
                            all_keywords[word]["tfidf_weight"] * 0.6 + weight * 0.4
                        )
                    else:
                        all_keywords[word] = {
                            "word": word,
                            "tfidf_weight": 0.0,
                            "textrank_weight": weight,
                            "final_weight": weight * 0.4
                        }
            
            # 按权重排序
            keywords = sorted(all_keywords.values(), 
                            key=lambda x: x["final_weight"], reverse=True)
            
        except Exception as e:
            logger.warning(f"关键词提取失败: {str(e)}")
            # 简单分词作为备选
            words = jieba.lcut(query)
            keywords = [{"word": word, "final_weight": 1.0} 
                       for word in words if word not in self.stop_words and len(word) > 1]
        
        return keywords[:8]  # 返回前8个关键词
    
    def _identify_intent(self, query: str, keywords: List[Dict]) -> str:
        """识别查询意图"""
        query_lower = query.lower()
        keyword_words = [kw["word"] for kw in keywords]
        
        # 意图模式匹配
        if any(word in query_lower for word in ["怎么", "如何", "怎样", "方法", "步骤"]):
            return "how_to"
        elif any(word in query_lower for word in ["什么", "是什么", "定义", "含义"]):
            return "definition"
        elif any(word in query_lower for word in ["为什么", "原因", "why"]):
            return "explanation"
        elif any(word in query_lower for word in ["哪里", "在哪", "位置", "where"]):
            return "location"
        elif any(word in query_lower for word in ["错误", "问题", "故障", "异常", "bug"]):
            return "troubleshooting"
        elif any(word in query_lower for word in ["配置", "设置", "安装", "部署"]):
            return "configuration"
        elif any(word in keyword_words for word in ["API", "接口", "调用"]):
            return "api_usage"
        else:
            return "general_search"
    
    def _identify_domain(self, keywords: List[Dict], context: Optional[Dict] = None) -> str:
        """识别查询领域"""
        keyword_words = [kw["word"] for kw in keywords]
        
        # 基于关键词的领域识别
        domain_scores = {}
        for domain, domain_keywords in self.domain_weights.items():
            score = sum(1 for word in keyword_words if word in domain_keywords)
            if score > 0:
                domain_scores[domain] = score
        
        # 基于上下文的领域识别
        if context:
            file_types = context.get("file_types", [])
            if "pdf" in file_types or "doc" in file_types:
                domain_scores["用户"] = domain_scores.get("用户", 0) + 1
            if any("法" in ft for ft in file_types):
                domain_scores["法律"] = domain_scores.get("法律", 0) + 2
        
        # 返回得分最高的领域
        if domain_scores:
            return max(domain_scores.items(), key=lambda x: x[1])[0]
        else:
            return "通用"
    
    def _expand_query(self, query: str, keywords: List[Dict], domain: str) -> List[str]:
        """扩展查询"""
        expanded_queries = [query]  # 包含原始查询
        
        try:
            # 1. 同义词扩展
            synonym_query = self._expand_with_synonyms(query)
            if synonym_query != query:
                expanded_queries.append(synonym_query)
            
            # 2. 关键词组合扩展
            if len(keywords) >= 2:
                top_keywords = [kw["word"] for kw in keywords[:3]]
                keyword_query = " ".join(top_keywords)
                if keyword_query not in expanded_queries:
                    expanded_queries.append(keyword_query)
            
            # 3. 领域特定扩展
            domain_query = self._expand_with_domain(query, domain)
            if domain_query and domain_query not in expanded_queries:
                expanded_queries.append(domain_query)
            
            # 4. 简化查询（只保留核心关键词）
            if len(keywords) >= 1:
                core_query = keywords[0]["word"]
                if len(keywords) >= 2:
                    core_query += " " + keywords[1]["word"]
                if core_query not in expanded_queries:
                    expanded_queries.append(core_query)
        
        except Exception as e:
            logger.warning(f"查询扩展失败: {str(e)}")
        
        return expanded_queries[:4]  # 最多返回4个查询
    
    def _expand_with_synonyms(self, query: str) -> str:
        """使用同义词扩展查询"""
        words = jieba.lcut(query)
        expanded_words = []
        
        for word in words:
            if word in self.synonyms:
                # 选择第一个同义词
                synonyms = self.synonyms[word]
                if synonyms:
                    expanded_words.append(synonyms[0])
                else:
                    expanded_words.append(word)
            else:
                expanded_words.append(word)
        
        return "".join(expanded_words)
    
    def _expand_with_domain(self, query: str, domain: str) -> Optional[str]:
        """基于领域扩展查询"""
        if domain == "技术":
            return f"{query} 技术文档"
        elif domain == "法律":
            return f"{query} 法规条例"
        elif domain == "用户":
            return f"{query} 使用说明"
        elif domain == "管理":
            return f"{query} 管理规范"
        else:
            return None
    
    def _calculate_confidence(self, keywords: List[Dict], intent: str, domain: str) -> float:
        """计算查询理解的置信度"""
        confidence = 0.0
        
        # 基于关键词质量
        if keywords:
            avg_weight = sum(kw["final_weight"] for kw in keywords) / len(keywords)
            confidence += min(avg_weight * 0.4, 0.4)
        
        # 基于意图识别
        if intent != "general_search":
            confidence += 0.3
        
        # 基于领域识别
        if domain != "通用":
            confidence += 0.3
        
        return min(confidence, 1.0)


def test_query_enhancer():
    """测试查询增强器"""
    enhancer = QueryEnhancer()
    
    test_queries = [
        "如何调用用户登录API接口",
        "交通违章处理流程",
        "系统配置文件在哪里",
        "API接口返回错误怎么办",
        "文档编辑方法"
    ]
    
    for query in test_queries:
        print(f"\n原始查询: {query}")
        result = enhancer.enhance_query(query)
        print(f"关键词: {[kw['word'] for kw in result['keywords']]}")
        print(f"意图: {result['intent']}")
        print(f"领域: {result['domain']}")
        print(f"置信度: {result['confidence']:.2f}")
        print(f"扩展查询: {result['enhanced_queries']}")


if __name__ == "__main__":
    test_query_enhancer()
