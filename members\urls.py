"""
URL configuration for repository project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path
from . import views

urlpatterns = [
    # path('signin', views.Signin.as_view()),
    #修改部门管理员信息和删除部门管理员
    path('user/<int:id>', views.UserViewUpdate.as_view({'put':'put','delete':'delete'})),
    # #管理端接口修改和删除用户
    # path('user_admin/<int:id>', views.AdminUserView.as_view({'put':'put','delete':'delete'})),
    # # 登录登出
    path('user/login/', views.UserLogin.as_view({'post':'signin'})),
    path('username/login/', views.UsernameLogin.as_view({'post':'signin'})),
    path('user/logout/', views.UserLogout.as_view({'get':'signout'})),
    # 注册和获取用户列表
    path('user/', views.UserViewSet.as_view({'get':'get'})),
    path('user/roles/', views.UserRolesView.as_view()),
    # 注册部门管理账号
    path('user/depart/', views.DepartUserViewSet.as_view({'post':'post'})),
    path('user/depart/members/', views.DepartmentViewSet.as_view()),
    # path('user_center/', views.UserCenterViewSet.as_view()),
    # #修改密码
    # path('rewrite/', views.PasswordChangeView.as_view({'put':'update'})),
    # #忘记密码
    # path('smspassword/', views.PasswordCodeView.as_view({'get':'code','put':'resetpassword'})),
    # #充值和激活用户
    # path('active_reset/', views.UserSetView.as_view()),
    # # path('', include(router.urls)),
    # path('userapp/', views.UserappViewSet.as_view({'get':'get'})),
    
    # path('useravatar/', views.AvatarView.as_view()),
    # path('modifylastlogin/', views.LastLoginView.as_view()),
    path('user/log/', views.LoginLogView.as_view({'post':'post'})),
    path('user/log/<int:id>', views.LoginLogIDView.as_view({'put':'put','delete':'delete'})),
    
]
