
from untils.common import get_all_files, read_json, recursive_character_length_function

from loguru import logger
from langchain.docstore.document import Document
from langchain.text_splitter import CharacterTextSplitter

import re,json
import copy
from typing import List
from typing import Any, Union,Optional, Iterable



def _split_text_with_regex(
    text: str, separator: str, keep_separator: bool
) -> List[str]:
    # Now that we have the separator, split the text
    if not separator:
        return list(text)  # 如果分隔符为空，返回单字符列表

    # 使用正则表达式分割，保留分隔符
    splits = re.split(f'({separator})', text) if keep_separator != "none" else re.split(separator, text)
    
    result = []
    if keep_separator == "end":
        # 将分隔符保留在当前片段的末尾
        for i in range(0, len(splits) - 1, 2):
            result.append(splits[i] + splits[i + 1])
        if len(splits) % 2 != 0:  # 如果有奇数个元素，添加最后一个元素
            result.append(splits[-1])
    
    elif keep_separator == "start":
        # 将分隔符移动到下一个元素的开头
        if len(splits) > 0:
            result.append(splits[0])  # 第一个元素可能没有前导分隔符
        for i in range(1, len(splits) - 1, 2):
            result.append(splits[i] + splits[i + 1])
        if len(splits) % 2 == 0 and len(splits) > 1:  # 如果有偶数个元素，最后一个分隔符需要处理
            result.append(splits[-1])
    
    else:
        # 不保留分隔符
        result = [s for s in splits if s]

    return [s for s in result if s.strip()]  # 去除空白字符

    # if separator:
        
    #     # print('fdfdsfdsa-----------------',  keep_separator)
    #     if keep_separator:
    #         # print('2222222222222222222')
    #         # The parentheses in the pattern keep the delimiters in the result.
    #         _splits = re.split(f"({separator})", text)
    #         splits = []
    #         for i in range(0, len(_splits), 2):
    #             part = _splits[i]
    #             if i + 1 < len(_splits):
    #                 if keep_separator:
    #                     splits.append(part)
    #                     splits.append(_splits[i + 1])
    #                 else:
    #                     splits.append(part)
    #                     splits.append(_splits[i + 1])
    #             else:
    #                 splits.append(part)
    #         # 将分隔符移动到下一段开头
    #         if keep_separator:
    #             for i in range(1, len(splits), 2):
    #                 splits[i] = splits[i] + splits[i + 1]
    #                 splits[i + 1] = ''
    #     else:
            
    #         # print('4444444444444',keep_separator)
    #         splits = re.split(separator, text)
    # else:
    #     # print('333333333333333333333')
    #     splits = list(text)
    # return [s for s in splits if s != ""]

class CHZNTextSplitter(CharacterTextSplitter):
    
    def __init__(
        self, separator: Union[str, List[str]], keep_separator:Any,  **kwargs: Any
    ) -> None:
        """Create a new TextSplitter."""
        self.separator = separator
        super().__init__(**kwargs)
        self._keep_separator=keep_separator
        self._is_separator_list = isinstance(separator, list)
        self._is_separator_regex = not self._is_separator_list and bool(re.compile(separator))
        
    def split_text(self, text: str) -> List[str]:
        """Split incoming text and return chunks."""
        # First we naively split the large input into a bunch of smaller ones.
        if self._is_separator_list:
            separator = '|'.join(map(re.escape, self.separator))
        else:
            separator = re.escape(self.separator) if not self._is_separator_regex else self.separator
        
        # print('9999999999999',self._is_separator_regex,self._is_separator_list,separator,type(separator))
        splits = _split_text_with_regex(text, separator, self._keep_separator)
        _separator = "" if self._keep_separator else self._separator
        return self._merge_splits(splits, _separator)
    
 
    def split_documents(self, documents: Iterable[Document]) -> List[List[Document]]:
        """Split documents."""
        all_docs_split = []
        for doc in documents:
            texts, metadatas = [doc.page_content], [doc.metadata]
            split_docs = self.create_documents(texts, metadatas=metadatas)
            all_docs_split.append(split_docs)
        return all_docs_split

class TextSpilt():   
        
    def parse_separator(self,separator: str) -> Union[str, List[str]]:
        """Parse the separator input into the correct type."""
        try:
            # Attempt to parse the separator as JSON
            parsed_separator = json.loads(separator)
            # logger.info(f'---{type(parsed_separator)},{type(separator)}')
            if isinstance(parsed_separator, list):
                logger.info(f'Detected list format separator : {type(parsed_separator)}')
                return parsed_separator
            else:
                logger.info(f'Detected JSON format but not list, treating as string: {parsed_separator}')
                return separator
        except json.JSONDecodeError:
            try:
                re.compile(separator)
                logger.info("Detected valid regex separator  : <class 'regx'>")
                return separator  # It's a regex
            except re.error:
                logger.info("Detected simple string : <class 'str'>")
                return separator  # It's a simple string     
            
    def build_params(self, request):
        parser_params={}
        separator = request.POST.get('separator','\n')
        # keep_separator = self.request.POST.get('keep_separator', 'True').lower() in ['true', '1', 'yes']
        keep_separator = request.POST.get('keep_separator', 'none')
        if separator is not None:
            parser_params['separator'] = self.parse_separator(separator)
        if keep_separator is not None:
            parser_params['keep_separator'] = keep_separator
        
        chunksize = request.POST.get('chunk_size','1024')
        if chunksize is not None:
            parser_params['chunk_size'] = int(chunksize)
        chunkoverlap = request.POST.get('chunk_overlap','128')
        if chunkoverlap is not None:
            parser_params['chunk_overlap'] = int(chunkoverlap)
        
        return parser_params   
            
    
    def split_from_folder(
        self,
        folder_path: str,
        separator:Union[str, List[str]],
        keep_separator: bool = False,
        chunk_size: int = 1024,
        chunk_overlap: int = 128,
    ):    
        # print('++++++++++',chunk_size,type(chunk_size))
        files = get_all_files(folder_path)
        text_spliter = CHZNTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separator=separator,
            keep_separator=keep_separator,
        )
        docs = []
        try:
            for _, file in enumerate(files):
                doc_obj = read_json(file)
                doc_meta = doc_obj["meta"]
                doc_content = doc_obj["content"] or '解析失败'
                doc = [Document(page_content=doc_content,metadata=doc_meta)]
                if doc_content:
                    chunks = text_spliter.split_documents(doc)
                else:
                    chunks = [doc]
                docs.extend(chunks)
            return docs
        except Exception as e:
            logger.error(f"Error when split file 【 {file}】:{e}")
            
    def split_from_cache(
        self,
        cache: List[str],
        separator:Union[str, List[str]],
        keep_separator: bool = False,
        chunk_size: int = 1024,
        chunk_overlap: int = 128,
        ):
        text_spliter = CHZNTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separator=separator,
            keep_separator=keep_separator,
        )
        docs = []
        try:
            for  file in cache:
                doc_meta = file["meta"]
                doc_content = file["content"] or '解析失败'
                doc = [Document(page_content=doc_content,metadata=doc_meta)]
                if doc_content:
                    chunks = text_spliter.split_documents(doc)
                else:
                    chunks = [doc]
                docs.extend(chunks)
            return docs
        except Exception as e:
            logger.error(f"Error when split file 【 {file}】:{e}")