# 使用官方Python镜像作为基础
# 阶段1：构建
FROM python:3.11.5-slim as builder
WORKDIR /install
# 安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --prefix=/install -r requirements.txt 

# 阶段2：最终镜像
FROM python:3.11.5-slim
COPY --from=builder /install /usr/local
# 设置工作目录
WORKDIR /server
# 安装系统依赖，包括 curl
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*
# 安装系统依赖
RUN apt-get update && apt-get install -y catdoc libssl-dev libffi-dev && \
pip install cryptography
RUN python -m playwright install
RUN python -m playwright install-deps
# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV PYTHONMALLOC malloc
# 复制项目文件到容器中
COPY . /server/
RUN chmod +x /server/init.sh
CMD ["./init.sh"]