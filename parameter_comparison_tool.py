# ===================================================================
# 此文件已被注释 - 用于测试和演示目的
# 如需使用，请取消注释
# ===================================================================

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数效果对比工具

帮助用户理解不同参数设置对检索结果的影响
"""

import requests
import time
import json
from typing import Dict, List, Any, Tuple


class ParameterComparisonTool:
    """参数效果对比工具"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        
        # 预定义的对比配置
        self.comparison_configs = {
            "batch_size_comparison": [
                {"name": "小批次", "batch_size": 30, "description": "快速但可能遗漏信息"},
                {"name": "中批次", "batch_size": 60, "description": "平衡性能和覆盖"},
                {"name": "大批次", "batch_size": 120, "description": "全面但响应较慢"}
            ],
            "min_score_comparison": [
                {"name": "宽松阈值", "min_score": 0.15, "description": "最大覆盖面"},
                {"name": "标准阈值", "min_score": 0.25, "description": "平衡质量和数量"},
                {"name": "严格阈值", "min_score": 0.4, "description": "高质量结果"}
            ],
            "analysis_depth_comparison": [
                {"name": "基础分析", "enable_deep_analysis": False, "description": "快速响应"},
                {"name": "深度分析", "enable_deep_analysis": True, "description": "详细信息"}
            ]
        }
    
    def compare_batch_sizes(self, query: str, files: List[str]) -> Dict[str, Any]:
        """对比不同批次大小的效果"""
        print("📊 批次大小对比测试")
        print("=" * 50)
        
        results = {}
        base_config = {
            "text": query,
            "selected_files": files,
            "min_score": 0.25,
            "max_results": 200,
            "enable_deep_analysis": True
        }
        
        for config in self.comparison_configs["batch_size_comparison"]:
            print(f"\n🔍 测试 {config['name']} (batch_size={config['batch_size']})")
            
            test_config = {**base_config, "batch_size": config["batch_size"]}
            
            start_time = time.time()
            result = self._call_api(test_config)
            end_time = time.time()
            
            if 'error' not in result:
                data = result.get('data', {})
                results[config['name']] = {
                    'response_time': round(end_time - start_time, 2),
                    'total_segments': data.get('total_segments', 0),
                    'avg_score': self._calculate_avg_score(data),
                    'config': config
                }
                
                print(f"   ⏱️  响应时间: {results[config['name']]['response_time']}秒")
                print(f"   📊 结果数量: {results[config['name']]['total_segments']}")
                print(f"   🎯 平均分数: {results[config['name']]['avg_score']:.3f}")
            else:
                print(f"   ❌ 测试失败: {result['error']}")
        
        return results
    
    def compare_score_thresholds(self, query: str, files: List[str]) -> Dict[str, Any]:
        """对比不同分数阈值的效果"""
        print("\n🎯 分数阈值对比测试")
        print("=" * 50)
        
        results = {}
        base_config = {
            "text": query,
            "selected_files": files,
            "batch_size": 60,
            "max_results": 300,
            "enable_deep_analysis": True
        }
        
        for config in self.comparison_configs["min_score_comparison"]:
            print(f"\n🔍 测试 {config['name']} (min_score={config['min_score']})")
            
            test_config = {**base_config, "min_score": config["min_score"]}
            
            result = self._call_api(test_config)
            
            if 'error' not in result:
                data = result.get('data', {})
                results[config['name']] = {
                    'total_segments': data.get('total_segments', 0),
                    'avg_score': self._calculate_avg_score(data),
                    'score_distribution': self._analyze_score_distribution(data),
                    'config': config
                }
                
                print(f"   📊 结果数量: {results[config['name']]['total_segments']}")
                print(f"   🎯 平均分数: {results[config['name']]['avg_score']:.3f}")
                
                dist = results[config['name']]['score_distribution']
                print(f"   📈 分数分布: 高({dist['high']}) 中({dist['medium']}) 低({dist['low']})")
            else:
                print(f"   ❌ 测试失败: {result['error']}")
        
        return results
    
    def compare_analysis_depth(self, query: str, files: List[str]) -> Dict[str, Any]:
        """对比分析深度的效果"""
        print("\n🧠 分析深度对比测试")
        print("=" * 50)
        
        results = {}
        base_config = {
            "text": query,
            "selected_files": files,
            "batch_size": 50,
            "min_score": 0.25,
            "max_results": 200
        }
        
        for config in self.comparison_configs["analysis_depth_comparison"]:
            print(f"\n🔍 测试 {config['name']} (deep_analysis={config['enable_deep_analysis']})")
            
            test_config = {**base_config, "enable_deep_analysis": config["enable_deep_analysis"]}
            
            start_time = time.time()
            result = self._call_api(test_config)
            end_time = time.time()
            
            if 'error' not in result:
                data = result.get('data', {})
                results[config['name']] = {
                    'response_time': round(end_time - start_time, 2),
                    'analysis_method': data.get('analysis_method', 'unknown'),
                    'has_entities': 'entities' in data,
                    'has_clusters': 'content_clusters' in data,
                    'has_structured_summary': 'structured_summary' in data,
                    'config': config
                }
                
                print(f"   ⏱️  响应时间: {results[config['name']]['response_time']}秒")
                print(f"   🔬 分析方法: {results[config['name']]['analysis_method']}")
                print(f"   🏷️  实体识别: {'是' if results[config['name']]['has_entities'] else '否'}")
                print(f"   🎭 内容聚类: {'是' if results[config['name']]['has_clusters'] else '否'}")
                print(f"   📋 结构化总结: {'是' if results[config['name']]['has_structured_summary'] else '否'}")
            else:
                print(f"   ❌ 测试失败: {result['error']}")
        
        return results
    
    def comprehensive_comparison(self, query: str, files: List[str]) -> Dict[str, Any]:
        """综合对比测试"""
        print("🎯 综合参数对比测试")
        print("=" * 60)
        
        # 定义几种典型配置
        test_configs = {
            "快速预览": {
                "batch_size": 30,
                "min_score": 0.3,
                "max_results": 80,
                "enable_deep_analysis": False
            },
            "标准分析": {
                "batch_size": 50,
                "min_score": 0.25,
                "max_results": 250,
                "enable_deep_analysis": True
            },
            "深度分析": {
                "batch_size": 100,
                "min_score": 0.2,
                "max_results": 500,
                "enable_deep_analysis": True
            },
            "高精度": {
                "batch_size": 40,
                "min_score": 0.4,
                "max_results": 150,
                "enable_deep_analysis": True
            }
        }
        
        results = {}
        base_config = {
            "text": query,
            "selected_files": files
        }
        
        for name, config in test_configs.items():
            print(f"\n🔍 测试配置: {name}")
            
            test_config = {**base_config, **config}
            
            start_time = time.time()
            result = self._call_api(test_config)
            end_time = time.time()
            
            if 'error' not in result:
                data = result.get('data', {})
                results[name] = {
                    'response_time': round(end_time - start_time, 2),
                    'total_segments': data.get('total_segments', 0),
                    'avg_score': self._calculate_avg_score(data),
                    'analysis_features': self._count_analysis_features(data),
                    'config': config
                }
                
                print(f"   ⏱️  响应时间: {results[name]['response_time']}秒")
                print(f"   📊 结果数量: {results[name]['total_segments']}")
                print(f"   🎯 平均分数: {results[name]['avg_score']:.3f}")
                print(f"   🔬 分析特性: {results[name]['analysis_features']}项")
            else:
                print(f"   ❌ 测试失败: {result['error']}")
        
        # 生成对比总结
        self._generate_comparison_summary(results)
        
        return results
    
    def _call_api(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """调用API"""
        url = f"{self.base_url}/datasheet/fulltextsearch/"
        
        try:
            response = requests.post(url, json=config, timeout=120)
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'error': str(e)}
    
    def _calculate_avg_score(self, data: Dict[str, Any]) -> float:
        """计算平均分数"""
        if 'basic_statistics' in data:
            return data['basic_statistics'].get('average_score', 0)
        elif 'content_previews' in data:
            previews = data['content_previews']
            if previews:
                scores = [p.get('score', 0) for p in previews]
                return sum(scores) / len(scores)
        return 0.0
    
    def _analyze_score_distribution(self, data: Dict[str, Any]) -> Dict[str, int]:
        """分析分数分布"""
        distribution = {'high': 0, 'medium': 0, 'low': 0}
        
        if 'basic_statistics' in data:
            stats = data['basic_statistics']
            if 'score_distribution' in stats:
                return stats['score_distribution']
        
        # 如果没有现成的分布数据，尝试从其他地方计算
        return distribution
    
    def _count_analysis_features(self, data: Dict[str, Any]) -> int:
        """统计分析特性数量"""
        features = 0
        
        if 'key_themes' in data:
            features += 1
        if 'entities' in data:
            features += 1
        if 'content_clusters' in data:
            features += 1
        if 'structured_summary' in data:
            features += 1
        if 'basic_statistics' in data:
            features += 1
        
        return features
    
    def _generate_comparison_summary(self, results: Dict[str, Any]):
        """生成对比总结"""
        print("\n📋 对比总结")
        print("=" * 40)
        
        # 按响应时间排序
        by_speed = sorted(results.items(), key=lambda x: x[1]['response_time'])
        print(f"🚀 最快响应: {by_speed[0][0]} ({by_speed[0][1]['response_time']}秒)")
        
        # 按结果数量排序
        by_quantity = sorted(results.items(), key=lambda x: x[1]['total_segments'], reverse=True)
        print(f"📊 最多结果: {by_quantity[0][0]} ({by_quantity[0][1]['total_segments']}个)")
        
        # 按质量排序
        by_quality = sorted(results.items(), key=lambda x: x[1]['avg_score'], reverse=True)
        print(f"🎯 最高质量: {by_quality[0][0]} (平均分数{by_quality[0][1]['avg_score']:.3f})")
        
        # 按功能丰富度排序
        by_features = sorted(results.items(), key=lambda x: x[1]['analysis_features'], reverse=True)
        print(f"🔬 最多功能: {by_features[0][0]} ({by_features[0][1]['analysis_features']}项特性)")


def demo_parameter_comparison():
    """演示参数对比"""
    tool = ParameterComparisonTool()
    
    # 测试参数
    query = "主要内容 重要信息"
    files = ["道路交通.txt"]
    
    print("🧪 参数效果对比演示")
    print(f"查询: {query}")
    print(f"文件: {files}")
    
    try:
        # 批次大小对比
        tool.compare_batch_sizes(query, files)
        
        # 分数阈值对比
        tool.compare_score_thresholds(query, files)
        
        # 分析深度对比
        tool.compare_analysis_depth(query, files)
        
        # 综合对比
        tool.comprehensive_comparison(query, files)
        
        print("\n✅ 对比测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")


if __name__ == "__main__":
    demo_parameter_comparison()
