#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用文档分析器

提供完全通用的文档内容分析功能，不依赖特定领域的硬编码规则
"""

import re
import jieba
import jieba.analyse
from collections import Counter, defaultdict
from typing import List, Dict, Any, Optional
from loguru import logger


class UniversalDocumentAnalyzer:
    """通用文档分析器"""
    
    def __init__(self):
        # 初始化jieba
        jieba.initialize()
        
        # 通用停用词
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', 
            '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', 
            '会', '着', '没有', '看', '好', '自己', '这', '那', '什么', '怎么',
            '可以', '应该', '因为', '所以', '但是', '然后', '现在', '时候',
            '已经', '还是', '只是', '如果', '虽然', '或者', '而且', '不过'
        }
        
        # 实体类型模式（通用）
        self.entity_patterns = {
            'person': [
                r'[\u4e00-\u9fa5]{2,3}(?:先生|女士|老师|教授|博士|主任|经理|总裁|主席)',
                r'[\u4e00-\u9fa5]{2,4}(?:说|道|表示|认为|指出|强调)',
            ],
            'place': [
                r'[\u4e00-\u9fa5]{2,6}(?:市|省|县|区|镇|村|路|街|院|校|厂|公司)',
                r'[\u4e00-\u9fa5]{2,8}(?:大学|学院|医院|银行|酒店)',
            ],
            'time': [
                r'\d{4}年\d{1,2}月\d{1,2}日',
                r'\d{4}年\d{1,2}月',
                r'\d{4}年',
                r'(?:春秋|战国|汉朝|唐朝|宋朝|元朝|明朝|清朝)时期',
            ],
            'event': [
                r'[\u4e00-\u9fa5]{2,8}(?:战争|战役|会议|活动|事件|运动|革命)',
                r'[\u4e00-\u9fa5]{2,6}(?:之战|会战|大战)',
            ],
            'concept': [
                r'[\u4e00-\u9fa5]{2,6}(?:理论|原理|方法|技术|策略|政策|制度)',
                r'[\u4e00-\u9fa5]{2,8}(?:主义|思想|观念|概念)',
            ]
        }
    
    def analyze_documents(self, documents: List[Any], query: str, analysis_type: str = 'general') -> Dict[str, Any]:
        """
        通用文档分析
        
        Args:
            documents: 文档列表
            query: 查询文本
            analysis_type: 分析类型
            
        Returns:
            分析结果
        """
        # 提取所有文本内容
        all_content = self._extract_content(documents)
        
        # 基础统计
        basic_stats = self._calculate_basic_stats(documents, all_content)
        
        # 关键词提取
        keywords = self._extract_keywords(all_content, query)
        
        # 实体识别
        entities = self._extract_entities(all_content)
        
        # 主题聚类
        clusters = self._cluster_content(documents, keywords, entities)
        
        # 生成结构化输出
        structured_output = self._generate_universal_summary(
            basic_stats, keywords, entities, clusters, query, analysis_type
        )
        
        return {
            'basic_stats': basic_stats,
            'keywords': keywords,
            'entities': entities,
            'clusters': clusters,
            'structured_output': structured_output
        }
    
    def _extract_content(self, documents: List[Any]) -> str:
        """提取文档内容"""
        contents = []
        for doc in documents:
            if hasattr(doc, 'page_content'):
                contents.append(doc.page_content)
            elif isinstance(doc, dict):
                contents.append(doc.get('content', ''))
            else:
                contents.append(str(doc))
        
        return ' '.join(contents)
    
    def _calculate_basic_stats(self, documents: List[Any], content: str) -> Dict[str, Any]:
        """计算基础统计信息"""
        scores = []
        for doc in documents:
            if hasattr(doc, 'metadata'):
                score = doc.metadata.get('score', 0)
                scores.append(score)
        
        return {
            'total_documents': len(documents),
            'total_characters': len(content),
            'total_words': len(jieba.lcut(content)),
            'avg_score': sum(scores) / len(scores) if scores else 0,
            'score_distribution': {
                'high': sum(1 for s in scores if s >= 0.7),
                'medium': sum(1 for s in scores if 0.4 <= s < 0.7),
                'low': sum(1 for s in scores if s < 0.4)
            } if scores else {}
        }
    
    def _extract_keywords(self, content: str, query: str) -> List[Dict[str, Any]]:
        """通用关键词提取"""
        try:
            # 使用TF-IDF提取关键词
            tfidf_keywords = jieba.analyse.extract_tags(content, topK=20, withWeight=True)
            
            # 使用TextRank提取关键词
            textrank_keywords = jieba.analyse.textrank(content, topK=20, withWeight=True)
            
            # 合并和评分
            keyword_scores = defaultdict(float)
            
            for word, weight in tfidf_keywords:
                if word not in self.stop_words and len(word) > 1:
                    keyword_scores[word] += weight * 0.6
            
            for word, weight in textrank_keywords:
                if word not in self.stop_words and len(word) > 1:
                    keyword_scores[word] += weight * 0.4
            
            # 查询相关性加权
            query_words = set(jieba.lcut(query))
            for word in keyword_scores:
                if word in query_words:
                    keyword_scores[word] *= 1.5
            
            # 排序并返回
            sorted_keywords = sorted(keyword_scores.items(), key=lambda x: x[1], reverse=True)
            
            return [
                {
                    'word': word,
                    'weight': weight,
                    'category': self._classify_keyword(word),
                    'frequency': content.count(word)
                }
                for word, weight in sorted_keywords[:15]
            ]
            
        except Exception as e:
            logger.warning(f"关键词提取失败: {str(e)}")
            return []
    
    def _classify_keyword(self, word: str) -> str:
        """分类关键词"""
        # 简单的关键词分类
        if any(char in word for char in ['战', '攻', '守', '军', '兵']):
            return 'military'
        elif any(char in word for char in ['人', '者', '君', '王', '帝']):
            return 'person'
        elif any(char in word for char in ['国', '城', '地', '山', '河']):
            return 'place'
        elif any(char in word for char in ['法', '理', '道', '德', '义']):
            return 'concept'
        elif any(char in word for char in ['时', '年', '月', '日', '代']):
            return 'time'
        else:
            return 'general'
    
    def _extract_entities(self, content: str) -> Dict[str, List[Dict[str, Any]]]:
        """通用实体提取"""
        entities = defaultdict(list)
        
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if match not in [e['text'] for e in entities[entity_type]]:
                        entities[entity_type].append({
                            'text': match,
                            'frequency': content.count(match),
                            'confidence': 0.8  # 基础置信度
                        })
        
        # 按频率排序
        for entity_type in entities:
            entities[entity_type] = sorted(
                entities[entity_type], 
                key=lambda x: x['frequency'], 
                reverse=True
            )[:10]  # 每类最多10个
        
        return dict(entities)
    
    def _cluster_content(self, documents: List[Any], keywords: List[Dict], entities: Dict) -> List[Dict[str, Any]]:
        """内容聚类"""
        clusters = defaultdict(list)
        
        # 基于关键词的简单聚类
        top_keywords = [kw['word'] for kw in keywords[:10]]
        
        for doc in documents:
            content = getattr(doc, 'page_content', str(doc))
            score = getattr(doc, 'metadata', {}).get('score', 0)
            
            # 找到最匹配的关键词
            best_keyword = None
            max_count = 0
            
            for keyword in top_keywords:
                count = content.count(keyword)
                if count > max_count:
                    max_count = count
                    best_keyword = keyword
            
            cluster_key = best_keyword if best_keyword else 'other'
            clusters[cluster_key].append({
                'content': content,
                'score': score,
                'length': len(content),
                'keyword_matches': max_count
            })
        
        # 转换为列表格式并计算统计信息
        cluster_list = []
        for cluster_name, docs in clusters.items():
            if len(docs) >= 2:  # 至少2个文档才形成聚类
                cluster_list.append({
                    'name': cluster_name,
                    'document_count': len(docs),
                    'avg_score': sum(d['score'] for d in docs) / len(docs),
                    'total_length': sum(d['length'] for d in docs),
                    'documents': docs[:5],  # 只保留前5个文档
                    'preview': docs[0]['content'][:200] + '...' if docs else ''
                })
        
        # 按文档数量排序
        return sorted(cluster_list, key=lambda x: x['document_count'], reverse=True)
    
    def _generate_universal_summary(self, basic_stats: Dict, keywords: List[Dict], 
                                  entities: Dict, clusters: List[Dict], 
                                  query: str, analysis_type: str) -> Dict[str, Any]:
        """生成通用总结"""
        
        # 动态生成标题
        main_keywords = [kw['word'] for kw in keywords[:3]]
        title = f"关于「{query}」的内容分析"
        if main_keywords:
            title += f" - 主要涉及：{', '.join(main_keywords)}"
        
        summary = {
            'title': title,
            'analysis_type': analysis_type,
            'overview': {
                'total_segments': basic_stats['total_documents'],
                'total_characters': basic_stats['total_characters'],
                'avg_relevance_score': round(basic_stats['avg_score'], 3),
                'main_themes_count': len(keywords),
                'clusters_count': len(clusters)
            },
            'key_findings': self._generate_key_findings(keywords, entities, clusters),
            'main_themes': keywords[:10],
            'entity_summary': self._summarize_entities(entities),
            'content_clusters': clusters[:5],
            'recommendations': self._generate_recommendations(basic_stats, keywords, clusters)
        }
        
        return summary
    
    def _generate_key_findings(self, keywords: List[Dict], entities: Dict, clusters: List[Dict]) -> List[str]:
        """生成关键发现"""
        findings = []
        
        # 基于关键词的发现
        if keywords:
            top_keyword = keywords[0]
            findings.append(f"最重要的主题是「{top_keyword['word']}」，出现{top_keyword['frequency']}次")
        
        # 基于实体的发现
        for entity_type, entity_list in entities.items():
            if entity_list:
                type_name = {
                    'person': '人物',
                    'place': '地点',
                    'time': '时间',
                    'event': '事件',
                    'concept': '概念'
                }.get(entity_type, entity_type)
                
                top_entity = entity_list[0]
                findings.append(f"主要{type_name}：{top_entity['text']}（出现{top_entity['frequency']}次）")
        
        # 基于聚类的发现
        if clusters:
            findings.append(f"内容可以分为{len(clusters)}个主要主题类别")
            top_cluster = clusters[0]
            findings.append(f"最大的主题类别是「{top_cluster['name']}」，包含{top_cluster['document_count']}个相关片段")
        
        return findings[:5]  # 最多5个关键发现
    
    def _summarize_entities(self, entities: Dict) -> Dict[str, Any]:
        """总结实体信息"""
        summary = {}
        
        type_names = {
            'person': '人物',
            'place': '地点', 
            'time': '时间',
            'event': '事件',
            'concept': '概念'
        }
        
        for entity_type, entity_list in entities.items():
            if entity_list:
                summary[type_names.get(entity_type, entity_type)] = {
                    'count': len(entity_list),
                    'top_entities': [e['text'] for e in entity_list[:5]],
                    'most_frequent': entity_list[0]['text'] if entity_list else None
                }
        
        return summary
    
    def _generate_recommendations(self, basic_stats: Dict, keywords: List[Dict], clusters: List[Dict]) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 基于结果质量的建议
        if basic_stats['avg_score'] < 0.4:
            recommendations.append("建议降低搜索阈值以获取更多相关内容")
        elif basic_stats['avg_score'] > 0.8:
            recommendations.append("搜索结果质量很高，可以考虑提高阈值以获得更精确的结果")
        
        # 基于内容数量的建议
        if basic_stats['total_documents'] < 10:
            recommendations.append("结果数量较少，建议扩大搜索范围或降低相关性阈值")
        elif basic_stats['total_documents'] > 200:
            recommendations.append("结果数量较多，建议提高相关性阈值或细化搜索条件")
        
        # 基于聚类的建议
        if len(clusters) > 5:
            recommendations.append("内容主题较为分散，建议针对特定主题进行深入分析")
        elif len(clusters) <= 2:
            recommendations.append("内容主题较为集中，可以进行更深入的细节分析")
        
        return recommendations


def test_universal_analyzer():
    """测试通用分析器"""
    analyzer = UniversalDocumentAnalyzer()
    
    # 模拟文档数据
    class MockDoc:
        def __init__(self, content, score):
            self.page_content = content
            self.metadata = {'score': score}
    
    test_docs = [
        MockDoc("刘备、关羽、张飞三人结义，共同创业建立蜀汉政权", 0.8),
        MockDoc("诸葛亮在隆中对中提出了三分天下的战略构想", 0.7),
        MockDoc("曹操在官渡之战中击败袁绍，统一北方", 0.9),
        MockDoc("孙权在江东建立了稳固的根据地", 0.6),
    ]
    
    result = analyzer.analyze_documents(test_docs, "三国历史人物", "general")
    
    print("通用分析结果:")
    print(f"标题: {result['structured_output']['title']}")
    print(f"关键发现: {result['structured_output']['key_findings']}")
    print(f"主要主题: {[kw['word'] for kw in result['keywords'][:5]]}")


if __name__ == "__main__":
    test_universal_analyzer()
