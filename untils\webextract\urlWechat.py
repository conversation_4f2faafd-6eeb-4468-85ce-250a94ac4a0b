import re
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, parse_qs, unquote
from typing import Optional
from .webtype import WeChatArticleInfo

class WeChatExtractor:
    """微信公众号内容提取器"""
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def is_wechat_url(self, url: str) -> bool:
        """判断是否为微信公众号文章链接"""
        return 'mp.weixin.qq.com' in url
    
    def get_article_info(self, url: str) -> Optional[WeChatArticleInfo]:
        """获取微信公众号文章信息"""
        try:
            # 清理URL参数，保留必要参数
            cleaned_url = self._clean_wechat_url(url)
            
            print(f"正在获取微信文章: {cleaned_url}")
            
            # 发送请求获取页面内容
            response = self.session.get(cleaned_url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取标题
            title = self._extract_title(soup)
            
            # 提取正文内容
            content = self._extract_content(soup)
            
            # 提取作者信息
            author = self._extract_author(soup)
            
            # 提取公众号名称
            account_name = self._extract_account_name(soup)
            
            # 提取发布时间
            publish_time = self._extract_publish_time(soup)
            
            # 提取阅读数和点赞数（这些可能需要JavaScript渲染，可能获取不到）
            read_count = self._extract_read_count(soup, response.text)
            like_count = self._extract_like_count(soup, response.text)
            
            # 提取封面图片
            cover_image = self._extract_cover_image(soup)
            
            if not title or not content:
                print("未能提取到文章标题或内容")
                return None
            
            return WeChatArticleInfo(
                title=title,
                content=content,
                author=author,
                account_name=account_name,
                publish_time=publish_time,
                read_count=read_count,
                like_count=like_count,
                url=cleaned_url,
                cover_image=cover_image
            )
            
        except Exception as e:
            print(f"获取微信文章时出错: {e}")
            return None
    
    def _clean_wechat_url(self, url: str) -> str:
        """清理微信URL，保留必要参数"""
        try:
            # 解码URL
            url = unquote(url)
            
            # 提取必要的参数
            parsed = urlparse(url)
            params = parse_qs(parsed.query)
            
            # 保留必要参数
            essential_params = ['__biz', 'mid', 'idx', 'sn']
            clean_params = {}
            
            for param in essential_params:
                if param in params:
                    clean_params[param] = params[param][0]
            
            # 重构URL
            if clean_params:
                param_str = '&'.join([f"{k}={v}" for k, v in clean_params.items()])
                return f"https://mp.weixin.qq.com/s?{param_str}"
            
            return url
            
        except Exception:
            return url
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """提取文章标题"""
        # 方法1: meta标签
        title_meta = soup.find('meta', {'property': 'og:title'})
        if title_meta and title_meta.get('content'):
            return title_meta['content'].strip()
        
        # 方法2: 页面title标签
        title_tag = soup.find('title')
        if title_tag:
            return title_tag.get_text().strip()
        
        # 方法3: h1标签
        h1_tag = soup.find('h1')
        if h1_tag:
            return h1_tag.get_text().strip()
        
        # 方法4: 特定class的div
        title_div = soup.find('div', {'class': 'rich_media_title'})
        if title_div:
            return title_div.get_text().strip()
        
        return ""
    
    def _extract_content(self, soup: BeautifulSoup) -> str:
        """提取文章正文内容"""
        content_parts = []
        
        # 查找正文容器
        content_containers = [
            soup.find('div', {'class': 'rich_media_content'}),
            soup.find('div', {'id': 'js_content'}),
            soup.find('div', {'class': 'msg_content'}),
        ]
        
        for container in content_containers:
            if container:
                # 移除脚本和样式标签
                for script in container.find_all(['script', 'style']):
                    script.decompose()
                
                # 提取文本内容
                text_content = self._extract_text_from_element(container)
                if text_content:
                    content_parts.append(text_content)
                break
        
        return '\n\n'.join(content_parts) if content_parts else ""
    
    def _extract_text_from_element(self, element) -> str:
        """从HTML元素中提取纯文本"""
        text_parts = []
        
        for child in element.children:
            if hasattr(child, 'name'):
                if child.name in ['p', 'div', 'section']:
                    text = child.get_text().strip()
                    if text:
                        text_parts.append(text)
                elif child.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                    text = child.get_text().strip()
                    if text:
                        text_parts.append(f"\n=== {text} ===\n")
                elif child.name == 'br':
                    text_parts.append('\n')
                else:
                    # 递归处理其他元素
                    sub_text = self._extract_text_from_element(child)
                    if sub_text:
                        text_parts.append(sub_text)
            else:
                # 文本节点
                text = str(child).strip()
                if text:
                    text_parts.append(text)
        
        return ' '.join(text_parts)
    
    def _extract_author(self, soup: BeautifulSoup) -> str:
        """提取作者信息"""
        # 查找作者信息的常见位置
        author_selectors = [
            'meta[name="author"]',
            '.rich_media_meta_text',
            '.profile_nickname',
            '#js_author_name',
        ]
        
        for selector in author_selectors:
            element = soup.select_one(selector)
            if element:
                if element.name == 'meta':
                    return element.get('content', '').strip()
                else:
                    return element.get_text().strip()
        
        return ""
    
    def _extract_account_name(self, soup: BeautifulSoup) -> str:
        """提取公众号名称"""
        # 查找公众号名称
        account_selectors = [
            'meta[property="og:site_name"]',
            '.profile_nickname',
            '#js_name',
            '.rich_media_meta_nickname',
        ]
        
        for selector in account_selectors:
            element = soup.select_one(selector)
            if element:
                if element.name == 'meta':
                    return element.get('content', '').strip()
                else:
                    return element.get_text().strip()
        
        return ""
    
    def _extract_publish_time(self, soup: BeautifulSoup) -> str:
        """提取发布时间"""
        # 查找发布时间
        time_selectors = [
            'meta[property="og:updated_time"]',
            '.rich_media_meta_text',
            '#publish_time',
            '.time',
        ]
        
        for selector in time_selectors:
            element = soup.select_one(selector)
            if element:
                if element.name == 'meta':
                    return element.get('content', '').strip()
                else:
                    text = element.get_text().strip()
                    # 提取时间格式的文本
                    time_match = re.search(r'\d{4}-\d{2}-\d{2}|\d{4}年\d{1,2}月\d{1,2}日', text)
                    if time_match:
                        return time_match.group()
                    return text
        
        return ""
    
    def _extract_read_count(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取阅读数（可能需要JavaScript渲染）"""
        # 尝试从JavaScript变量中提取
        read_count_pattern = r'read_num["\']?\s*[:=]\s*["\']?(\d+)'
        match = re.search(read_count_pattern, html_text)
        if match:
            return match.group(1)
        
        # 尝试从HTML元素中提取
        read_elements = soup.find_all(string=re.compile(r'阅读\s*\d+'))
        for element in read_elements:
            num_match = re.search(r'\d+', element)
            if num_match:
                return num_match.group()
        
        return ""
    
    def _extract_like_count(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取点赞数"""
        # 尝试从JavaScript变量中提取
        like_count_pattern = r'like_num["\']?\s*[:=]\s*["\']?(\d+)'
        match = re.search(like_count_pattern, html_text)
        if match:
            return match.group(1)
        
        return ""
    
    def _extract_cover_image(self, soup: BeautifulSoup) -> str:
        """提取封面图片"""
        # 查找封面图片
        img_selectors = [
            'meta[property="og:image"]',
            '.rich_media_thumb',
            'img[data-src]',
        ]
        
        for selector in img_selectors:
            element = soup.select_one(selector)
            if element:
                if element.name == 'meta':
                    return element.get('content', '').strip()
                else:
                    return element.get('data-src') or element.get('src', '').strip()
        
        return ""
    
