#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用全文检索功能示例

展示如何使用完全通用的全文检索功能分析任何类型的文档
不再依赖硬编码的领域知识
"""

import requests
import json
from typing import Dict, List, Any


class UniversalFullTextClient:
    """通用全文检索客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
    
    def analyze_any_document(self, files: List[str], query: str, **kwargs) -> Dict[str, Any]:
        """
        分析任何类型的文档
        
        Args:
            files: 文件列表
            query: 分析查询
            **kwargs: 其他参数
            
        Returns:
            分析结果
        """
        url = f"{self.base_url}/datasheet/fulltextsearch/"
        
        payload = {
            'text': query,
            'selected_files': files,
            'database': kwargs.get('database', ['default']),
            'collection': kwargs.get('collection', ['chzngk228']),
            'analysis_type': kwargs.get('analysis_type', 'general'),
            'batch_size': kwargs.get('batch_size', 50),
            'min_score': kwargs.get('min_score', 0.2),
            'max_results': kwargs.get('max_results', 300),
            'enable_deep_analysis': kwargs.get('enable_deep_analysis', True)
        }
        
        try:
            response = requests.post(url, json=payload, timeout=60)
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}: {response.text}'}
        except Exception as e:
            return {'error': str(e)}


def demo_novel_analysis():
    """演示小说分析 - 完全通用，不限于三国演义"""
    client = UniversalFullTextClient()
    
    print("📚 通用小说分析演示")
    print("=" * 50)
    
    # 可以分析任何小说
    test_cases = [
        {
            'name': '古典小说战役分析',
            'files': ['三国演义.txt'],
            'query': '战争 战役 军事 策略',
            'description': '分析古典小说中的军事内容'
        },
        {
            'name': '神话小说冒险分析', 
            'files': ['西游记.txt'],
            'query': '冒险 妖怪 法术 神通',
            'description': '分析神话小说中的冒险元素'
        },
        {
            'name': '现代小说情感分析',
            'files': ['现代小说.txt'],
            'query': '爱情 友情 亲情 情感',
            'description': '分析现代小说中的情感主题'
        }
    ]
    
    for case in test_cases:
        print(f"\n🔍 {case['name']}")
        print(f"📝 描述: {case['description']}")
        print(f"📁 文件: {case['files']}")
        print(f"🎯 查询: {case['query']}")
        
        result = client.analyze_any_document(
            files=case['files'],
            query=case['query'],
            min_score=0.25,
            max_results=200
        )
        
        if 'error' in result:
            print(f"❌ 分析失败: {result['error']}")
            continue
        
        data = result.get('data', {})
        
        # 显示基础统计
        print(f"\n📊 分析结果:")
        print(f"   - 相关片段: {data.get('total_segments', 0)}个")
        print(f"   - 分析方法: {data.get('analysis_method', 'unknown')}")
        
        # 显示结构化总结
        if 'structured_summary' in data:
            summary = data['structured_summary']
            print(f"   - 标题: {summary.get('title', 'N/A')}")
            
            key_findings = summary.get('key_findings', [])
            if key_findings:
                print(f"   - 关键发现:")
                for finding in key_findings[:3]:
                    print(f"     • {finding}")
        
        # 显示主要主题
        if 'key_themes' in data:
            themes = data['key_themes'][:5]
            print(f"   - 主要主题: {[t.get('word', '') for t in themes]}")
        
        print("-" * 40)


def demo_technical_document_analysis():
    """演示技术文档分析"""
    client = UniversalFullTextClient()
    
    print("\n💻 通用技术文档分析演示")
    print("=" * 50)
    
    tech_cases = [
        {
            'name': 'API文档分析',
            'files': ['API文档.pdf'],
            'query': 'API 接口 参数 返回值 错误码',
            'analysis_type': 'technical'
        },
        {
            'name': '系统架构分析',
            'files': ['系统设计.docx'],
            'query': '架构 模块 组件 服务 数据库',
            'analysis_type': 'technical'
        },
        {
            'name': '用户手册分析',
            'files': ['用户手册.txt'],
            'query': '操作 步骤 功能 使用方法',
            'analysis_type': 'manual'
        }
    ]
    
    for case in tech_cases:
        print(f"\n🔧 {case['name']}")
        
        result = client.analyze_any_document(
            files=case['files'],
            query=case['query'],
            analysis_type=case['analysis_type'],
            min_score=0.3,
            max_results=150
        )
        
        if 'error' in result:
            print(f"❌ 分析失败: {result['error']}")
            continue
        
        data = result.get('data', {})
        
        # 显示实体信息
        if 'entities' in data:
            entities = data['entities']
            print(f"📋 识别的实体:")
            for entity_type, entity_list in entities.items():
                if entity_list:
                    top_entities = [e['text'] for e in entity_list[:3]]
                    print(f"   - {entity_type}: {', '.join(top_entities)}")
        
        # 显示内容聚类
        if 'content_clusters' in data:
            clusters = data['content_clusters'][:3]
            print(f"🎭 内容聚类:")
            for cluster in clusters:
                print(f"   - {cluster.get('name', '未知')}: {cluster.get('document_count', 0)}个片段")


def demo_legal_document_analysis():
    """演示法律文档分析"""
    client = UniversalFullTextClient()
    
    print("\n⚖️ 通用法律文档分析演示")
    print("=" * 50)
    
    legal_cases = [
        {
            'name': '交通法规分析',
            'files': ['道路交通.txt'],
            'query': '违法 处罚 罚款 扣分 责任',
            'focus': '违法行为和处罚措施'
        },
        {
            'name': '合同条款分析',
            'files': ['合同模板.docx'],
            'query': '条款 义务 权利 责任 违约',
            'focus': '合同权利义务关系'
        },
        {
            'name': '法律程序分析',
            'files': ['诉讼程序.pdf'],
            'query': '程序 流程 步骤 期限 要求',
            'focus': '法律程序和时限'
        }
    ]
    
    for case in legal_cases:
        print(f"\n📜 {case['name']}")
        print(f"🎯 关注点: {case['focus']}")
        
        result = client.analyze_any_document(
            files=case['files'],
            query=case['query'],
            analysis_type='legal',
            min_score=0.25,
            max_results=250
        )
        
        if 'error' in result:
            print(f"❌ 分析失败: {result['error']}")
            continue
        
        data = result.get('data', {})
        
        # 显示基础统计
        basic_stats = data.get('basic_statistics', {})
        print(f"📊 统计信息:")
        print(f"   - 文档数: {basic_stats.get('total_documents', 0)}")
        print(f"   - 平均相关度: {basic_stats.get('average_score', 0):.3f}")
        
        # 显示内容预览
        if 'content_previews' in data:
            previews = data['content_previews'][:2]
            print(f"📄 内容预览:")
            for preview in previews:
                print(f"   片段{preview['index']}: {preview['preview'][:100]}...")


def demo_comparative_analysis():
    """演示对比分析 - 完全通用"""
    client = UniversalFullTextClient()
    
    print("\n🔄 通用对比分析演示")
    print("=" * 50)
    
    # 可以对比任何类型的文档
    comparison_cases = [
        {
            'name': '古典小说对比',
            'files': ['三国演义.txt', '西游记.txt'],
            'query': '英雄 智慧 冒险 成长',
            'description': '对比不同古典小说的主题'
        },
        {
            'name': '技术文档对比',
            'files': ['前端文档.md', '后端文档.md'],
            'query': '架构 设计 实现 优化',
            'description': '对比前后端技术方案'
        },
        {
            'name': '政策文件对比',
            'files': ['政策A.pdf', '政策B.pdf'],
            'query': '规定 要求 标准 流程',
            'description': '对比不同政策的要求'
        }
    ]
    
    for case in comparison_cases:
        print(f"\n🆚 {case['name']}")
        print(f"📝 {case['description']}")
        
        result = client.analyze_any_document(
            files=case['files'],
            query=case['query'],
            analysis_type='comparative',
            min_score=0.2,
            max_results=400
        )
        
        if 'error' in result:
            print(f"❌ 对比失败: {result['error']}")
            continue
        
        data = result.get('data', {})
        print(f"📊 对比结果: 找到{data.get('total_segments', 0)}个相关片段")
        
        # 显示文件分布（如果有的话）
        if 'content_clusters' in data:
            clusters = data['content_clusters']
            print(f"🎭 主题分布:")
            for cluster in clusters[:3]:
                print(f"   - {cluster.get('name', '未知')}: {cluster.get('document_count', 0)}个片段")


def main():
    """主函数"""
    print("🚀 通用全文检索功能演示")
    print("✨ 完全通用，适用于任何类型的文档分析")
    print("🎯 不再依赖硬编码的领域知识")
    
    try:
        # 演示各种类型的文档分析
        demo_novel_analysis()
        demo_technical_document_analysis()
        demo_legal_document_analysis()
        demo_comparative_analysis()
        
        print("\n✅ 演示完成!")
        print("\n💡 通用全文检索的优势:")
        print("   ✓ 适用于任何领域的文档")
        print("   ✓ 自动识别主题和实体")
        print("   ✓ 智能聚类和分析")
        print("   ✓ 无需预设领域知识")
        print("   ✓ 灵活的参数配置")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {str(e)}")


if __name__ == '__main__':
    main()
