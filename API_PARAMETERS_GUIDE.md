# 全文检索API参数说明文档

## 📡 API端点

```
POST /datasheet/fulltextsearch/
```

## 📋 请求参数详解

### 🔴 必需参数

#### `text` (string, 必需)
- **描述**: 搜索查询文本，用于指定要分析的主题或内容
- **示例**: 
  ```json
  "text": "战争 战役 军事 策略"
  "text": "API 接口 参数 返回值"
  "text": "违法 处罚 罚款 责任"
  ```
- **建议**: 
  - 使用2-8个相关关键词，用空格分隔
  - 避免过于宽泛的词汇（如"内容"、"信息"）
  - 可以包含同义词以提高召回率

#### `selected_files` (array, 必需)
- **描述**: 要分析的目标文件列表
- **格式**: 字符串数组
- **示例**:
  ```json
  "selected_files": ["三国演义.txt"]
  "selected_files": ["API文档.pdf", "用户手册.docx"]
  "selected_files": ["道路交通.txt", "刑法条文.pdf"]
  ```
- **注意事项**:
  - 文件名必须与知识库中的完全匹配
  - 支持多种格式：.txt, .pdf, .docx, .md等
  - 建议单次分析不超过5个文件以保证性能

### 🟡 可选参数

#### `database` (array, 可选)
- **描述**: 数据库名称列表
- **默认值**: `["default"]`
- **示例**: `"database": ["default", "backup"]`

#### `collection` (array, 可选)
- **描述**: 集合名称列表
- **默认值**: `["chzngk228"]`
- **示例**: `"collection": ["main_collection", "archive"]`

#### `analysis_type` (string, 可选)
- **描述**: 分析类型，影响分析策略和输出格式
- **默认值**: `"general"`
- **可选值**:
  - `"general"` - 通用分析，适用于大多数场景
  - `"technical"` - 技术文档分析，重点识别技术概念
  - `"legal"` - 法律文档分析，重点识别法条和程序
  - `"comparative"` - 对比分析，适用于多文档比较
  - `"academic"` - 学术文档分析，重点识别理论概念
- **示例**: `"analysis_type": "technical"`

#### `batch_size` (integer, 可选)
- **描述**: 每批检索的文档数量，影响检索的深度和性能
- **默认值**: `50`
- **取值范围**: 10-200
- **建议**:
  - 小文档或精确查找: `20-30`
  - 标准分析: `50-80`
  - 大文档全面分析: `100-150`
- **示例**: `"batch_size": 80`

#### `min_score` (float, 可选)
- **描述**: 最低相关度分数阈值，过滤低质量结果
- **默认值**: `0.2`
- **取值范围**: 0.0-1.0
- **建议**:
  - 严格筛选: `0.4-0.6`
  - 平衡质量和覆盖: `0.2-0.3`
  - 最大覆盖: `0.1-0.15`
- **示例**: `"min_score": 0.25`

#### `max_results` (integer, 可选)
- **描述**: 最大返回结果数量，控制分析的广度
- **默认值**: `300`
- **取值范围**: 50-1000
- **建议**:
  - 快速概览: `50-100`
  - 标准分析: `200-400`
  - 深度分析: `500-800`
- **示例**: `"max_results": 250`

#### `enable_deep_analysis` (boolean, 可选)
- **描述**: 是否启用深度分析功能
- **默认值**: `true`
- **说明**:
  - `true`: 使用通用分析器进行深度主题和实体分析
  - `false`: 仅进行基础统计分析，响应更快
- **示例**: `"enable_deep_analysis": true`

## 📊 参数组合建议

### 🎯 场景1: 快速预览
```json
{
    "text": "主要内容概述",
    "selected_files": ["目标文档.txt"],
    "batch_size": 30,
    "min_score": 0.3,
    "max_results": 50,
    "enable_deep_analysis": false
}
```

### 🔍 场景2: 标准分析
```json
{
    "text": "详细主题分析",
    "selected_files": ["文档1.pdf", "文档2.docx"],
    "analysis_type": "general",
    "batch_size": 50,
    "min_score": 0.2,
    "max_results": 300,
    "enable_deep_analysis": true
}
```

### 🎭 场景3: 深度研究
```json
{
    "text": "全面主题挖掘",
    "selected_files": ["研究文档.pdf"],
    "analysis_type": "academic",
    "batch_size": 100,
    "min_score": 0.15,
    "max_results": 500,
    "enable_deep_analysis": true
}
```

### 🆚 场景4: 对比分析
```json
{
    "text": "共同主题对比",
    "selected_files": ["文档A.txt", "文档B.txt", "文档C.pdf"],
    "analysis_type": "comparative",
    "batch_size": 80,
    "min_score": 0.2,
    "max_results": 600,
    "enable_deep_analysis": true
}
```

## ⚙️ 性能优化参数

### 🚀 高性能配置（快速响应）
```json
{
    "batch_size": 30,
    "min_score": 0.4,
    "max_results": 100,
    "enable_deep_analysis": false
}
```

### 🎯 高质量配置（精确结果）
```json
{
    "batch_size": 40,
    "min_score": 0.35,
    "max_results": 200,
    "enable_deep_analysis": true
}
```

### 📊 高覆盖配置（全面分析）
```json
{
    "batch_size": 100,
    "min_score": 0.15,
    "max_results": 800,
    "enable_deep_analysis": true
}
```

## 🔧 参数调优指南

### 根据文档大小调整
```python
# 小文档 (< 100KB)
batch_size = 20-40
max_results = 100-200

# 中等文档 (100KB - 1MB)  
batch_size = 50-80
max_results = 200-400

# 大文档 (> 1MB)
batch_size = 80-150
max_results = 400-800
```

### 根据查询复杂度调整
```python
# 简单查询 (1-2个关键词)
min_score = 0.15-0.25
max_results = 300-500

# 复杂查询 (3-5个关键词)
min_score = 0.2-0.3
max_results = 200-400

# 精确查询 (专业术语)
min_score = 0.3-0.5
max_results = 100-300
```

### 根据分析目的调整
```python
# 快速了解
enable_deep_analysis = False
batch_size = 30
max_results = 100

# 主题研究
enable_deep_analysis = True
batch_size = 80
max_results = 400

# 全面分析
enable_deep_analysis = True
batch_size = 120
max_results = 600
```

## ⚠️ 注意事项

### 参数限制
- `batch_size`: 建议不超过200，过大可能导致内存问题
- `max_results`: 建议不超过1000，过大影响响应时间
- `min_score`: 不建议低于0.1，可能产生大量噪音

### 性能考虑
- 大文档 + 高`max_results` + 深度分析 = 较长响应时间
- 多文件分析时建议适当降低`batch_size`
- `enable_deep_analysis=false`可显著提升响应速度

### 质量平衡
- 降低`min_score`增加覆盖面但可能引入噪音
- 提高`min_score`保证质量但可能遗漏相关内容
- `batch_size`过小可能遗漏重要信息

## 📝 完整请求示例

```json
{
    "text": "人工智能 机器学习 深度学习 算法",
    "selected_files": ["AI技术报告.pdf", "机器学习指南.docx"],
    "database": ["default"],
    "collection": ["chzngk228"],
    "analysis_type": "technical",
    "batch_size": 60,
    "min_score": 0.25,
    "max_results": 350,
    "enable_deep_analysis": true
}
```

## 🎯 最佳实践

1. **先用默认参数测试**，再根据结果调整
2. **根据文档类型选择合适的`analysis_type`**
3. **平衡`min_score`和`max_results`**以获得最佳效果
4. **大文档分析时适当增加`batch_size`**
5. **多文档对比时使用`comparative`类型**
6. **性能要求高时关闭`enable_deep_analysis`**
