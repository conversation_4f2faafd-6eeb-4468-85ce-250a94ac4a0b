from pptx import Presentation


from loguru import logger
class PptParser:
    def __init__(self) -> None:
        pass

    def parse(self, file_path):
        raise NotImplementedError


class PptxParser:
    def parse(self, file_path):
        try:
            prs = Presentation(file_path)
            text = ""
            for slide in prs.slides:
                for shape in slide.shapes:
                    if shape.has_text_frame:
                        for paragraph in shape.text_frame.paragraphs:
                            for run in paragraph.runs:
                                text += run.text
                                text += " "  # 添加空格分隔段落
                text += "\n"  # 添加换行分隔幻灯片
            return text
        except Exception as e:
                logger.error(f"Failed to parse the .pptx file: {e}")
                return None
