import fitz,base64,io,tqdm  # 导入tqdm库
from openai import OpenAI
from PIL import Image
from loguru import logger
import os,time,json
import concurrent.futures
import threading
EXT_PROMPT="# 角色\
你是一个高效精准的数据处理助手，专注于从图片中提取内容。\
## 限制:\
- 仅处理与图片中相关的，不得涉及与当前图片无关的其他方面。\
- 确保完整反映图片中的信息。\
- 严禁出现'图片包含，图片展示'等字眼"
class VBllmBase():
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 设置OpenAI API密钥和基础URL
        # openai.api_key = os.getenv("OPENAI_API_KEY")
        # openai.base_url = os.getenv("OPENAI_BASE_URL")
        
    def encode_image(self,image):
        buffered = io.BytesIO()
        image.save(buffered, format="PNG")
        return base64.b64encode(buffered.getvalue()).decode('utf-8')
    
    def pdf_to_images(self,pdf_path,pnum=None):
        doc = fitz.open(pdf_path)
        images = []
        
        if pnum is None:
            pnum = range(len(doc))
        for page_num in pnum:
            page = doc.load_page(page_num)
            pix = page.get_pixmap()
            img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
            images.append(img)
        return images
    
    # def process_with_gpt4o(self,images):
    #     start_time = time.time()  # 记录开始时间
    #     combined_text = []
    #     errList = []
    #     for i, image in enumerate(tqdm.tqdm(images, desc="处理进度", unit="张")):
    #         base64_image = self.encode_image(image)
    #         try:
    #             response = openai.ChatCompletion.create(
    #                 temperature=0,
    #                 model="gpt-4o",
    #                 messages=[
    #                     {
    #                         "role": "user",
    #                         "content": [
    #                             # {"type": "text", "text": "You are a powerful data extraction assistant. Please extract the information in the picture below intact. The key thing you have to do is to re-format the information in the picture into a neat format and then output it into a text file. Please do not understand it as refining and summarizing. I need the information intact and output it in Chinese, with some prefaces to your answers in the middle, such as: (This information can be saved into a text file named extracted_information.txt. Here is the text you need to include in the file:) and (我已提取并重新格式化图片中的信息如下：) are considered spam, please delete these prefaces from the text"},
    #                             {"type": "text", "text": EXT_PROMPT},
    #                             {
    #                                 "type": "image_url",
    #                                 "image_url": {
    #                                     "url": f"data:image/png;base64,{base64_image}"
    #                                 }
    #                             }
    #                         ]
    #                     }
    #                 ],
    #             )
    #             content = response.choices[0].message['content'] 
    #             combined_text.append({"index":i,"page_content": content,"size":len(content)})
    #             # print(f'正在解析第{int(i)+1:03d}页')
    #         except Exception as e:
    #             logger.error(f"处理第 {i+1} 页图像时出错: {e}")
    #             errList.append(i)
    #     end_time=time.time()
    #     logger.info(f"共耗时{end_time-start_time:.2f}秒")
    #     return combined_text,errList
    def extract_response_content(response):
        try:
            # 提取第一个 choice 的 message 内容
            print(response.choices[0].message,'------------')
            content = response.choices[0].message.content
            return content
        except (IndexError, AttributeError) as e:
            # 如果提取失败，记录错误日志并返回空字符串
            logger.error(f"提取 response 内容失败: {e}")
            return ""
    def process_image(self,image,index):
        base64_image = self.encode_image(image)
        client = OpenAI(base_url=os.getenv("OPENAI_BASE_URL"),api_key=os.getenv("OPENAI_API_KEY"))
        try:
            response = client.chat.completions.create(
                temperature=0,
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": EXT_PROMPT},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
            )
            # 提取内容
            
            content = response.choices[0].message.content
            return {"index":index,"page_content": content,"size":len(content),'prompt_tokens':response.usage.prompt_tokens,'completion_tokens':response.usage.completion_tokens,'total_tokens':response.usage.total_tokens}
            # print(f'正在解析第{int(i)+1:03d}页')
        except Exception as e:
            # logger.error(f"处理第 {index+1} 页图像时出错: {e}")
            return {'index':index,'error':True,'page_content':'第{}页数据提取失败'.format(index+1)}

    def process_image_server(self, image, index=0):
        import requests
        # 将图像编码为 Base64
        base64_image = self.encode_image(image)
        # Node.js 服务的 URL
        service_url = "http://218.75.223.27:8080/proxy/api/files/out/pdf"

        try:
            # 发送 POST 请求到 Node.js 服务
            res = requests.post(
                service_url,
                json={
                    "base64_image":base64_image,  # 发送 Base64 编码的图像
                },
                timeout=60  # 设置超时时间
            )
            # 检查响应状态码
            if res.status_code == 200:
                # 解析响应数据
                # 解析响应数据# 确保使用 UTF-8 解码
                res.encoding = 'utf-8'
                response = res.json()
                # 提取内容
                content = response.get("choices", [{}])[0].get("message", {}).get("content", "")
                prompt_tokens = response.get("usage", {}).get("prompt_tokens", 0)
                completion_tokens = response.get("usage", {}).get("completion_tokens", 0)
                total_tokens = response.get("usage", {}).get("total_tokens", 0)

                return {
                    "index": index,
                    "page_content": content,
                    "size": len(content),
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": total_tokens
                }
            else:
                logger.error(f"Node.js 服务返回错误: {res.status_code}, {res.text}")
                return {"index": index, "error": True, "page_content": f"第 {index + 1} 页数据提取失败"
                }

        except requests.exceptions.RequestException as e:
            # 捕获请求异常
            logger.error(f"请求 Node.js 服务时出错: {e}")
            return {
                "index": index, "error": True, "page_content": f"第 {index + 1} 页数据提取失败"
            }
    # 并发，一次性返回所有结果
    def process_with_gpt4o_concurrent(self, images):
        start_time = time.time()
        combined_text = []
        errList = []
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future_to_image = {executor.submit(self.process_image, image, i): i for i, image in enumerate(images)}
            for future in tqdm.tqdm(concurrent.futures.as_completed(future_to_image), total=len(images), desc="处理进度"):
                result = future.result()
                if result and 'error' not in result:
                    combined_text.append(result)
                elif result:
                    errList.append(result['index'])
                    logger.warning(f"第 {result['index'] + 1} 页需要重试。错误信息：{result['error']}")
                    # Optionally retry failed images immediately or collect them for a retry batch
        end_time = time.time()
        logger.info(f"共耗时{end_time - start_time:.2f}秒")
        return combined_text,errList
    
    # 并发执行无法实现终止
    def process_with_gpt4o_streaming(self, images):
        logger.info(f'开始提取：合计{len(images)}页')
        def response_stream():            
            with tqdm.tqdm(total=len(images), desc='SUCCESS', unit="页") as pbar_success, tqdm.tqdm(total=len(images), desc='FAILED', unit="页") as pbar_error:
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future_to_image = {executor.submit(self.process_image_server, image, i): i for i, image in enumerate(images)}
                    for future in concurrent.futures.as_completed(future_to_image):
                        try:
                            result = future.result(timeout=40)
                            if result and 'error' not in result:
                                pbar_success.update(1)
                                yield f"data: {json.dumps(result)}\n\n"
                            elif result:
                                pbar_error.update(1)
                                yield f"data: {json.dumps({'index': result['index'], 'error': True,'page_content':'第{}页数据提取失败'.format(result['index']+1)})}\n\n"
                        except concurrent.futures.TimeoutError:
                            logger.error(f'task {future_to_image[future]} time out.')
                        except Exception as e:
                            logger.error(f"处理第 {future_to_image[future]+1} 页图像:{e}")
                        # # Check if the client has disconnected
                        # # Check if the client has disconnected
                        # if hasattr(self.request, 'META') and self.request.META.get('HTTP_CONNECTION') == 'close':
                        #     logger.warning('客户端中断了连接')
                        #     # self.stop_event.set()
                        #     executor.shutdown(wait=False, cancel_futures=True)
                            # return
                yield "data: [DONE]\n\n"  # 流式传输结束标志
        yield from response_stream()
        
    # 按顺序执行可以实现终止
    def process_with_gpt4o_stream(self, images):
        def response_stream():
            yield "data:[START]\n\n"
            error_count = 0
            max_errors = int(len(images)*0.1)  # 最大允许错误次数
            stop_event = threading.Event()  # 用于标记停止状态
            
            def process_image_wrapper(image, index):
                if stop_event.is_set():
                    return {'index': index, 'error': True, 'page_content': '处理已终止'}
                return self.process_image(image, index)
            with concurrent.futures.ThreadPoolExecutor() as executor:
                
                future_to_image = []
                with tqdm.tqdm(total=len(images)) as pbar:
                    for i, image in enumerate(images):
                        
                        if error_count >= max_errors:
                            logger.error('错误次数超过最大限制，终止处理。')
                            stop_event.set()  # 设置停止状态
                            executor.shutdown(wait=False)  # 立即终止所有任务
                            yield "data: {\"error\": \"处理过程中错误次数过多，处理已终止。\"}\n\n"
                            return
                        future = executor.submit(process_image_wrapper, image, i)
                        future_to_image.append(future)
                        for future in concurrent.futures.as_completed(future_to_image):
                            future_to_image.remove(future)  # 处理后移除 future
                            try:
                                result = future.result(timeout=60)
                                if result and 'error' not in result:
                                    yield f"data: {json.dumps(result)}\n\n"
                                elif result:
                                    error_count += 1
                                    yield f"data: {json.dumps({'index': result['index'], 'error': True,'page_content':'第{}页数据提取失败'.format(result['index']+1)})}\n\n"
                            except concurrent.futures.TimeoutError:
                                error_count += 1
                                logger.error(f'task {future_to_image[future]} time out.')
                            except Exception as e:
                                error_count += 1
                                logger.error(f"处理第 {future_to_image[future]+1} 页图像:{e}")
                            finally:
                                pbar.update(1)
                        # # Check if the client has disconnected
                    # # Check if the client has disconnected
                    # if hasattr(self.request, 'META') and self.request.META.get('HTTP_CONNECTION') == 'close':
                    #     logger.warning('客户端中断了连接')
                    #     # self.stop_event.set()
                    #     executor.shutdown(wait=False, cancel_futures=True)
                        # return
            yield "data: [DONE]\n\n"  # 流式传输结束标志
        yield from response_stream()
    
    def get_text_from_file(self,file_path):
        text=[]
        with fitz.open(file_path) as doc:
            for i,page in enumerate(doc):
                content = page.get_text()
                text.append({"index":i,"page_content": content,"size":len(content)})
        
        return text