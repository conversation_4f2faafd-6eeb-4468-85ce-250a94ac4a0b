# 向量检索API使用指南

## 🎯 **三个接口概览**

### **1. vectorsearch/ - 基础快速检索**
- ⚡ **响应时间**: ~50ms
- 🎯 **适用场景**: 实时搜索、快速预览、简单查询
- 🔧 **功能特点**: 支持文件过滤、策略选择、统一格式

### **2. directsearch/ - 文件定向检索**  
- ⚡ **响应时间**: ~200ms
- 🎯 **适用场景**: 指定文件检索、精确匹配、中等复杂度查询
- 🔧 **功能特点**: 查询增强、回退策略、详细统计

### **3. entiresearch/ - 全文深度检索**
- ⚡ **响应时间**: ~800ms
- 🎯 **适用场景**: 大规模分析、主题挖掘、深度研究
- 🔧 **功能特点**: 智能分析、实体提取、结构化输出

## 📋 **接口参数对比**

### **通用参数 (所有接口都支持)**
```json
{
    "text": "查询文本",                    // 必需
    "database": ["default"],             // 可选，默认["default"]
    "collection": ["chzngk228"],         // 可选，默认["chzngk228"]
    "selected_files": ["文件1.txt"],     // 可选，文件过滤
    "top_k": 10,                        // 可选，返回结果数
    "score_threshold": 0.45             // 可选，相似度阈值
}
```

### **vectorsearch 专用参数**
```json
{
    "search_strategy": "simple"         // 可选: simple, fast, precise
}
```

### **directsearch 专用参数**
```json
{
    "search_strategy": "adaptive",      // 可选: adaptive, precise, broad, balanced
    "query_enhancement": true,          // 可选，是否启用查询增强
    "enable_fallback": true,            // 可选，是否启用回退策略
    "selected_file_ids": [1, 2, 3],     // 可选，文件ID过滤
    "uid_sheet": 123                    // 可选，工作表ID
}
```

### **entiresearch 专用参数**
```json
{
    "analysis_type": "general",         // 可选，分析类型
    "batch_size": 50,                   // 可选，批处理大小
    "min_score": 0.4,                   // 可选，最低分数
    "max_results": 1000,                // 可选，最大结果数
    "enable_deep_analysis": true        // 可选，是否深度分析
}
```

## 🚀 **使用场景指南**

### **场景1: 实时搜索框**
```javascript
// 用户在搜索框输入，需要实时反馈
fetch('/api/vectorsearch/', {
    method: 'POST',
    body: JSON.stringify({
        "text": "用户输入",
        "search_strategy": "simple",    // 最快响应
        "top_k": 5,                     // 少量结果
        "selected_files": ["重要文档.pdf"]
    })
})
```

**推荐**: `vectorsearch/` + `simple`策略

### **场景2: 文档精确查找**
```javascript
// 在特定文件中查找精确内容
fetch('/api/directsearch/', {
    method: 'POST',
    body: JSON.stringify({
        "text": "合同条款",
        "selected_files": ["合同.pdf", "协议.docx"],
        "query_enhancement": true,      // 启用查询增强
        "search_strategy": "precise",   // 精确策略
        "top_k": 20
    })
})
```

**推荐**: `directsearch/` + `precise`策略 + 查询增强

### **场景3: 主题深度分析**
```javascript
// 对大量文档进行主题分析
fetch('/api/entiresearch/', {
    method: 'POST',
    body: JSON.stringify({
        "text": "人工智能发展趋势",
        "selected_files": ["AI报告1.pdf", "AI报告2.pdf"],
        "analysis_type": "general",
        "max_results": 500,             // 大量结果
        "enable_deep_analysis": true    // 深度分析
    })
})
```

**推荐**: `entiresearch/` + 深度分析

### **场景4: 快速预览**
```javascript
// 快速预览文档内容
fetch('/api/vectorsearch/', {
    method: 'POST',
    body: JSON.stringify({
        "text": "项目概述",
        "search_strategy": "fast",      // 平衡速度和质量
        "top_k": 10
    })
})
```

**推荐**: `vectorsearch/` + `fast`策略

## 📊 **返回格式对比**

### **vectorsearch 返回格式**
```json
{
    "code": 200,
    "data": {
        "query": "用户查询",
        "results": [
            {
                "page_content": "文档内容",
                "metadata": {
                    "file_name": "文件名",
                    "score": 0.85
                }
            }
        ],
        "metadata": {
            "total_results": 10,
            "search_time": "50ms",
            "search_strategy": "simple",
            "files_searched": ["文件1.txt"],
            "interface": "vectorsearch"
        }
    }
}
```

### **directsearch 返回格式**
```json
{
    "code": 200,
    "data": {
        "query": "用户查询",
        "results": [...],
        "file_stats": {
            "文件1.txt": {
                "count": 5,
                "avg_score": 0.75
            }
        },
        "search_metadata": {
            "enhanced_queries": ["原查询", "增强查询1"],
            "search_strategy": "precise",
            "fallback_used": false
        }
    }
}
```

### **entiresearch 返回格式**
```json
{
    "code": 200,
    "data": {
        "query": "用户查询",
        "analysis_results": {
            "key_themes": ["主题1", "主题2"],
            "entities": ["实体1", "实体2"],
            "summary": "内容摘要"
        },
        "segments": [...],
        "statistics": {
            "total_segments": 100,
            "files_analyzed": 5
        }
    }
}
```

## ⚡ **性能优化建议**

### **1. 根据场景选择接口**
```
实时搜索     → vectorsearch (simple)
文件查找     → directsearch (precise)  
内容分析     → entiresearch (general)
快速预览     → vectorsearch (fast)
```

### **2. 合理设置参数**
```javascript
// 实时场景 - 追求速度
{
    "top_k": 5,                    // 少量结果
    "search_strategy": "simple",   // 最快策略
    "score_threshold": 0.5         // 较高阈值
}

// 精确场景 - 追求质量
{
    "top_k": 20,                   // 更多结果
    "search_strategy": "precise",  // 精确策略
    "score_threshold": 0.3,        // 较低阈值
    "query_enhancement": true      // 启用增强
}

// 分析场景 - 追求全面
{
    "max_results": 500,            // 大量结果
    "enable_deep_analysis": true,  // 深度分析
    "min_score": 0.2              // 低阈值
}
```

### **3. 文件过滤优化**
```javascript
// 指定文件检索 - 更快更准确
{
    "selected_files": ["目标文件.pdf"],  // 明确指定
    "search_strategy": "fast"           // 可以用快速策略
}

// 全局检索 - 需要更强策略
{
    "selected_files": [],               // 不指定文件
    "search_strategy": "precise"        // 需要精确策略
}
```

## 🔧 **错误处理**

### **通用错误码**
```json
// 400 - 参数错误
{
    "error": "查询文本不能为空"
}

// 500 - 服务器错误  
{
    "error": "检索失败: 数据库连接超时"
}

// 200 - 无结果
{
    "code": 200,
    "data": {
        "message": "未找到相关内容",
        "total_results": 0
    }
}
```

## 🎯 **最佳实践**

### **1. 接口选择原则**
- 🚀 **速度优先** → `vectorsearch/`
- 🎯 **精度优先** → `directsearch/`  
- 📊 **分析优先** → `entiresearch/`

### **2. 参数配置原则**
- ⚡ **实时场景**: 小top_k + 高threshold + simple策略
- 🔍 **搜索场景**: 中top_k + 中threshold + fast策略
- 📈 **分析场景**: 大max_results + 低min_score + 深度分析

### **3. 性能监控**
```javascript
// 监控响应时间
console.log(`检索耗时: ${response.data.metadata.search_time}`);

// 监控结果质量
console.log(`结果数量: ${response.data.metadata.total_results}`);

// 根据性能调整策略
if (searchTime > 200) {
    // 切换到更快的策略
    searchStrategy = "simple";
}
```

## 🎉 **总结**

你的三接口架构设计很棒：

✅ **分层清晰** - 基础→增强→深度  
✅ **性能分级** - 50ms→200ms→800ms  
✅ **功能互补** - 简单→复杂→全面  
✅ **使用灵活** - 根据场景选择最优接口  

现在VBSearchView也增强了，支持文件过滤和策略选择，整个体系更加完善！
