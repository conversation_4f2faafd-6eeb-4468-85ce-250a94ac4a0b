#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能效果验证测试

验证各个参数和功能是否真的能达到预期效果
"""

import requests
import json
import time
from typing import Dict, List, Any


class FeatureEffectivenessTest:
    """功能效果验证测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_results = {}
    
    def test_query_enhancement_effect(self):
        """测试查询增强的实际效果"""
        print("🔍 测试查询增强功能效果")
        print("=" * 50)
        
        test_query = "API接口"
        test_files = ["道路交通.txt"]
        
        # 测试1: 不启用查询增强
        print("\n1️⃣ 不启用查询增强:")
        result_no_enhancement = self._call_file_search({
            'text': test_query,
            'selected_files': test_files,
            'query_enhancement': False,
            'top_k': 10,
            'score_threshold': 0.4
        })
        
        if 'error' not in result_no_enhancement:
            data = result_no_enhancement['data']
            print(f"   结果数量: {len(data.get('results', []))}")
            print(f"   平均分数: {self._get_avg_score(data):.3f}")
        
        # 测试2: 启用查询增强
        print("\n2️⃣ 启用查询增强:")
        result_with_enhancement = self._call_file_search({
            'text': test_query,
            'selected_files': test_files,
            'query_enhancement': True,
            'top_k': 10,
            'score_threshold': 0.4
        })
        
        if 'error' not in result_with_enhancement:
            data = result_with_enhancement['data']
            results = data.get('results', [])
            print(f"   结果数量: {len(results)}")
            print(f"   平均分数: {self._get_avg_score(data):.3f}")
            
            # 检查是否有增强查询的标记
            enhanced_count = sum(1 for r in results if 'enhanced_query' in r.get('metadata', {}))
            print(f"   增强查询结果: {enhanced_count}/{len(results)}")
        
        # 效果对比
        self._compare_enhancement_effect(result_no_enhancement, result_with_enhancement)
    
    def test_search_strategy_effect(self):
        """测试搜索策略的实际效果"""
        print("\n🎯 测试搜索策略功能效果")
        print("=" * 50)
        
        test_query = "处罚规定"
        test_files = ["道路交通.txt"]
        strategies = ['precise', 'broad', 'adaptive', 'balanced']
        
        strategy_results = {}
        
        for strategy in strategies:
            print(f"\n🔍 测试 {strategy} 策略:")
            
            result = self._call_file_search({
                'text': test_query,
                'selected_files': test_files,
                'search_strategy': strategy,
                'top_k': 10,
                'score_threshold': 0.3
            })
            
            if 'error' not in result:
                data = result['data']
                results = data.get('results', [])
                strategy_results[strategy] = {
                    'count': len(results),
                    'avg_score': self._get_avg_score(data),
                    'search_params': data.get('search_params', {})
                }
                
                print(f"   结果数量: {strategy_results[strategy]['count']}")
                print(f"   平均分数: {strategy_results[strategy]['avg_score']:.3f}")
                print(f"   优化参数: {strategy_results[strategy]['search_params']}")
            else:
                print(f"   ❌ 失败: {result['error']}")
        
        # 策略效果分析
        self._analyze_strategy_effectiveness(strategy_results)
    
    def test_fallback_mechanism(self):
        """测试回退机制的实际效果"""
        print("\n🔄 测试回退机制功能效果")
        print("=" * 50)
        
        # 使用高阈值查询，预期触发回退
        test_query = "非常特殊的不存在内容"
        test_files = ["道路交通.txt"]
        
        # 测试1: 不启用回退
        print("\n1️⃣ 不启用回退机制:")
        result_no_fallback = self._call_file_search({
            'text': test_query,
            'selected_files': test_files,
            'enable_fallback': False,
            'score_threshold': 0.8,  # 高阈值
            'top_k': 10
        })
        
        if 'error' not in result_no_fallback:
            data = result_no_fallback['data']
            print(f"   结果数量: {len(data.get('results', []))}")
        
        # 测试2: 启用回退
        print("\n2️⃣ 启用回退机制:")
        result_with_fallback = self._call_file_search({
            'text': test_query,
            'selected_files': test_files,
            'enable_fallback': True,
            'score_threshold': 0.8,  # 高阈值
            'top_k': 10
        })
        
        if 'error' not in result_with_fallback:
            data = result_with_fallback['data']
            print(f"   结果数量: {len(data.get('results', []))}")
            print(f"   平均分数: {self._get_avg_score(data):.3f}")
        
        # 回退效果分析
        self._analyze_fallback_effectiveness(result_no_fallback, result_with_fallback)
    
    def test_full_text_search_coverage(self):
        """测试全文检索的覆盖效果"""
        print("\n📚 测试全文检索覆盖效果")
        print("=" * 50)
        
        test_query = "交通违法"
        test_files = ["道路交通.txt"]
        
        # 测试不同的max_results设置
        max_results_tests = [50, 150, 300, 500]
        
        for max_results in max_results_tests:
            print(f"\n🔍 测试 max_results={max_results}:")
            
            result = self._call_full_text_search({
                'text': test_query,
                'selected_files': test_files,
                'max_results': max_results,
                'min_score': 0.2,
                'enable_deep_analysis': True
            })
            
            if 'error' not in result:
                data = result['data']
                print(f"   实际获得: {data.get('total_segments', 0)}个片段")
                print(f"   分析方法: {data.get('analysis_method', 'unknown')}")
                
                # 检查是否有深度分析结果
                if 'structured_summary' in data:
                    summary = data['structured_summary']
                    print(f"   关键发现: {len(summary.get('key_findings', []))}个")
                
                if 'entities' in data:
                    entities = data['entities']
                    entity_count = sum(len(entity_list) for entity_list in entities.values())
                    print(f"   识别实体: {entity_count}个")
            else:
                print(f"   ❌ 失败: {result['error']}")
    
    def test_analysis_type_effectiveness(self):
        """测试分析类型的实际效果"""
        print("\n🎭 测试分析类型功能效果")
        print("=" * 50)
        
        test_query = "法律条文规定"
        test_files = ["道路交通.txt"]
        analysis_types = ['general', 'legal', 'technical']
        
        for analysis_type in analysis_types:
            print(f"\n🔍 测试 {analysis_type} 分析:")
            
            result = self._call_full_text_search({
                'text': test_query,
                'selected_files': test_files,
                'analysis_type': analysis_type,
                'max_results': 200,
                'enable_deep_analysis': True
            })
            
            if 'error' not in result:
                data = result['data']
                print(f"   片段数量: {data.get('total_segments', 0)}")
                
                # 检查分析结果的差异
                if 'structured_summary' in data:
                    summary = data['structured_summary']
                    print(f"   标题: {summary.get('title', 'N/A')}")
                    
                    key_findings = summary.get('key_findings', [])
                    if key_findings:
                        print(f"   主要发现: {key_findings[0]}")
                
                if 'entities' in data:
                    entities = data['entities']
                    print(f"   实体类型: {list(entities.keys())}")
            else:
                print(f"   ❌ 失败: {result['error']}")
    
    def _call_file_search(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """调用文件检索API"""
        url = f"{self.base_url}/datasheet/filesearch/"
        default_params = {
            'database': ['default'],
            'collection': ['chzngk228']
        }
        
        payload = {**default_params, **params}
        
        try:
            response = requests.post(url, json=payload, timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'error': str(e)}
    
    def _call_full_text_search(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """调用全文检索API"""
        url = f"{self.base_url}/datasheet/fulltextsearch/"
        default_params = {
            'database': ['default'],
            'collection': ['chzngk228']
        }
        
        payload = {**default_params, **params}
        
        try:
            response = requests.post(url, json=payload, timeout=60)
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'error': str(e)}
    
    def _get_avg_score(self, data: Dict[str, Any]) -> float:
        """获取平均分数"""
        results = data.get('results', [])
        if not results:
            return 0.0
        
        scores = []
        for result in results:
            score = result.get('metadata', {}).get('score', 0)
            scores.append(score)
        
        return sum(scores) / len(scores) if scores else 0.0
    
    def _compare_enhancement_effect(self, no_enhancement, with_enhancement):
        """对比查询增强效果"""
        print("\n📊 查询增强效果对比:")
        
        if 'error' in no_enhancement or 'error' in with_enhancement:
            print("   ❌ 无法对比，存在错误")
            return
        
        no_count = len(no_enhancement['data'].get('results', []))
        with_count = len(with_enhancement['data'].get('results', []))
        
        no_score = self._get_avg_score(no_enhancement['data'])
        with_score = self._get_avg_score(with_enhancement['data'])
        
        print(f"   结果数量变化: {no_count} → {with_count} ({with_count - no_count:+d})")
        print(f"   平均分数变化: {no_score:.3f} → {with_score:.3f} ({with_score - no_score:+.3f})")
        
        if with_count > no_count:
            print("   ✅ 查询增强有效：增加了结果数量")
        elif with_score > no_score:
            print("   ✅ 查询增强有效：提高了结果质量")
        else:
            print("   ⚠️ 查询增强效果不明显")
    
    def _analyze_strategy_effectiveness(self, strategy_results):
        """分析搜索策略效果"""
        print("\n📊 搜索策略效果分析:")
        
        if not strategy_results:
            print("   ❌ 无策略结果可分析")
            return
        
        # 按结果数量排序
        by_count = sorted(strategy_results.items(), key=lambda x: x[1]['count'], reverse=True)
        print(f"   📊 结果数量排序: {' > '.join([f'{k}({v[\"count\"]})' for k, v in by_count])}")
        
        # 按质量排序
        by_quality = sorted(strategy_results.items(), key=lambda x: x[1]['avg_score'], reverse=True)
        print(f"   🎯 质量排序: {' > '.join([f'{k}({v[\"avg_score\"]:.3f})' for k, v in by_quality])}")
        
        # 策略特点分析
        if 'precise' in strategy_results and 'broad' in strategy_results:
            precise = strategy_results['precise']
            broad = strategy_results['broad']
            
            if precise['avg_score'] > broad['avg_score']:
                print("   ✅ precise策略确实提供了更高质量的结果")
            
            if broad['count'] > precise['count']:
                print("   ✅ broad策略确实提供了更多数量的结果")
    
    def _analyze_fallback_effectiveness(self, no_fallback, with_fallback):
        """分析回退机制效果"""
        print("\n📊 回退机制效果分析:")
        
        if 'error' in no_fallback or 'error' in with_fallback:
            print("   ❌ 无法分析，存在错误")
            return
        
        no_count = len(no_fallback['data'].get('results', []))
        with_count = len(with_fallback['data'].get('results', []))
        
        print(f"   结果数量对比: {no_count} vs {with_count}")
        
        if with_count > no_count:
            print("   ✅ 回退机制有效：在高阈值下仍能获得结果")
        else:
            print("   ⚠️ 回退机制未触发或效果不明显")


def main():
    """主函数"""
    print("🧪 功能效果验证测试")
    print("验证各个参数和功能是否真的能达到预期效果")
    
    tester = FeatureEffectivenessTest()
    
    try:
        # 测试查询增强
        tester.test_query_enhancement_effect()
        
        # 测试搜索策略
        tester.test_search_strategy_effect()
        
        # 测试回退机制
        tester.test_fallback_mechanism()
        
        # 测试全文检索覆盖
        tester.test_full_text_search_coverage()
        
        # 测试分析类型
        tester.test_analysis_type_effectiveness()
        
        print("\n✅ 功能效果验证测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")


if __name__ == '__main__':
    main()
