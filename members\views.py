
# from django.shortcuts import render
# Create your views here.
from members.models import *
from rest_framework import mixins,viewsets
from .Userserializers import UserSetSerializer,UserLoginSerializer,UserLoginLogSerializer,PasswordChangeSerializer,UserGetSerializer,MemberAvatarSerializer,PasswordCodeSerializer
from untils.untils import Mixin,MixinID,LoginMixin,LogoutMixin,CheckUser,message as mg
from django.http import JsonResponse
from rest_framework.views import APIView
from rest_framework.exceptions import NotFound
from django.db import transaction
from loguru import logger
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
# from untils.crypt import Crypt
import random
from django.contrib.auth import login,logout,authenticate

# import redis,os
import json
# 连接到Redis
# redis_client = redis.StrictRedis(host=os.getenv('REDIS_URL'), port=6699, db=1)
class UserViewUpdate(MixinID):#
    queryset = Members.objects.all()
    serializer_class = UserSetSerializer

    lookup_field = 'id'
# 用户注册
class UserViewSet(Mixin):#
    queryset = Members.objects.all()
    serializer_class = UserSetSerializer

    def create(self, request, *args, **kwargs):
        # 获取Members表中的记录数
        has_data = Members.objects.exists()
        serializer = self.get_serializer(data=request.data)
        username = request.data.get('username')
        company_id = request.data.get('uuid_company')
        member_id = request.data.get('id')
        
        
        if has_data:#说明已有用户登录   
            # 检查新用户的公司ID是否与已有用户的公司ID相匹配
            # 检查是否已有用户存在，并验证company_id是否为空
            existing_company_ids = Members.objects.exclude(company_id__in=[None, '']).values_list('company_id', flat=True).distinct()
            company_id_str = ', '.join(existing_company_ids)
            try:
                member = Members.objects.get(username = username)
            except Members.DoesNotExist:
                member = None
            logger.info(f"{username}")
            if member and member.username == username:
                # 用户存在，更新字段
                # member.company_id = company_id if company_id else member.company_id # 如果没有提供新值，则保留原始值
                # member.id = member_id if member_id else member.id
                # member.save()
                # return Response({'code':200,'msg':'当前用户已存在'})
                # refresh = RefreshToken.for_user(member)
                usr_data =UserGetSerializer(member)
                
                from untils.refreshToken import CustomRefreshToken
                # refresh = RefreshToken.for_user(user)
                refresh = CustomRefreshToken.for_user(member)

                    # dep_data = DepartmentSerializer(user.role_uid)
                    # pos_data = PositionSerializer(dep_data.department)
                data = usr_data.data
                access_token = str(refresh)
                print(access_token)
                # data = user.to_json()
                data.setdefault('token' , access_token)
                login(request, member)
                return Response({'code':200,'msg': '用户已存在','data':data})
            # 说明是同一家公司 但是是新用户
            elif company_id_str == company_id:
                serializer.is_valid(raise_exception=True)
                # 使用最初的序列化器创建新数据，并手动指定ID
                new_member_serializer = serializer
                # new_member_data = new_member_serializer.validated_data            
                
                u = new_member_serializer.save(id=member_id)
                u.company_id=company_id
                u.is_active=True
                u.is_staff=True
                u.roles=0
                u.user_id=u.id
                u.set_password(serializer.validated_data['password'])
                u.save()# 创建成功，返回新创建的用户信息和HTTP 201创建状态码
                from untils.refreshToken import CustomRefreshToken
                # refresh = RefreshToken.for_user(user)
                refresh = CustomRefreshToken.for_user(u)

                    # dep_data = DepartmentSerializer(user.role_uid)
                    # pos_data = PositionSerializer(dep_data.department)
                # data = usr_data.data
                access_token = str(refresh)
                print(access_token)
                # data = user.to_json()
                
                usr_data =UserGetSerializer(u)
                data = usr_data.data
                data.setdefault('token' , access_token)
                login(request, u)
                return Response({'code':200,'msg':'该用户已添加到当前企业','data':data})       
            else:
                return Response({'code':400,'msg': '用户不存在，且不属于当前企业'})
        else:#没有用户，直接保存当前用户信息
            serializer.is_valid(raise_exception=True)
            # 使用最初的序列化器创建新数据，并手动指定ID
            new_member_serializer = serializer
            # new_member_data = new_member_serializer.validated_data            
            
            u = new_member_serializer.save(id=member_id)
            u.company_id=company_id
            u.is_active=True
            u.is_staff=True
            u.roles=0
            u.set_password(serializer.validated_data['password'])
            u.save()# 创建成功，返回新创建的用户信息和HTTP 201创建状态码
            
            from untils.refreshToken import CustomRefreshToken
            # refresh = RefreshToken.for_user(user)
            refresh = CustomRefreshToken.for_user(u)

                # dep_data = DepartmentSerializer(user.role_uid)
                # pos_data = PositionSerializer(dep_data.department)
            # data = usr_data.data
            access_token = str(refresh)
            print(access_token)
            # data = user.to_json()
            
            usr_data =UserGetSerializer(u)
            data = usr_data.data
            data.setdefault('token' , access_token)
            login(request, u)
            
            return Response({'code':200,'data':data})       

# # class UserViewUpdate(CheckUser,MixinID):#
# #     queryset = Members.objects.all()
# #     serializer_class = UserSetSerializer

# #     lookup_field = 'id'

# # class AdminUserView(MixinID):#
# #     queryset = Members.objects.all()
# #     serializer_class = UserSetSerializer

# #     lookup_field = 'id'
from rest_framework.decorators import action
# 注册部门管理用户
class DepartUserViewSet(Mixin):#
    queryset = Members.objects.all()
    serializer_class = UserSetSerializer
    def create(self, request, *args, **kwargs):
        username = request.data.get('username')
        member_id = request.data.get('user_id')
        department = request.data.get('depart_name')
        print(username,member_id)
        
        try:
            # 检查用户是否已存在
            existing_user = Members.objects.get(username=username)
            # 如果用户存在，只更新 user_id
            if member_id:
                existing_user.user_id = member_id
                existing_user.depart_name = department
                existing_user.save()
            
            # 序列化返回数据
            serializer = UserGetSerializer(existing_user)
            return Response({
                'code': 200,
                'msg': '用户已存在，已更新用户ID',
                'data': serializer.data
            })
            
        except Members.DoesNotExist:
            # 如果用户不存在，创建新用户
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            return Response({
                'code': 200,
                'msg': '用户创建成功',
                'data': serializer.data
            })
    def perform_create(self, serializer):
        user = serializer.save()
        user.set_password(serializer.validated_data['password'])
        user.save()
        
from .Userserializers import UserRolesSerializer
class UserRolesView(APIView):
    querysets=Members.objects.all()
    def get(self,request):
        query = Members.objects.all()
        ser = UserRolesSerializer(query        ,many=True)
        return JsonResponse(mg(data=ser.data))
        


class DepartmentViewSet(APIView):        
    def post(self, request):
        cur = request.user
        if cur.roles !='1':
            return Response({'error': 'You do not have permission to view members.'},status=403)
        department_head = cur  # 如果当前用户是部门头
        member_usernames = request.data.get('username', [])

        try:
            members = Members.objects.get(username = member_usernames)
        except Members.DoesNotExist:
            return JsonResponse(mg(msg='member not found',code=400))

        department_head.departments.add(members.id)
        return JsonResponse(mg(msg='Members added to department successfully',code=200))

    def put(self, request):
        cur = request.user        
        if cur.roles !='1':
            return Response({'error': 'You do not have permission to view members.'},status=403)
        department_head = cur  # 如果当前用户是部门头
        member_usernames = request.data.get('username', [])

        try:
            members = Members.objects.get(username = member_usernames)
        except Members.DoesNotExist:
            return JsonResponse(mg(msg='One or More members not found',code=400))

        department_head.departments.remove(members.id)
        return JsonResponse(mg(msg='Members remove to department successfully',code=200))

    def get(self, request):
        cur = request.user
        
        if cur.roles !='1':
            return Response({'error': 'You do not have permission to view members.'},status=403)
        members = cur.departments.all()
        serializer = UserSetSerializer(members, many=True)
        return JsonResponse(mg(serializer.data))

class LoginLogView(Mixin):
    serializer_class = UserLoginLogSerializer
    queryset = LoginLog.objects.all()
    def perform_create(self, serializer):
        # 首先，将所有现有的登录记录的状态设置为0# 获取新添加记录的 username
        username = self.request.data.get('username')
        
        # 首先，将指定用户的所有现有登录记录的状态设置为0
        if username:
            LoginLog.objects.filter(username=username).update(status=0)

        # 然后，创建新的记录
        serializer = self.serializer_class(data=self.request.data)
        if serializer.is_valid():
            # 设置新记录的状态为1
            serializer.save(status=1)
            return Response(serializer.data, status=201)
        return Response(serializer.errors, status=400)
    
class LoginLogIDView(MixinID):
    serializer_class = UserLoginLogSerializer
    queryset = LoginLog.objects.all()
    
    lookup_field ='id'
        
    
#账密登录
class UserLogin(LoginMixin,mixins.CreateModelMixin,viewsets.GenericViewSet):    
    # queryset = Members.objects.all()
    serializer_class = UserLoginSerializer

    def signin(self, request, *args, **kwargs):
        return self.login(request, *args, **kwargs)  

class UsernameLogin(LoginMixin,mixins.CreateModelMixin,viewsets.GenericViewSet):    

    def signin(self, request, *args, **kwargs):
        return self.LoginByUsername(request, *args, **kwargs)  
# 用户登出
class UserLogout(LogoutMixin,viewsets.GenericViewSet,):    
    def signout(self, request, *args, **kwargs):
        return self.logout(request, *args, **kwargs)  

# # 修改密码
# class PasswordChangeView(mixins.UpdateModelMixin, viewsets.GenericViewSet):
#     serializer_class=PasswordChangeSerializer
#     queryset = Members.objects.all()

#     def update(self, request, *args, **kwargs):
#         pass
#         # isinstance = self.request.user
#         # serializer = self.get_serializer(data = request.data)
#         # if serializer.is_valid(raise_exception = True):
#         #     if not isinstance.check_password(serializer.validated_data.get('old_password')):
#         #         return JsonResponse(mg(code=401, msg='密码输入错误'))
#         #     if isinstance.check_password(serializer.validated_data.get('new_password')):
#         #         return JsonResponse(mg(code=401, msg='新密码不能和旧密码相同'))
#         #     else:
#         #         isinstance.set_password(serializer.validated_data.get('new_password'))
#         #         isinstance.save()
#         #         return JsonResponse(mg(code=200))
#         # return JsonResponse(mg(code=500))
# # 忘记密码
# class PasswordCodeView(mixins.UpdateModelMixin, viewsets.GenericViewSet):
#     serializer_class=PasswordCodeSerializer
#     queryset = Members.objects.all()

#     def code(self, request, *args, **kwargs):
#         pass
#         # username = request.GET.get('username','')

#         # if not username:
#         #     return JsonResponse(mg(code=400,msg='用户名为空'))
#         # try:
#         #     user = Members.objects.get(username=username)
#         # except Members.DoesNotExist:
#         #     return JsonResponse(mg(code=404,msg='用户不存在'))
        
#         # if user:
#         #     code = random.randint(100000, 999999)
#         #     logger.info(code)
#         #     try:
#         #         SmsApi.sendsms(user.username,code)
#         #     except Exception as e:
#         #         return JsonResponse(mg(code=400,msg='验证码发送失败'))
#         #     # 存储验证码到Redis，设置5分钟过期
#         #     redis_client.set(user.username, code, ex=120)
#         #     return JsonResponse(mg(code=200,data=Crypt.encrypt_data(str(code))))

#     def resetpassword(self, request, *args, **kwargs):    
#         pass
#         # serializer = self.get_serializer(data = request.data)
        
#         # if serializer.is_valid(raise_exception = True):
#         #     try:
#         #         user = Members.objects.get(username=serializer.validated_data.get('username'))
#         #     except Members.DoesNotExist:
#         #         return JsonResponse(mg(code=400,msg='用户不存在'))
#         #     try:
#         #         stored_code = redis_client.get(user.username)
#         #         # getcode = Crypt.decrypt_data(str(serializer.validated_data.get('code')))
#         #         # logger.info(str(serializer.validated_data.get('code')))
#         #         # 验证码比较
#         #         if stored_code is not None and str(serializer.validated_data.get('code')) == stored_code.decode('utf-8'):
#         #             user.set_password(serializer.validated_data.get('password'))
#         #             user.save()
#         #             redis_client.delete(user.username)
#         #             return JsonResponse({'code': 200})
#         #         else:
#         #             return JsonResponse({'msg': '验证码错误或已过期', 'code': 400})
#         #     except Exception as e:
#         #         logger.error(str(e))
#         #         return JsonResponse(mg(msg=str(e)))
#         # return JsonResponse(mg(code=500))
 
# class UserSetView(APIView): 
#     def patch(self,request, *args, **kwargs):
#         pass
#         # user_id = request.data.get("username")
#         # print('bbbb',user_id)
#         # user = request.user
#         # admin = Members.objects.get(username=user.username)
#         # if not admin.is_superuser:
#         #     return JsonResponse(mg(msg='没有权限操作'))
#         # new_password = request.data.get("new_password")
#         # # new_password = request.data.get("new_password") if request.data.get("new_password") else '123456'
#         # # is_active = request.data.get("is_active", False)
#         # try:
#         #     user = Members.objects.get(username=user_id)
            
#         #     user.is_active = not user.is_active
#         #     if new_password != None:
#         #         user.set_password(new_password)
#         #     user.save()
#         #     return JsonResponse(mg(code=200))
#         # except Exception as e:
#         #     print(e)
#         #     return JsonResponse(mg(code=500,msg='用户不存在'))
        
# class AvatarView(APIView):
#     def post(self,request, *args, **kwargs):
#         pass
#         # user = request.user
#         # try:
#         #     member = Members.objects.get(id = user.id)
#         # except Members.DoesNotExist:
#         #     return JsonResponse(mg(msg='用户不存在'))
        
#         # if 'avatar' not in request.FILES:
#         #     return JsonResponse(mg(msg='未发现头像文件'))

#         # serializer = MemberAvatarSerializer(member,data=request.data)

#         # if serializer.is_valid():
#         #     serializer.save()
#         #     return JsonResponse(mg(data=serializer.data))
#         # else:
#         #     return JsonResponse(mg(serializer.error))
        

        
# from django.utils import timezone
# from datetime import timedelta
# class LastLoginView(APIView):
#     def put(self,request, *args, **kwargs):      
#         pass  
#         # username = request.data.get('username')
#         # # logger.info(username)
#         # try:
#         #     member = Members.objects.get(username = username)
#         # except Members.DoesNotExist:
#         #     return JsonResponse(mg(msg='用户不存在'))
#         # # 确保用户实例已经存在
#         # if member is not None:
#         #     # 设置最后一次登录时间为现在的时间减去一天
#         #     member.last_login = timezone.now() - timedelta(days=1)
#         #     # 保存用户实例
#         #     member.save()
#         #     return JsonResponse(mg(msg='解绑成功'))
#         # else:
#         #     return JsonResponse(mg(code=400,msg='无效用户'))

           

