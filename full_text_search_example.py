#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全文检索功能示例

演示如何使用全文检索功能来全面分析文档内容
特别适用于总结书籍、分析主题等场景
"""

import requests
import json
from typing import Dict, List, Any


class FullTextSearchClient:
    """全文检索客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
    
    def analyze_book_battles(self, book_file: str, query: str = "经典战役和典故") -> Dict[str, Any]:
        """
        分析书籍中的战役和典故
        
        Args:
            book_file: 书籍文件名
            query: 分析查询
            
        Returns:
            分析结果
        """
        url = f"{self.base_url}/datasheet/fulltextsearch/"
        
        payload = {
            'text': query,
            'selected_files': [book_file],
            'database': ['default'],
            'collection': ['chzngk228'],
            'analysis_type': 'summary',
            'batch_size': 50,
            'min_score': 0.2,
            'max_results': 300,
            'enable_clustering': True
        }
        
        try:
            response = requests.post(url, json=payload, timeout=60)
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}: {response.text}'}
        except Exception as e:
            return {'error': str(e)}
    
    def extract_characters(self, book_file: str, query: str = "主要人物") -> Dict[str, Any]:
        """
        提取书籍中的主要人物
        
        Args:
            book_file: 书籍文件名
            query: 分析查询
            
        Returns:
            人物分析结果
        """
        url = f"{self.base_url}/datasheet/fulltextsearch/"
        
        payload = {
            'text': query,
            'selected_files': [book_file],
            'analysis_type': 'extract',
            'batch_size': 40,
            'min_score': 0.25,
            'max_results': 200,
            'enable_clustering': True
        }
        
        try:
            response = requests.post(url, json=payload, timeout=60)
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}: {response.text}'}
        except Exception as e:
            return {'error': str(e)}
    
    def comprehensive_analysis(self, book_files: List[str], theme: str) -> Dict[str, Any]:
        """
        对多个文件进行综合主题分析
        
        Args:
            book_files: 文件列表
            theme: 分析主题
            
        Returns:
            综合分析结果
        """
        url = f"{self.base_url}/datasheet/fulltextsearch/"
        
        payload = {
            'text': theme,
            'selected_files': book_files,
            'analysis_type': 'classify',
            'batch_size': 60,
            'min_score': 0.15,
            'max_results': 500,
            'enable_clustering': True
        }
        
        try:
            response = requests.post(url, json=payload, timeout=90)
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}: {response.text}'}
        except Exception as e:
            return {'error': str(e)}


def demo_sanguo_analysis():
    """演示三国演义分析"""
    client = FullTextSearchClient()
    
    print("📚 《三国演义》全文分析示例")
    print("=" * 50)
    
    # 1. 分析经典战役和典故
    print("\n🏛️ 分析经典战役和典故...")
    battle_result = client.analyze_book_battles(
        book_file="三国演义.txt",
        query="经典战役 典故 计策"
    )
    
    if 'error' in battle_result:
        print(f"❌ 分析失败: {battle_result['error']}")
    else:
        data = battle_result.get('data', {})
        structured = data.get('structured_output', {})
        
        print(f"📊 总体概况:")
        overview = structured.get('overview', {})
        print(f"   - 相关片段数: {overview.get('total_segments', 0)}")
        print(f"   - 主要主题数: {overview.get('main_themes_count', 0)}")
        print(f"   - 聚类数量: {overview.get('clusters_count', 0)}")
        
        print(f"\n⚔️ 主要战役和典故:")
        battles = structured.get('main_battles_and_stories', [])
        for i, battle in enumerate(battles[:5], 1):
            print(f"   {i}. {battle.get('name', '未知')}")
            print(f"      片段数: {battle.get('segment_count', 0)}")
            print(f"      相关度: {battle.get('relevance_score', 0):.3f}")
            print(f"      预览: {battle.get('content_preview', '')[:100]}...")
            print()
        
        print(f"\n🔑 关键主题:")
        themes = structured.get('key_themes', [])
        for theme in themes[:8]:
            print(f"   - {theme.get('theme', '')}: {theme.get('frequency', 0)}次")
    
    print("\n" + "=" * 50)
    
    # 2. 分析主要人物
    print("\n👥 分析主要人物...")
    character_result = client.extract_characters(
        book_file="三国演义.txt",
        query="主要人物 英雄 将军"
    )
    
    if 'error' in character_result:
        print(f"❌ 分析失败: {character_result['error']}")
    else:
        data = character_result.get('data', {})
        processed = data.get('processed_results', {})
        
        print(f"📊 人物分析结果:")
        print(f"   - 总片段数: {processed.get('total_segments', 0)}")
        print(f"   - 平均相关度: {processed.get('avg_score', 0):.3f}")
        
        content_analysis = processed.get('content_analysis', {})
        themes = content_analysis.get('main_themes', [])
        
        print(f"\n👑 主要人物:")
        for theme in themes[:10]:
            if theme.get('type') == '人物' or any(char in theme.get('theme', '') 
                                               for char in ['刘备', '关羽', '张飞', '诸葛亮', '曹操']):
                print(f"   - {theme.get('theme', '')}: 出现{theme.get('frequency', 0)}次")


def demo_comparative_analysis():
    """演示对比分析"""
    client = FullTextSearchClient()
    
    print("\n📖 多书籍对比分析示例")
    print("=" * 50)
    
    # 对比分析三国演义和西游记的主题
    result = client.comprehensive_analysis(
        book_files=["三国演义.txt", "西游记.txt"],
        theme="英雄 冒险 智慧 策略"
    )
    
    if 'error' in result:
        print(f"❌ 对比分析失败: {result['error']}")
    else:
        data = result.get('data', {})
        processed = data.get('processed_results', {})
        
        print(f"📊 对比分析结果:")
        print(f"   - 总片段数: {processed.get('total_segments', 0)}")
        print(f"   - 平均相关度: {processed.get('avg_score', 0):.3f}")
        
        clusters = processed.get('clusters', [])
        print(f"\n🎭 主题聚类:")
        for i, cluster in enumerate(clusters[:5], 1):
            print(f"   {i}. {cluster.get('theme', '未知主题')}")
            print(f"      片段数: {cluster.get('count', 0)}")
            print(f"      平均分数: {cluster.get('avg_score', 0):.3f}")


def demo_custom_analysis():
    """演示自定义分析"""
    client = FullTextSearchClient()
    
    print("\n🔍 自定义主题分析示例")
    print("=" * 50)
    
    # 自定义分析：研究三国演义中的军事策略
    result = client.analyze_book_battles(
        book_file="三国演义.txt",
        query="军事策略 兵法 计谋 战术 布阵"
    )
    
    if 'error' in result:
        print(f"❌ 自定义分析失败: {result['error']}")
    else:
        data = result.get('data', {})
        
        print(f"🎯 军事策略分析:")
        print(f"   - 检索到片段: {data.get('total_segments', 0)}个")
        
        structured = data.get('structured_output', {})
        battles = structured.get('main_battles_and_stories', [])
        
        print(f"\n⚔️ 相关战役策略:")
        for battle in battles:
            if any(keyword in battle.get('name', '') 
                   for keyword in ['战', '计', '策', '兵']):
                print(f"   - {battle.get('name', '')}")
                print(f"     相关度: {battle.get('relevance_score', 0):.3f}")


def main():
    """主函数"""
    print("🚀 全文检索功能演示")
    print("适用于深度分析文档内容，如总结书籍主题、提取关键信息等")
    
    try:
        # 演示三国演义分析
        demo_sanguo_analysis()
        
        # 演示对比分析
        demo_comparative_analysis()
        
        # 演示自定义分析
        demo_custom_analysis()
        
        print("\n✅ 演示完成!")
        print("\n💡 使用建议:")
        print("   1. 调整 min_score 参数控制结果质量")
        print("   2. 增加 max_results 获取更全面的内容")
        print("   3. 使用不同的 analysis_type 适应不同需求")
        print("   4. 启用 clustering 获得结构化的主题分析")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {str(e)}")


if __name__ == '__main__':
    main()
