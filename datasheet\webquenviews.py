from untils.webextract.webCommon import ContentExtractorService, WebExtractorConfig,FileProcessor
from django.http import JsonResponse
from typing import List
import time
import asyncio
from rest_framework.views import APIView
from .tasks import complete_web_pipeline
from loguru import logger

class WebExtractor(APIView):
    """优化后的Web提取API"""
    
    def __init__(self):
        self.config = WebExtractorConfig()
        self.service = ContentExtractorService(self.config)
        self.processor = FileProcessor()
    
    def post(self, request):
        try:
            # 获取参数
            urls = request.data.get('urls')
            if not urls:
                return JsonResponse({'error': '缺少URLs参数'}, status=400)
            
            # 标准化URLs为列表
            url_list = [urls] if isinstance(urls, str) else urls
            if not isinstance(url_list, list) or not url_list:
                return JsonResponse({'error': '无效的URLs格式'}, status=400)
            
            user = request.user
            uid_sheet = request.data.get('uid_sheet', '')
            
            # 生成文件夹名
            timestr = str(time.time()).replace(".", "")
            folder_name = request.data.get('cache_id', timestr)
            
            # 选择处理模式
            use_celery = request.data.get('use_celery', 'False')
            # 先创建文件预览记录（用于立即显示）
            preview_records = self.processor._preform_process_file(url_list, str(user.uuid_user), uid_sheet, folder_name)
            
            if use_celery:
                # 使用Celery异步处理
                task = complete_web_pipeline.delay(
                    url_list, 
                    user.username, 
                    uid_sheet, 
                    str(user.uuid_user), 
                    folder_name
                )
                
                return JsonResponse({
                    'status': 'processing',
                    'task_id': task.id,
                    'target_folder': folder_name,
                    'preview_records':preview_records,
                    'message': '任务已提交，正在后台处理'
                })
            else:
                # 同步处理（适用于少量URL）
                return self._process_synchronously(url_list, user, uid_sheet, folder_name)
                
        except Exception as e:
            logger.error(f"Error processing URLs: {e}")
            return JsonResponse({'error': str(e)}, status=500)
    
    def _process_synchronously(self, url_list: List[str], user, uid_sheet: str, folder_name: str):
        """同步处理URLs"""
        try:
            # 创建目标目录
            target_dir = self.processor.create_target_directory(user.username, folder_name)
            
            async def process_urls():
                results = []
                for url in url_list:
                    try:
                        file_path = await self.service.extract_and_save(url, user.username)
                        results.append(file_path)
                    except Exception as e:
                        results.append({'error': str(e), 'url': url})
                return results
            
            # 运行异步任务
            file_paths = asyncio.run(process_urls())
            
            # 处理文件
            processed_files = []
            for file_path in file_paths:
                if isinstance(file_path, dict) and 'error' in file_path:
                    processed_files.append(file_path)
                    continue
                
                try:
                    result = self.processor.process_file(
                        file_path, target_dir, uid_sheet, str(user.uuid_user)
                    )
                    processed_files.append({
                        'file_path': file_path,
                        'result': result
                    })
                except Exception as e:
                    processed_files.append({
                        'file_path': file_path,
                        'error': str(e)
                    })
            
            return JsonResponse({
                'status': 'completed',
                'target_folder': folder_name,
                'processed_files': processed_files
            })
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
        
    # 可选：任务状态查询API
class TaskStatusView(APIView):
    """查询Celery任务状态"""
    
    def get(self, request, task_id):
        from celery.result import AsyncResult
        
        try:
            task = AsyncResult(task_id)
            
            if task.ready():
                return JsonResponse({
                    'status': 'completed',
                    'result': task.result
                })
            else:
                return JsonResponse({
                    'status': 'processing',
                    'message': '任务正在处理中...'
                })
                
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)