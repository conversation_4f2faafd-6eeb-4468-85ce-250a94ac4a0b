import requests
import re
import time
import os
import tempfile
from typing import Dict, List, Optional, Any, Tuple
import yt_dlp
import whisper
import webvtt
from io import StringIO
from .webtype import VideoInfo, SubtitleText
    
class BilibiliExtractor:
    """Bilibili内容提取器"""
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'https://www.bilibili.com/',
            'Origin': 'https://www.bilibili.com',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def is_bilibili_url(self, url: str) -> bool:
        """判断是否为bilibili链接"""
        return any(domain in url for domain in ['bilibili.com', 'b23.tv'])
    def extract_bv_from_url(self, url: str) -> Optional[str]:
        """从URL中提取BV号"""
        pattern = r'BV[a-zA-Z0-9]+'
        match = re.search(pattern, url)
        return match.group() if match else None
    
    def get_video_info(self, url: str) -> Optional[VideoInfo]:
        """获取B站视频信息"""
        try:
            bvid = self.extract_bv_from_url(url)
            if not bvid:
                print("无法从URL中提取BV号")
                return None
            
            api_url = f"https://api.bilibili.com/x/web-interface/view?bvid={bvid}"
            response = self.session.get(api_url)
            data = response.json()
            
            if data['code'] != 0:
                print(f"API请求失败: {data['message']}")
                return None
            
            video_data = data['data']
            
            return VideoInfo(
                title=video_data['title'],
                description=video_data['desc'],
                duration=video_data['duration'],
                view_count=video_data['stat']['view'],
                like_count=video_data['stat']['like'],
                upload_date=time.strftime('%Y-%m-%d', time.localtime(video_data['pubdate'])),
                uploader=video_data['owner']['name'],
                thumbnail=video_data['pic'],
                url=url,
                platform='bilibili'
            )
            
        except Exception as e:
            print(f"获取B站视频信息时出错: {e}")
            return None
    
    def get_comments(self, url: str, max_comments: int = 50) -> List[Dict[str, Any]]:
        """获取B站视频评论"""
        try:
            bvid = self.extract_bv_from_url(url)
            if not bvid:
                return []
            
            # 先获取视频aid
            api_url = f"https://api.bilibili.com/x/web-interface/view?bvid={bvid}"
            response = self.session.get(api_url)
            data = response.json()
            
            if data['code'] != 0:
                return []
            
            aid = data['data']['aid']
            
            # 获取评论
            comment_url = f"https://api.bilibili.com/x/v2/reply?type=1&oid={aid}&sort=2&ps=20"
            response = self.session.get(comment_url)
            comment_data = response.json()
            
            if comment_data['code'] != 0:
                return []
            
            comments = []
            if comment_data['data'] and comment_data['data']['replies']:
                replies = comment_data['data']['replies'][:max_comments]
                
                for reply in replies:
                    comment = {
                        'author': reply['member']['uname'],
                        'content': reply['content']['message'],
                        'like_count': reply['like'],
                        'reply_count': reply['rcount'],
                        'time': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(reply['ctime']))
                    }
                    comments.append(comment)
            
            return comments
            
        except Exception as e:
            print(f"获取B站评论时出错: {e}")
            return []
    
    def get_subtitles(self, url: str) -> List[SubtitleText]:
        """获取B站视频字幕（修复版）"""
        try:
            bvid = self.extract_bv_from_url(url)
            if not bvid:
                print("无法提取BV号")
                return []
            
            # 获取视频基本信息
            api_url = f"https://api.bilibili.com/x/web-interface/view?bvid={bvid}"
            response = self.session.get(api_url)
            data = response.json()
            
            if data['code'] != 0:
                print(f"获取视频信息失败: {data.get('message', 'Unknown error')}")
                return []
            
            aid = data['data']['aid']
            cid = data['data']['pages'][0]['cid']
            
            print(f"视频信息: aid={aid}, cid={cid}, bvid={bvid}")
            
            subtitles = []
            
            # 方法1：使用新的字幕API
            try:
                subtitle_api = f"https://api.bilibili.com/x/player/wbi/v2"
                params = {
                    'aid': aid,
                    'cid': cid,
                    'bvid': bvid,
                }
                
                print(f"尝试获取字幕: {subtitle_api}")
                response = self.session.get(subtitle_api, params=params, timeout=10)
                print(f"字幕API响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    subtitle_data = response.json()
                    print(f"字幕API返回: {subtitle_data.get('code', 'no code')}")
                    
                    if (subtitle_data.get('code') == 0 and 
                        subtitle_data.get('data') and 
                        subtitle_data['data'].get('subtitle') and 
                        subtitle_data['data']['subtitle'].get('subtitles')):
                        
                        print(f"找到字幕数量: {len(subtitle_data['data']['subtitle']['subtitles'])}")
                        
                        for sub in subtitle_data['data']['subtitle']['subtitles']:
                            subtitle_url = sub['subtitle_url']
                            if not subtitle_url.startswith('http'):
                                subtitle_url = 'https:' + subtitle_url
                            
                            print(f"下载字幕文件: {subtitle_url}")
                            
                            try:
                                sub_response = self.session.get(subtitle_url, timeout=10)
                                if sub_response.status_code == 200:
                                    sub_content = sub_response.json()
                                    
                                    # 提取纯文本
                                    text_content = []
                                    if 'body' in sub_content:
                                        for item in sub_content['body']:
                                            if 'content' in item:
                                                text_content.append(item['content'])
                                    
                                    if text_content:
                                        full_text = '\n'.join(text_content)
                                        
                                        subtitles.append(SubtitleText(
                                            language=sub.get('lan_doc', sub.get('lan', 'unknown')),
                                            content=full_text,
                                            source='subtitle'
                                        ))
                                        
                                        print(f"成功提取B站字幕: {sub.get('lan_doc', 'unknown')}")
                                    else:
                                        print("字幕文件没有内容")
                                else:
                                    print(f"字幕文件下载失败: {sub_response.status_code}")
                                    
                            except Exception as e:
                                print(f"处理字幕文件时出错: {e}")
                                continue
                    else:
                        print("API返回格式不包含字幕信息")
                        print(f"返回数据结构: {list(subtitle_data.keys()) if isinstance(subtitle_data, dict) else 'not dict'}")
                        if subtitle_data.get('data'):
                            print(f"data结构: {list(subtitle_data['data'].keys()) if isinstance(subtitle_data['data'], dict) else 'data not dict'}")
                            
            except Exception as e:
                print(f"字幕API请求出错: {e}")
            
            # 方法2：尝试旧的API
            if not subtitles:
                try:
                    print("尝试旧版API...")
                    old_api = f"https://api.bilibili.com/x/player/v2"
                    params = {'aid': aid, 'cid': cid}
                    response = self.session.get(old_api, params=params, timeout=10)
                    
                    if response.status_code == 200:
                        old_data = response.json()
                        print(f"旧API返回码: {old_data.get('code')}")
                        
                        if (old_data.get('code') == 0 and 
                            old_data.get('data') and 
                            old_data['data'].get('subtitle') and 
                            old_data['data']['subtitle'].get('subtitles')):
                            
                            for sub in old_data['data']['subtitle']['subtitles']:
                                subtitle_url = sub['subtitle_url']
                                if not subtitle_url.startswith('http'):
                                    subtitle_url = 'https:' + subtitle_url
                                
                                try:
                                    sub_response = self.session.get(subtitle_url, timeout=10)
                                    if sub_response.status_code == 200:
                                        sub_content = sub_response.json()
                                        
                                        text_content = []
                                        if 'body' in sub_content:
                                            for item in sub_content['body']:
                                                if 'content' in item:
                                                    text_content.append(item['content'])
                                        
                                        if text_content:
                                            full_text = '\n'.join(text_content)
                                            
                                            subtitles.append(SubtitleText(
                                                language=sub.get('lan_doc', sub.get('lan', 'unknown')),
                                                content=full_text,
                                                source='subtitle'
                                            ))
                                            
                                            print(f"通过旧API成功提取字幕: {sub.get('lan_doc', 'unknown')}")
                                            
                                except Exception as e:
                                    print(f"处理旧API字幕时出错: {e}")
                                    continue
                        else:
                            print("旧API也没有字幕信息")
                            
                except Exception as e:
                    print(f"旧API请求出错: {e}")
            
            if not subtitles:
                print("该B站视频没有找到字幕")
            else:
                print(f"成功提取到 {len(subtitles)} 个字幕文件")
            
            return subtitles
            
        except Exception as e:
            print(f"获取B站字幕时出错: {e}")
            import traceback
            traceback.print_exc()
            return []

class YouTubeExtractor:
    """YouTube内容提取器"""
    
    def __init__(self):
        self.ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extractaudio': False,
            'extract_flat': False,
        }
    def is_youtube_url(self, url: str) -> bool:
        """判断是否为youtube链接"""
        return any(domain in url for domain in ['youtube.com', 'youtu.be'])
    def get_video_info(self, url: str) -> Optional[VideoInfo]:
        """获取YouTube视频信息"""
        try:
            with yt_dlp.YoutubeDL(self.ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                return VideoInfo(
                    title=info.get('title', ''),
                    description=info.get('description', ''),
                    duration=info.get('duration', 0),
                    view_count=info.get('view_count', 0),
                    like_count=info.get('like_count', 0),
                    upload_date=info.get('upload_date', ''),
                    uploader=info.get('uploader', ''),
                    thumbnail=info.get('thumbnail', ''),
                    url=url,
                    platform='youtube'
                )
                
        except Exception as e:
            print(f"获取YouTube视频信息时出错: {e}")
            return None
    
    def get_subtitles(self, url: str) -> List[SubtitleText]:
        """获取YouTube字幕并只返回一个中文字幕（修复版）"""
        try:
            # 中文语言优先级（从高到低）
            chinese_langs = ['zh-CN', 'zh-Hans', 'zh', 'zh-TW', 'zh-Hant', 'zh-HK']
            
            ydl_opts = {
                'writesubtitles': True,
                'writeautomaticsub': True,
                'skip_download': True,
                'quiet': True,
                'subtitleslangs': chinese_langs + ['en', 'en-US'],  # 包含英文作为备选
            }
            
            subtitles = []
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                # 优先处理手动字幕
                if 'subtitles' in info:
                    for lang in chinese_langs:
                        if lang in info['subtitles']:
                            for sub in info['subtitles'][lang]:
                                if sub['ext'] in ['vtt', 'srt']:
                                    text_content = self._download_and_parse_subtitle(sub['url'])
                                    if text_content:
                                        subtitles.append(SubtitleText(
                                            language=f"中文({lang})",
                                            content=text_content,
                                            source='subtitle'
                                        ))
                                        print(f"成功提取YouTube手动字幕: {lang}")
                                        return subtitles  # 只返回第一个找到的中文字幕
                
                # 如果没有手动字幕，尝试自动字幕
                if not subtitles and 'automatic_captions' in info:
                    for lang in chinese_langs:
                        if lang in info['automatic_captions']:
                            for sub in info['automatic_captions'][lang]:
                                if sub['ext'] in ['vtt', 'srt']:
                                    text_content = self._download_and_parse_subtitle(sub['url'])
                                    if text_content:
                                        subtitles.append(SubtitleText(
                                            language=f"中文({lang})-自动",
                                            content=text_content,
                                            source='subtitle'
                                        ))
                                        print(f"成功提取YouTube自动字幕: {lang}")
                                        return subtitles  # 只返回第一个找到的中文字幕
                
                # 如果还是没有中文字幕，尝试英文字幕（只取一个）
                if not subtitles:
                    english_langs = ['en', 'en-US', 'en-GB']
                    for source_name, source_data in [('subtitles', info.get('subtitles', {})), 
                                                   ('automatic_captions', info.get('automatic_captions', {}))]:
                        for lang in english_langs:
                            if lang in source_data:
                                for sub in source_data[lang]:
                                    if sub['ext'] in ['vtt', 'srt']:
                                        text_content = self._download_and_parse_subtitle(sub['url'])
                                        if text_content:
                                            subtitles.append(SubtitleText(
                                                language=f"英文({lang})",
                                                content=text_content,
                                                source='subtitle'
                                            ))
                                            print(f"成功提取YouTube英文字幕: {lang}")
                                            return subtitles  # 只返回第一个找到的英文字幕
            
            if not subtitles:
                print("YouTube视频没有找到中文或英文字幕")
            
            return subtitles
            
        except Exception as e:
            print(f"获取YouTube字幕时出错: {e}")
            return []
    
    def _download_and_parse_subtitle(self, subtitle_url: str) -> str:
        """下载并解析字幕文件，返回纯文本"""
        try:
            response = requests.get(subtitle_url, timeout=30)
            response.raise_for_status()
            
            # 判断字幕格式并解析
            if subtitle_url.endswith('.vtt') or 'vtt' in subtitle_url:
                return self._parse_vtt_content(response.text)
            elif subtitle_url.endswith('.srt') or 'srt' in subtitle_url:
                return self._parse_srt_content(response.text)
            else:
                # 尝试按VTT格式解析
                return self._parse_vtt_content(response.text)
                
        except Exception as e:
            print(f"下载字幕时出错: {e}")
            return ""
    
    def _parse_vtt_content(self, vtt_content: str) -> str:
        """解析VTT字幕内容"""
        try:
            # 使用webvtt库解析
            f = StringIO(vtt_content)
            captions = webvtt.read_buffer(f)
            
            text_lines = []
            for caption in captions:
                # 清理HTML标签和格式
                clean_text = re.sub(r'<[^>]+>', '', caption.text)
                clean_text = clean_text.strip()
                if clean_text:
                    text_lines.append(clean_text)
            
            return '\n'.join(text_lines)
            
        except Exception as e:
            # 如果webvtt解析失败，使用正则表达式
            print(f"webvtt解析失败，使用备用方法: {e}")
            return self._parse_vtt_regex(vtt_content)
    
    def _parse_vtt_regex(self, vtt_content: str) -> str:
        """使用正则表达式解析VTT内容"""
        try:
            lines = vtt_content.split('\n')
            text_lines = []
            
            for line in lines:
                line = line.strip()
                # 跳过时间戳行和空行
                if (not line or 
                    line.startswith('WEBVTT') or 
                    line.startswith('NOTE') or 
                    '-->' in line or 
                    re.match(r'^\d+$', line)):
                    continue
                
                # 清理HTML标签
                clean_line = re.sub(r'<[^>]+>', '', line)
                clean_line = clean_line.strip()
                
                if clean_line:
                    text_lines.append(clean_line)
            
            return '\n'.join(text_lines)
            
        except Exception as e:
            print(f"正则表达式解析VTT失败: {e}")
            return ""
    
    def _parse_srt_content(self, srt_content: str) -> str:
        """解析SRT字幕内容"""
        try:
            lines = srt_content.split('\n')
            text_lines = []
            
            for line in lines:
                line = line.strip()
                # 跳过序号行、时间戳行和空行
                if (not line or 
                    re.match(r'^\d+$', line) or 
                    '-->' in line):
                    continue
                
                # 清理HTML标签
                clean_line = re.sub(r'<[^>]+>', '', line)
                clean_line = clean_line.strip()
                
                if clean_line:
                    text_lines.append(clean_line)
            
            return '\n'.join(text_lines)
            
        except Exception as e:
            print(f"解析SRT内容失败: {e}")
            return ""

class WhisperTranscriber:
    """Whisper语音转文字"""
    
    def __init__(self, model_name: str = "base"):
        """
        初始化Whisper模型
        model_name: tiny, base, small, medium, large
        """
        try:
            self.model = whisper.load_model(model_name, device="cpu")
            print(f"Whisper模型 '{model_name}' 加载成功")
        except Exception as e:
            print(f"加载Whisper模型失败: {e}")
            self.model = None
    
    def transcribe_video(self, url: str, platform: str) -> Optional[SubtitleText]:
        """对视频进行语音转文字"""
        if not self.model:
            print("Whisper模型未加载")
            return None
        
        temp_audio_file = None
        try:
            print("正在下载音频文件...")
            
            # 下载音频
            temp_audio_file = self._download_audio(url)
            if not temp_audio_file:
                return None
            
            print("正在进行语音识别...")
            
            # 进行转录
            result = self.model.transcribe(temp_audio_file, language='zh')  # 指定中文
            
            if result and 'text' in result:
                return SubtitleText(
                    language="中文(Whisper识别)",
                    content=result['text'].strip(),
                    source='whisper'
                )
            
        except Exception as e:
            print(f"语音转文字失败: {e}")
            return None
        
        finally:
            # 清理临时文件
            if temp_audio_file and os.path.exists(temp_audio_file):
                try:
                    os.unlink(temp_audio_file)
                except:
                    pass
        
        return None
    
    def _download_audio(self, url: str) -> Optional[str]:
        """下载视频的音频部分"""
        try:
            temp_dir = tempfile.gettempdir()
            temp_filename = os.path.join(temp_dir, f"temp_audio_{int(time.time())}.%(ext)s")
            
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': temp_filename,
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '192',
                }],
                'quiet': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
            
            # 查找下载的文件
            temp_audio = temp_filename.replace('.%(ext)s', '.mp3')
            if os.path.exists(temp_audio):
                return temp_audio
            
            # 如果mp3不存在，查找其他格式
            base_name = temp_filename.replace('.%(ext)s', '')
            for ext in ['.wav', '.m4a', '.webm', '.ogg']:
                test_file = base_name + ext
                if os.path.exists(test_file):
                    return test_file
            
            print("找不到下载的音频文件")
            return None
            
        except Exception as e:
            print(f"下载音频失败: {e}")
            return None


