"""
URL configuration for repository project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path
from . import views
from . import webviews,webquenviews

urlpatterns = [
    # 获取知识库列表和创建知识库
    path('sheet/', views.SheetViewSet.as_view({'get':'get'})),
    path('user/sheet/', views.SheetByUserViewSet.as_view()),
    
    # 获取notebook
    path('sheet/notebook/', views.SheetByNotebookViewSet.as_view()),
    path('sheet/summary/', views.SheetSummaryViewSet.as_view()),
    #修改知识库和删除知识库
    path('sheet/list/<int:id>', views.SheetViewUpdate.as_view({'put':'put','delete':'delete'})),
    #删除数据集
    path('frame/<int:id>', views.FrameUpdateViewSet.as_view({'delete':'destroy','put':'put'})),
    path('frame/multiple/<str:ids>', views.FrameUpdateViewSet.as_view({'delete':'multiple_destroy'})),
    path('frame/group/<str:cache_id>', views.FrameUpdateViewSet.as_view({'delete':'group_destroy'})),
    #获取数据集和上传数据集
    path('frame/', views.FrameUploadViewSet.as_view({'get':'list','post':'post'})),
    path('sheet/detail/<int:fid>', views.SheetByDataFrameView.as_view()),
    #根据文件夹切片
    path('splits/folder/', views.VBSplitsView.as_view()),
    #根据单个文件名切片
    path('splits/file/', views.VBSplitOneView.as_view()),

    # 向量化存储(post和delete)
    path('milvus/', views.VBMilvusView.as_view()),
    #修改切片
    path('edit/', views.VBeditView.as_view()),
    #大模型提取PDF
    path('pdf/', views.VBllmView.as_view()),
    #大模型按页提取PDF内容
    path('pdf/page/', views.VBllmOneView.as_view()),
    # 提取pdf原文件内容
    path('pdf/text/', views.VBllmTextView.as_view()),
    # 经过大模型提取后保存到txt文件
    path('save/file/', views.VBSaveToFileFromCacheView.as_view()),
    #从缓存中获取切片数据，实现重新上传
    path('resave/', views.VBSaveFromCacheView.as_view()),
    path('delete/cache/', views.VBDeleteFromCacheView.as_view()),
    # 保存切片到数据库
    path('save/sql/', views.VBMySQLView.as_view()),
    
    path("segments/<int:uid_frame>", views.SegmentsView.as_view({'get':'segments'})),
    # 向量搜索
    path('vectorsearch/', views.VBSearchView.as_view()),
    path('indexsearch/', views.LlamaindexSearchView.as_view()),
    path('knowledgesearch/', views.KnowledgeSearchView.as_view({'get':'searchlist'})),


    path('lawlist/', views.LawView.as_view()),
    path('searchlist/', views.LawSearchView.as_view({'get':'searchlist'})),
    # path('tools/url2md/',webviews.WebExtractor.as_view()),
    path('tools/url2md/',webquenviews.WebExtractor.as_view()),

    path('task/<str:task_id>',webquenviews.TaskStatusView.as_view()),
    path('task_id/<str:task_id>',views.get_task_status)


    
]
