from rest_framework import permissions
from rest_framework.exceptions import APIException
import os

class InvalidAPIKey(APIException):
    status_code = 401
    default_detail = '无效的API key'

class VerifyAPIkey(permissions.BasePermission):
    message = '提供的API key 无效'
    
    @staticmethod
    def get_valid_api_keys():
        # 配置文件路径
        # config_path = os.getenv('API_KEYS_CONFIG_PATH', '../related/apikey.config')
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)),  'related', 'apikey.config')
        print('------',config_path)
        try:
            with open(config_path, 'r',encoding='utf-8') as file:
                # 读取每一行，并忽略井号(#)后面的注释
                return [line.split('#')[0].strip() for line in file.readlines() if line.strip() and not line.startswith('#')]
        except FileNotFoundError:
            return []

    def has_permission(self, request, view):
        valid_api_keys = self.get_valid_api_keys()
        print(valid_api_keys)
        api_key = request.META.get('HTTP_CH_API_KEY')
        print('api_key',api_key)
        if not api_key or api_key not in valid_api_keys:
            raise InvalidAPIKey()
        return True