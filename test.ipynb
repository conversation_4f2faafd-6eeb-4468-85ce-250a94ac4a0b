{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from pymilvus import Collection\n", "from pymilvus import connections"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# 1. 连接到 <PERSON><PERSON><PERSON><PERSON> 服务\n", "connections.connect(\"default\", host='*************', port='19955',user='chznroot',password='chznaihttp')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# 3. 创建集合\n", "collection_name = \"social\"\n", "collection = Collection(name=collection_name)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["An index already exists on the field 'vector'.\n"]}], "source": ["# 检查向量字段上是否存在索引\n", "# has_index, index = collection.has_index(field_name=\"vector\")\n", "# if has_index:\n", "#     print(\"An index already exists on the field 'vector'. Index details:\", index.params)\n", "# 检查向量字段上是否存在索引\n", "has_index = collection.has_index(field_name=\"vector\")\n", "if has_index:\n", "    print(\"An index already exists on the field 'vector'.\")\n", "\n", "    # 删除现有索引\n", "    # print(\"Dropping the existing index...\")\n", "    # collection.drop_index(field_name=\"vector\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index type: HNSW\n", "Index parameters: {'metric_type': 'L2', 'index_type': 'HNSW', 'params': {'M': 8, 'efConstruction': 64}}\n"]}], "source": ["# 检查向量字段上是否存在索引，并获取索引详情\n", "has_index = collection.has_index(field_name=\"vector\")\n", "if has_index:\n", "    # 获取索引的详细信息\n", "    index_info = collection.index(field_name=\"vector\")\n", "    print(f\"Index type: {index_info.params['index_type']}\")\n", "    print(f\"Index parameters: {index_info.params}\")\n", "    # 注意，索引的创建时间不是由 Milvus SDK 直接提供的。如果需要，可能必须通过 Milvus 的日志或其他内部机制来追踪。\n", "else:\n", "    print(\"No index exists on the field 'vector'.\")\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Field name: vector, Vector dimension: 1024\n"]}], "source": ["# 假设 collection 是你已经连接的 Milvus 集合对象\n", "\n", "# 获取集合的 schema\n", "collection_schema = collection.schema\n", "\n", "# 遍历 schema 中的字段\n", "for field in collection_schema.fields:\n", "    # 检查字段类型是否为 FLOAT_VECTOR 或 BINARY_VECTOR\n", "    if field.dtype == DataType.FLOAT_VECTOR or field.dtype == DataType.BINARY_VECTOR:\n", "        # 打印字段名称和向量维度\n", "        print(f\"Field name: {field.name}, Vector dimension: {field.params['dim']}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 5. 创建 HNSW 索引\n", "index_params = {\n", "    \"metric_type\": \"L2\",  # 使用 L2 距离\n", "    \"index_type\": \"HNSW\",\n", "    \"params\": {\"M\": 16, \"efConstruction\": 500}\n", "}\n", "collection.create_index(field_name=\"vector\", index_params=index_params)\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "\n", "load_dotenv(dotenv_path=\"./related/.env\", override=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义文档类，用于模拟问题中的文档结构\n", "class Document:\n", "    def __init__(self, page_content, metadata):\n", "        self.page_content = page_content\n", "        self.metadata = metadata\n", "    \n", "    # 遍历文档函数\n", "def iterate_documents(self,docs):\n", "    for doc in docs:\n", "        print(\"文档名:\", doc.metadata['file_name'])\n", "        print(\"内容:\")\n", "        print(doc.page_content)\n", "        print(\"-\" * 50)  # 打印分割线以区分不同文档的内容"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'VECTOR_DB_DRIVER'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mdatasheet\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mverctor_db\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m VectorDB\n\u001b[1;32m----> 2\u001b[0m vdb\u001b[38;5;241m=\u001b[39m\u001b[43mVectorDB\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m      3\u001b[0m ret \u001b[38;5;241m=\u001b[39m vdb\u001b[38;5;241m.\u001b[39mretriever([\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdefault\u001b[39m\u001b[38;5;124m'\u001b[39m],[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcriminal\u001b[39m\u001b[38;5;124m'\u001b[39m])\n\u001b[0;32m      4\u001b[0m res \u001b[38;5;241m=\u001b[39m ret\u001b[38;5;241m.\u001b[39mget_relevant_documents(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m第十四条 新闻、出版、文化、广播、电影\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32md:\\project\\LtdOrg\\related\\datasheet\\verctor_db.py:25\u001b[0m, in \u001b[0;36mVectorDB.__init__\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m     24\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m---> 25\u001b[0m     driver_name \u001b[38;5;241m=\u001b[39m \u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43menviron\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mVECTOR_DB_DRIVER\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[0;32m     26\u001b[0m     driver_class \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdriver_map\u001b[38;5;241m.\u001b[39mget(driver_name)\n\u001b[0;32m     27\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m driver_class:\n", "File \u001b[1;32mD:\\Program Files\\Python310\\lib\\os.py:680\u001b[0m, in \u001b[0;36m_Environ.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m    677\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_data[\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mencodekey(key)]\n\u001b[0;32m    678\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m:\n\u001b[0;32m    679\u001b[0m     \u001b[38;5;66;03m# raise KeyError with the original key value\u001b[39;00m\n\u001b[1;32m--> 680\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m    681\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdecodevalue(value)\n", "\u001b[1;31mKeyError\u001b[0m: 'VECTOR_DB_DRIVER'"]}], "source": ["from datasheet.verctor_db import VectorDB\n", "vdb=VectorDB()\n", "ret = vdb.retriever(['default'],['criminal'])\n", "res = ret.get_relevant_documents('第十四条 新闻、出版、文化、广播、电影')\n", "source_list =[]\n", "for i in res:\n", "    print(i.page_content)\n", "\n", "# print(res[0])\n", "# print(res[1])\n", "# for sr in res:\n", "#     data = {\n", "#         # \"page_content\":sr.page_content,\n", "#         \"file_name\":sr.metadata.get('file_name'),\n", "#         \"file_path\":sr.metadata.get('file_path')\n", "#     }\n", "#     if data not in source_list:  # 检查数据是否已存在于source_list中\n", "#         source_list.append(data)\n", "#     # source_url.append(sr.metadata.get('file_path'))\n", "# print(source_list)\n", "# for i in res:\n", "#     print(i.page_content)\n", "#     print(i.metadata)\n", "# print((res))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 6. 执行搜索\n", "search_params = {\"metric_type\": \"L2\", \"params\": {\"ef\": 64}}\n", "query_vectors = np.random.random((10, dim)).astype(np.float32).tolist()  # 假设我们有 10 个查询向量\n", "results = collection.search(\n", "    data=query_vectors,\n", "    anns_field=\"vector\",\n", "    param=search_params,\n", "    limit=5,  # 返回每个查询向量的前 5 个最相似向量\n", "    expr=None\n", ")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 7. 输出搜索结果\n", "for i, result in enumerate(results):\n", "    print(f\"Query {i}:\")\n", "    for hit in result:\n", "        print(f\"\\tID: {hit.id}, Distance: {hit.distance}\")\n", "\n", "# 断开连接\n", "connections.disconnect(\"default\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "# # 2. 定义集合的 Schema\n", "# dim = 128  # 假设我们的向量维度是 128\n", "# id_field = FieldSchema(name=\"id\", dtype=DataType.INT64, is_primary=True, auto_id=True)\n", "# vector_field = FieldSchema(name=\"embedding\", dtype=DataType.FLOAT_VECTOR, dim=dim)\n", "# schema = CollectionSchema(fields=[id_field, vector_field], description=\"Test collection\")\n", "\n", "\n", "\n", "# # 4. 插入数据\n", "# import numpy as np\n", "# num_vectors = 1000  # 假设我们要插入 1000 个向量\n", "# vectors = np.random.random((num_vectors, dim)).astype(np.float32).tolist()  # 生成随机数据\n", "# mr = collection.insert([vectors])\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "social", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 2}