import os
import time
import shutil
from pathlib import Path
from typing import List, Optional, Dict, Any

from django.core.files import File

from untils.webextract.webMain import ContentExtractor
from datasheet.sheetserializers import FrameSerializer

class WebExtractorConfig:
    """配置类，集中管理所有配置"""
    BASE_PATH = Path(__file__).parent.parent.parent
    WHISPER_MODEL = "base"
    MAX_COMMENTS = 20
    RAW_FOLDER = os.getenv("TEM_FOLDER", "data/tempy")
    
    # 平台配置
    PLATFORM_CONFIGS = {
        'youtube': {
            'enable_whisper': True,
            'include_comments': True,
            'include_subtitles': True,
            'use_whisper_if_no_subtitle': True
        },
        'bilibili': {
            'enable_whisper': True,
            'include_comments': True,
            'include_subtitles': True,
            'use_whisper_if_no_subtitle': True
        },
        'xiaohongshu': {
            'enable_whisper': True,
            'include_comments': True,
            'include_subtitles': True,
            'use_whisper_if_no_subtitle': True
        },
        'toutiao': {
            'enable_whisper': True,
            'include_comments': True,
            'include_subtitles': True,
            'use_whisper_if_no_subtitle': True
        },
        'wechat': {
            'enable_whisper': False,
            'include_comments': False,
            'include_subtitles': False,
            'use_whisper_if_no_subtitle': False
        }
    }


class ContentExtractorService:
    """内容提取服务类"""
    
    def __init__(self, config: WebExtractorConfig):
        self.config = config
    
    def _get_extractor(self, platform: str) -> ContentExtractor:
        """根据平台获取相应的提取器"""
        platform_config = self.config.PLATFORM_CONFIGS.get(platform, {})
        enable_whisper = platform_config.get('enable_whisper', False)
        
        return ContentExtractor(
            whisper_model=self.config.WHISPER_MODEL if enable_whisper else None,
            enable_whisper=enable_whisper
        )
    
    def _get_target_folder(self, username: str, platform: str) -> Path:
        """获取目标文件夹路径"""
        folder_path = Path(self.config.BASE_PATH) / 'media' / 'data' / 'web' / username / platform
        folder_path.mkdir(parents=True, exist_ok=True)
        return folder_path
    
    async def _extract_content(self, url: str, platform: str) -> Dict[str, Any]:
        """提取内容的通用方法"""
        extractor = self._get_extractor(platform)
        platform_config = self.config.PLATFORM_CONFIGS.get(platform, {})
        
        content = await extractor.extract_content(
            url,
            include_comments=platform_config.get('include_comments', False),
            include_subtitles=platform_config.get('include_subtitles', False),
            use_whisper_if_no_subtitle=platform_config.get('use_whisper_if_no_subtitle', False),
            max_comments=self.config.MAX_COMMENTS
        )
        
        if 'error' in content:
            raise Exception(f"提取失败: {content['error']}")
        
        return content
    
    async def _save_content(self, content: Dict[str, Any], username: str, platform: str) -> str:
        """保存内容到文件"""
        target_folder = self._get_target_folder(username, platform)
        timestamp = int(time.time())
        
        # 保存JSON文件
        json_filename = target_folder / f"{timestamp}_{platform}.json"
        extractor = self._get_extractor(platform)
        extractor.save_to_file(content, str(json_filename))
        
        # 保存纯文本文件
        text_filename = target_folder / f"{timestamp}_{platform}.txt"
        all_text = extractor.extract_all_text(content)
        
        import json
        if not all_text:
            all_text = json.dumps({'url':content['url'],'results':'提取失败'}, ensure_ascii=False)
        with open(text_filename, 'w', encoding='utf-8') as f:
            f.write(all_text)
        
        return str(text_filename)
    
    async def extract_and_save(self, url: str, username: str) -> str:
        """提取并保存内容"""
        # 检测平台
        extractor = ContentExtractor(enable_whisper=False)
        platform = extractor.detect_platform(url)
        
        if platform not in self.config.PLATFORM_CONFIGS:
            raise Exception(f"不支持的平台: {platform}")
        
        # 提取内容
        content = await self._extract_content(url, platform)
        
        # 保存内容
        return await self._save_content(content, username, platform)


class FileProcessor:
    """文件处理器"""
    
    @staticmethod
    def create_target_directory(username: str, folder_name: str) -> Path:
        """创建目标目录"""
        raw_folder = Path(WebExtractorConfig.RAW_FOLDER)
        target_dir = raw_folder / username / folder_name
        target_dir.mkdir(parents=True, exist_ok=True)
        return target_dir
    
    @staticmethod
    def process_file(file_path: str, target_dir: Path, uid_sheet: str, user_uuid: str, original_url: str) -> Dict[str, Any]:
        """处理单个文件"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 复制文件到目标目录
        target_file_path = target_dir / file_path.name
        shutil.copy2(file_path, target_file_path)
        
        # 创建Django文件对象并保存到数据库
        with open(file_path, 'rb') as f:
            django_file = File(f, name=file_path.name)
            
            timestamp = str(time.time()).replace(".", "")
            rename = timestamp + file_path.suffix
            
            data = {
                'cache_id': target_dir.name,
                'file': django_file,
                'file_size': file_path.stat().st_size,
                'file_name': file_path.name,
                'uid_sheet': uid_sheet,
                'rename': rename,
                'desc': '',
                'url': original_url,
                'uuid_user': user_uuid,
            }
            
            serializer = FrameSerializer(data=data)
            if serializer.is_valid():
                instance = serializer.save()
                return {'success': True, 'instance_id': instance.id}
            else:
                raise Exception(f"序列化失败: {serializer.errors}")
    @staticmethod
    def _preform_process_file(url_list: str, user: str, uid_sheet: str, folder_name: str ) -> Dict[str, Any]:
        """创建数据库记录 - 使用FrameSerializer"""
        from django.core.files.base import ContentFile
        # 创建一个空的文件对象作为占位符
        # 创建一个包含占位符内容的文件
        timestamp = str(time.time()).replace(".", "")
        rename = timestamp + '.txt'
        placeholder_content = b'placeholder content'
        placeholder_file = ContentFile(placeholder_content, name=rename)
        for url in url_list:
            data = {
                'cache_id': folder_name,
                'file': placeholder_file,
                'file_size': 0,  # 占位符，实际下载后更新
                'file_name': rename,
                'uid_sheet': uid_sheet,
                'rename': rename,
                'desc': '',
                'available': 3,
                'url': url,
                'uuid_user': user,
                'is_temp':True,
            }
            serializer = FrameSerializer(data=data)
            if serializer.is_valid():
                instance = serializer.save()
            else:
                raise Exception(f"序列化失败: {serializer.errors}")
