# ===================================================================
# 此文件已被注释 - 用于测试和演示目的
# 如需使用，请取消注释
# ===================================================================

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确度对比测试

比较文件过滤检索与整个知识库检索的精确度差异
"""

import requests
import json
from typing import List, Dict, Any


class PrecisionComparisonTest:
    """精确度对比测试类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_cases = [
            {
                "name": "技术文档查询",
                "query": "API接口调用方法",
                "selected_files": ["新建 文本文档 (2).txt"],
                "expected_relevance": "高",
                "scenario": "用户明确知道要在特定技术文档中查找"
            },
            {
                "name": "交通法规查询", 
                "query": "道路交通违章处理",
                "selected_files": ["道路交通.txt"],
                "expected_relevance": "高",
                "scenario": "用户只关注交通相关法规"
            },
            {
                "name": "综合知识查询",
                "query": "项目管理经验",
                "selected_files": ["新建 文本文档 (2).txt", "道路交通.txt"],
                "expected_relevance": "中",
                "scenario": "查询内容可能跨多个领域"
            }
        ]
    
    def test_full_knowledge_base_search(self, query: str) -> Dict[str, Any]:
        """测试整个知识库检索"""
        url = f"{self.base_url}/datasheet/vectorsearch/"
        
        payload = {
            'text': query,
            'database': ['default'],
            'collection': ['chzngk228'],
            'top_k': 10,
            'score_threshold': 0.45
        }
        
        try:
            response = requests.post(url, json=payload)
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'error': str(e)}
    
    def test_file_filtered_search(self, query: str, selected_files: List[str]) -> Dict[str, Any]:
        """测试文件过滤检索"""
        url = f"{self.base_url}/datasheet/filesearch/"
        
        payload = {
            'text': query,
            'database': ['default'],
            'collection': ['chzngk228'],
            'selected_files': selected_files,
            'top_k': 10,
            'score_threshold': 0.45
        }
        
        try:
            response = requests.post(url, json=payload)
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'error': str(e)}
    
    def analyze_results(self, full_results: Dict, filtered_results: Dict, test_case: Dict) -> Dict[str, Any]:
        """分析结果质量"""
        analysis = {
            'test_case': test_case['name'],
            'query': test_case['query'],
            'scenario': test_case['scenario']
        }
        
        # 分析整个知识库结果
        if 'data' in full_results:
            full_data = full_results['data']
            analysis['full_kb'] = {
                'total_results': len(full_data),
                'avg_score': self._calculate_avg_score(full_data),
                'file_diversity': self._calculate_file_diversity(full_data),
                'top_files': self._get_top_files(full_data, 3)
            }
        else:
            analysis['full_kb'] = {'error': full_results.get('error', 'Unknown error')}
        
        # 分析文件过滤结果
        if 'data' in filtered_results:
            filtered_data = filtered_results['data'].get('results', [])
            analysis['filtered'] = {
                'total_results': len(filtered_data),
                'avg_score': self._calculate_avg_score(filtered_data),
                'filter_expr': filtered_results['data'].get('filter_expr'),
                'focused_files': test_case['selected_files']
            }
        else:
            analysis['filtered'] = {'error': filtered_results.get('error', 'Unknown error')}
        
        # 精确度评估
        analysis['precision_assessment'] = self._assess_precision(analysis, test_case)
        
        return analysis
    
    def _calculate_avg_score(self, results: List[Dict]) -> float:
        """计算平均分数"""
        if not results:
            return 0.0
        
        scores = []
        for result in results:
            metadata = result.get('metadata', {})
            score = metadata.get('score', 0)
            if isinstance(score, (int, float)):
                scores.append(score)
        
        return sum(scores) / len(scores) if scores else 0.0
    
    def _calculate_file_diversity(self, results: List[Dict]) -> int:
        """计算文件多样性（涉及多少个不同文件）"""
        files = set()
        for result in results:
            metadata = result.get('metadata', {})
            file_name = metadata.get('file_name')
            if file_name:
                files.add(file_name)
        return len(files)
    
    def _get_top_files(self, results: List[Dict], limit: int = 3) -> List[str]:
        """获取得分最高的文件"""
        file_scores = {}
        for result in results:
            metadata = result.get('metadata', {})
            file_name = metadata.get('file_name', 'Unknown')
            score = metadata.get('score', 0)
            
            if file_name not in file_scores:
                file_scores[file_name] = []
            file_scores[file_name].append(score)
        
        # 计算每个文件的平均分数
        file_avg_scores = {
            file: sum(scores) / len(scores) 
            for file, scores in file_scores.items()
        }
        
        # 按分数排序
        sorted_files = sorted(file_avg_scores.items(), key=lambda x: x[1], reverse=True)
        return [file for file, score in sorted_files[:limit]]
    
    def _assess_precision(self, analysis: Dict, test_case: Dict) -> Dict[str, str]:
        """评估精确度"""
        assessment = {}
        
        # 基于场景评估
        scenario = test_case['scenario']
        
        if "特定" in scenario or "明确" in scenario:
            # 用户明确知道要在特定文档中查找
            assessment['recommendation'] = "文件过滤检索更适合"
            assessment['reason'] = "用户目标明确，过滤可以减少噪音"
        elif "跨多个领域" in scenario or "综合" in scenario:
            # 跨领域查询
            assessment['recommendation'] = "整个知识库检索可能更好"
            assessment['reason'] = "需要更广的信息覆盖面"
        else:
            assessment['recommendation'] = "需要根据具体结果判断"
            assessment['reason'] = "场景不够明确"
        
        # 基于结果数量评估
        full_count = analysis.get('full_kb', {}).get('total_results', 0)
        filtered_count = analysis.get('filtered', {}).get('total_results', 0)
        
        if filtered_count == 0:
            assessment['result_analysis'] = "文件过滤可能过于严格，建议扩大范围"
        elif filtered_count < full_count * 0.3:
            assessment['result_analysis'] = "文件过滤显著减少了结果数量，可能提高精确度"
        else:
            assessment['result_analysis'] = "文件过滤效果不明显"
        
        return assessment
    
    def run_comparison_test(self):
        """运行完整的对比测试"""
        print("=== Milvus检索精确度对比测试 ===\n")
        
        all_results = []
        
        for test_case in self.test_cases:
            print(f"🧪 测试案例: {test_case['name']}")
            print(f"📝 查询: {test_case['query']}")
            print(f"📁 选择文件: {test_case['selected_files']}")
            print(f"🎯 场景: {test_case['scenario']}")
            print("-" * 50)
            
            # 执行整个知识库检索
            print("1️⃣ 整个知识库检索...")
            full_results = self.test_full_knowledge_base_search(test_case['query'])
            
            # 执行文件过滤检索
            print("2️⃣ 文件过滤检索...")
            filtered_results = self.test_file_filtered_search(
                test_case['query'], 
                test_case['selected_files']
            )
            
            # 分析结果
            analysis = self.analyze_results(full_results, filtered_results, test_case)
            all_results.append(analysis)
            
            # 打印分析结果
            self._print_analysis(analysis)
            print("\n" + "="*60 + "\n")
        
        # 总结
        self._print_summary(all_results)
    
    def _print_analysis(self, analysis: Dict):
        """打印分析结果"""
        print("📊 结果分析:")
        
        # 整个知识库结果
        full_kb = analysis.get('full_kb', {})
        if 'error' not in full_kb:
            print(f"   整个知识库: {full_kb.get('total_results', 0)}个结果, "
                  f"平均分数: {full_kb.get('avg_score', 0):.3f}, "
                  f"涉及{full_kb.get('file_diversity', 0)}个文件")
            print(f"   主要文件: {', '.join(full_kb.get('top_files', []))}")
        else:
            print(f"   整个知识库: 错误 - {full_kb['error']}")
        
        # 文件过滤结果
        filtered = analysis.get('filtered', {})
        if 'error' not in filtered:
            print(f"   文件过滤: {filtered.get('total_results', 0)}个结果, "
                  f"平均分数: {filtered.get('avg_score', 0):.3f}")
            print(f"   过滤表达式: {filtered.get('filter_expr', 'None')}")
        else:
            print(f"   文件过滤: 错误 - {filtered['error']}")
        
        # 精确度评估
        precision = analysis.get('precision_assessment', {})
        print(f"💡 建议: {precision.get('recommendation', 'Unknown')}")
        print(f"📝 原因: {precision.get('reason', 'Unknown')}")
        print(f"📈 结果分析: {precision.get('result_analysis', 'Unknown')}")
    
    def _print_summary(self, all_results: List[Dict]):
        """打印总结"""
        print("🎯 总结建议:")
        print("-" * 30)
        
        scenarios = {
            "文件过滤更适合": 0,
            "整个知识库更适合": 0,
            "需要具体判断": 0
        }
        
        for result in all_results:
            recommendation = result.get('precision_assessment', {}).get('recommendation', '')
            if "文件过滤" in recommendation:
                scenarios["文件过滤更适合"] += 1
            elif "整个知识库" in recommendation:
                scenarios["整个知识库更适合"] += 1
            else:
                scenarios["需要具体判断"] += 1
        
        for scenario, count in scenarios.items():
            print(f"   {scenario}: {count}个测试案例")
        
        print("\n📋 使用建议:")
        print("   ✅ 用户明确知道信息在哪些文件中 → 使用文件过滤检索")
        print("   ✅ 需要跨领域、全面的信息 → 使用整个知识库检索")
        print("   ✅ 不确定时可以先用文件过滤，无结果再扩大范围")


if __name__ == '__main__':
    tester = PrecisionComparisonTest()
    tester.run_comparison_test()
