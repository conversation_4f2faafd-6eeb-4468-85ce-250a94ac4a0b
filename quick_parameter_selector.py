#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速参数选择器

提供简单的交互式参数选择工具
"""

import json
from typing import Dict, Any


class QuickParameterSelector:
    """快速参数选择器"""
    
    def __init__(self):
        self.presets = {
            "1": {
                "name": "快速预览",
                "description": "快速了解文档主要内容，响应速度快",
                "config": {
                    "batch_size": 30,
                    "min_score": 0.3,
                    "max_results": 80,
                    "enable_deep_analysis": False,
                    "analysis_type": "general"
                },
                "适用场景": ["初次了解文档", "快速概览", "时间紧急"]
            },
            "2": {
                "name": "标准分析",
                "description": "平衡质量和覆盖面，适合大多数场景",
                "config": {
                    "batch_size": 50,
                    "min_score": 0.2,
                    "max_results": 300,
                    "enable_deep_analysis": True,
                    "analysis_type": "general"
                },
                "适用场景": ["常规分析", "主题研究", "内容总结"]
            },
            "3": {
                "name": "深度分析",
                "description": "全面深入分析，获得最完整的信息",
                "config": {
                    "batch_size": 100,
                    "min_score": 0.15,
                    "max_results": 600,
                    "enable_deep_analysis": True,
                    "analysis_type": "general"
                },
                "适用场景": ["学术研究", "详细调研", "全面分析"]
            },
            "4": {
                "name": "技术文档",
                "description": "专门针对技术文档优化",
                "config": {
                    "batch_size": 60,
                    "min_score": 0.25,
                    "max_results": 250,
                    "enable_deep_analysis": True,
                    "analysis_type": "technical"
                },
                "适用场景": ["API文档", "技术手册", "代码文档"]
            },
            "5": {
                "name": "法律文档",
                "description": "专门针对法律条文优化",
                "config": {
                    "batch_size": 40,
                    "min_score": 0.35,
                    "max_results": 200,
                    "enable_deep_analysis": True,
                    "analysis_type": "legal"
                },
                "适用场景": ["法律条文", "政策文件", "规章制度"]
            },
            "6": {
                "name": "对比分析",
                "description": "多文档对比分析",
                "config": {
                    "batch_size": 80,
                    "min_score": 0.2,
                    "max_results": 500,
                    "enable_deep_analysis": True,
                    "analysis_type": "comparative"
                },
                "适用场景": ["多文档比较", "版本对比", "差异分析"]
            }
        }
    
    def show_presets(self):
        """显示预设选项"""
        print("🎯 请选择分析类型:")
        print("=" * 60)
        
        for key, preset in self.presets.items():
            print(f"{key}. {preset['name']}")
            print(f"   描述: {preset['description']}")
            print(f"   适用: {', '.join(preset['适用场景'])}")
            print()
    
    def get_config_by_choice(self, choice: str) -> Dict[str, Any]:
        """根据选择获取配置"""
        if choice in self.presets:
            return self.presets[choice]["config"].copy()
        else:
            print("❌ 无效选择，使用默认配置")
            return self.presets["2"]["config"].copy()
    
    def interactive_selection(self) -> Dict[str, Any]:
        """交互式选择"""
        self.show_presets()
        
        while True:
            try:
                choice = input("请输入选择 (1-6): ").strip()
                if choice in self.presets:
                    config = self.get_config_by_choice(choice)
                    preset_name = self.presets[choice]["name"]
                    print(f"\n✅ 已选择: {preset_name}")
                    return config
                else:
                    print("❌ 请输入有效的选择 (1-6)")
            except KeyboardInterrupt:
                print("\n👋 已取消选择")
                return self.presets["2"]["config"].copy()
    
    def customize_config(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """自定义配置"""
        print("\n🔧 是否需要自定义参数? (y/n): ", end="")
        if input().lower() != 'y':
            return base_config
        
        print("\n📝 自定义参数 (直接回车保持默认值):")
        
        # batch_size
        current = base_config["batch_size"]
        new_value = input(f"批次大小 (当前: {current}, 建议: 20-150): ").strip()
        if new_value.isdigit():
            base_config["batch_size"] = max(10, min(200, int(new_value)))
        
        # min_score
        current = base_config["min_score"]
        new_value = input(f"最低分数 (当前: {current}, 建议: 0.1-0.6): ").strip()
        try:
            score = float(new_value)
            if 0.0 <= score <= 1.0:
                base_config["min_score"] = score
        except ValueError:
            pass
        
        # max_results
        current = base_config["max_results"]
        new_value = input(f"最大结果数 (当前: {current}, 建议: 50-800): ").strip()
        if new_value.isdigit():
            base_config["max_results"] = max(10, min(1000, int(new_value)))
        
        # enable_deep_analysis
        current = "是" if base_config["enable_deep_analysis"] else "否"
        new_value = input(f"深度分析 (当前: {current}, y/n): ").strip().lower()
        if new_value in ['y', 'yes', '是']:
            base_config["enable_deep_analysis"] = True
        elif new_value in ['n', 'no', '否']:
            base_config["enable_deep_analysis"] = False
        
        return base_config
    
    def generate_request_example(self, config: Dict[str, Any], 
                                query: str = "示例查询", 
                                files: list = None) -> str:
        """生成请求示例"""
        if files is None:
            files = ["示例文档.txt"]
        
        request_data = {
            "text": query,
            "selected_files": files,
            "database": ["default"],
            "collection": ["chzngk228"],
            **config
        }
        
        return json.dumps(request_data, indent=2, ensure_ascii=False)
    
    def show_config_summary(self, config: Dict[str, Any]):
        """显示配置摘要"""
        print("\n📊 最终配置:")
        print("=" * 40)
        print(f"批次大小: {config['batch_size']}")
        print(f"最低分数: {config['min_score']}")
        print(f"最大结果: {config['max_results']}")
        print(f"分析类型: {config['analysis_type']}")
        print(f"深度分析: {'是' if config['enable_deep_analysis'] else '否'}")
        
        # 性能预估
        if config['max_results'] > 400 and config['enable_deep_analysis']:
            print("⏱️  预计响应时间: 较长 (30-60秒)")
        elif config['max_results'] > 200:
            print("⏱️  预计响应时间: 中等 (10-30秒)")
        else:
            print("⏱️  预计响应时间: 较快 (5-15秒)")
        
        # 质量预估
        if config['min_score'] > 0.4:
            print("🎯 结果质量: 高精度")
        elif config['min_score'] > 0.25:
            print("🎯 结果质量: 平衡")
        else:
            print("🎯 结果质量: 高覆盖")


def main():
    """主函数"""
    print("🚀 全文检索参数快速选择器")
    print("帮助您快速选择最适合的参数配置\n")
    
    selector = QuickParameterSelector()
    
    try:
        # 交互式选择
        config = selector.interactive_selection()
        
        # 自定义配置
        config = selector.customize_config(config)
        
        # 显示最终配置
        selector.show_config_summary(config)
        
        # 生成请求示例
        print("\n📝 请求示例:")
        print("=" * 40)
        
        # 获取用户输入
        query = input("请输入您的查询文本 (可选): ").strip()
        if not query:
            query = "示例查询内容"
        
        files_input = input("请输入文件名 (用逗号分隔, 可选): ").strip()
        if files_input:
            files = [f.strip() for f in files_input.split(',')]
        else:
            files = ["示例文档.txt"]
        
        example = selector.generate_request_example(config, query, files)
        print(example)
        
        print("\n✅ 配置完成! 您可以复制上述JSON用于API调用")
        
    except Exception as e:
        print(f"\n❌ 出现错误: {str(e)}")


def quick_select(scenario: str) -> Dict[str, Any]:
    """快速选择预设配置"""
    selector = QuickParameterSelector()
    
    scenario_map = {
        "快速": "1",
        "标准": "2", 
        "深度": "3",
        "技术": "4",
        "法律": "5",
        "对比": "6"
    }
    
    choice = scenario_map.get(scenario, "2")
    return selector.get_config_by_choice(choice)


if __name__ == "__main__":
    main()
