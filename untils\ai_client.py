import requests,os
from typing import Dict, List, Optional
from django.conf import settings
from loguru import logger

class AIClient:
    def __init__(self):
        self.base_url = os.environ.get('AI_SERVICE_URL', 'http://localhost:8080')
        self.headers = {'Content-Type': 'application/json','X-CH-API-KEY': os.environ.get('GATEWAY_KEY')}
        self.timeout = 60  # 增加超时时间，因为AI分析可能需要更长时间
    
    def analyze_content(self, content: str, analysis_type: str = 'summary', 
                       custom_fields: Optional[List[str]] = None,
                       options: Optional[Dict] = None) -> Dict:
        """
        调用AI分析接口
        
        Args:
            content: 要分析的内容
            analysis_type: 分析类型 ('summary', 'structure', 'sentiment', 'keywords', 'full')
            custom_fields: 自定义返回字段列表
            options: 额外选项 (model, temperature, max_tokens)
        
        Returns:
            分析结果字典
        """
        try:
            url = f"{self.base_url}/proxy/api/ask/generator"
            logger.info(f"请求AI服务,url: {url}")
            payload = {
                "content": content,
                "analysis_type": analysis_type
            }
            
            if custom_fields:
                payload["custom_fields"] = custom_fields
            
            if options:
                payload["options"] = options
            
            logger.info(f"调用AI分析接口,类型: {analysis_type}")
            
            response = requests.post(
                url,
                json=payload,
                headers=self.headers,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"AI分析成功,类型: {analysis_type}")
                return result
            else:
                logger.error(f"AI服务返回错误: {response.status_code}, {response.text}")
                return {
                    "success": False,
                    "message": f"AI服务错误: {response.status_code}"
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"AI服务调用异常: {str(e)}")
            return {
                "success": False,
                "message": f"网络错误: {str(e)}"
            }
    
    def get_summary_and_topic(self, content: str) -> Dict:
        """获取摘要和主题（原有接口保持兼容）"""
        return self.analyze_content(content, 'summary')
    
    def get_structured_data(self, content: str) -> Dict:
        """获取结构化数据"""
        return self.analyze_content(content, 'structure')
    
    def get_keywords(self, content: str) -> Dict:
        """获取关键词"""
        return self.analyze_content(content, 'keywords')
    
    def get_sentiment(self, content: str) -> Dict:
        """获取情感分析"""
        return self.analyze_content(content, 'sentiment')
    
    def get_full_analysis(self, content: str) -> Dict:
        """获取完整分析"""
        return self.analyze_content(content, 'full')
    
    def get_custom_analysis(self, content: str, fields: List[str]) -> Dict:
        """自定义字段分析"""
        return self.analyze_content(content, 'full', custom_fields=fields)
    

# Python调用示例
# ai_client = AIClient()

# # 基础摘要分析
# result = ai_client.get_summary_and_topic(content)

# # 结构化分析
# result = ai_client.get_structured_data(content)

# # 自定义分析
# result = ai_client.get_custom_analysis(content, ['summary', 'keywords'])

# # 带选项的完整分析
# result = ai_client.analyze_content(
#     content, 
#     'full',
#     options={'model': 'gpt-4', 'temperature': 0.5}
# )