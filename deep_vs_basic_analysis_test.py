#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析 vs 基础分析对比测试

验证 enable_deep_analysis 参数是否真的有区别
"""

import requests
import json
import time
from typing import Dict, List, Any


class DeepVsBasicAnalysisTest:
    """深度分析 vs 基础分析对比测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
    
    def test_analysis_difference(self, query: str, files: List[str]):
        """测试深度分析和基础分析的区别"""
        print(f"🧪 深度分析 vs 基础分析对比测试")
        print(f"查询: {query}")
        print(f"文件: {files}")
        print("=" * 60)
        
        # 测试基础分析
        print("\n1️⃣ 基础分析 (enable_deep_analysis=False):")
        basic_result = self._call_full_text_search({
            'text': query,
            'selected_files': files,
            'enable_deep_analysis': False,
            'max_results': 200
        })
        
        if 'error' not in basic_result:
            self._analyze_result(basic_result, "基础分析")
        else:
            print(f"   ❌ 基础分析失败: {basic_result['error']}")
        
        # 测试深度分析
        print("\n2️⃣ 深度分析 (enable_deep_analysis=True):")
        deep_result = self._call_full_text_search({
            'text': query,
            'selected_files': files,
            'enable_deep_analysis': True,
            'max_results': 200
        })
        
        if 'error' not in deep_result:
            self._analyze_result(deep_result, "深度分析")
        else:
            print(f"   ❌ 深度分析失败: {deep_result['error']}")
        
        # 对比分析
        if 'error' not in basic_result and 'error' not in deep_result:
            self._compare_results(basic_result, deep_result)
    
    def _call_full_text_search(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """调用全文检索API"""
        url = f"{self.base_url}/datasheet/fulltextsearch/"
        default_params = {
            'database': ['default'],
            'collection': ['chzngk228']
        }
        
        payload = {**default_params, **params}
        
        try:
            start_time = time.time()
            response = requests.post(url, json=payload, timeout=60)
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                result['_response_time'] = round(end_time - start_time, 2)
                return result
            else:
                return {'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'error': str(e)}
    
    def _analyze_result(self, result: Dict[str, Any], analysis_type: str):
        """分析结果"""
        data = result.get('data', {})
        
        print(f"   📊 {analysis_type}结果:")
        print(f"      分析方法: {data.get('analysis_method', 'unknown')}")
        print(f"      片段数量: {data.get('total_segments', 0)}")
        print(f"      响应时间: {result.get('_response_time', 0)}秒")
        
        # 检查基础统计
        if 'basic_statistics' in data:
            stats = data['basic_statistics']
            print(f"      平均分数: {stats.get('avg_score', 0):.3f}")
        
        # 检查关键主题
        if 'key_themes' in data:
            themes = data['key_themes']
            print(f"      关键主题: {len(themes)}个")
            if themes:
                top_themes = [t.get('word', 'N/A') for t in themes[:3]]
                print(f"      主要主题: {', '.join(top_themes)}")
        
        # 检查实体识别
        if 'entities' in data:
            entities = data['entities']
            total_entities = sum(len(entity_list) for entity_list in entities.values())
            print(f"      识别实体: {total_entities}个")
            
            for entity_type, entity_list in entities.items():
                if entity_list:
                    print(f"        - {entity_type}: {len(entity_list)}个")
        
        # 检查内容聚类
        if 'content_clusters' in data:
            clusters = data['content_clusters']
            print(f"      内容聚类: {len(clusters)}个")
        
        # 检查结构化总结
        if 'structured_summary' in data:
            summary = data['structured_summary']
            print(f"      结构化总结: 是")
            
            key_findings = summary.get('key_findings', [])
            if key_findings:
                print(f"      关键发现: {len(key_findings)}个")
        
        # 检查内容预览
        if 'content_previews' in data:
            previews = data['content_previews']
            print(f"      内容预览: {len(previews)}个")
        
        # 检查备注信息
        if 'note' in data:
            print(f"      说明: {data['note']}")
    
    def _compare_results(self, basic_result: Dict[str, Any], deep_result: Dict[str, Any]):
        """对比两种分析结果"""
        print(f"\n📊 对比分析:")
        print("=" * 40)
        
        basic_data = basic_result.get('data', {})
        deep_data = deep_result.get('data', {})
        
        # 对比分析方法
        basic_method = basic_data.get('analysis_method', 'unknown')
        deep_method = deep_data.get('analysis_method', 'unknown')
        print(f"分析方法: {basic_method} vs {deep_method}")
        
        # 对比响应时间
        basic_time = basic_result.get('_response_time', 0)
        deep_time = deep_result.get('_response_time', 0)
        print(f"响应时间: {basic_time}秒 vs {deep_time}秒")
        
        if deep_time > basic_time:
            print(f"  ⏱️ 深度分析耗时更长 (+{deep_time - basic_time:.1f}秒)")
        
        # 对比功能差异
        print(f"\n功能对比:")
        
        # 关键主题
        basic_themes = len(basic_data.get('key_themes', []))
        deep_themes = len(deep_data.get('key_themes', []))
        print(f"  关键主题: {basic_themes} vs {deep_themes}")
        
        # 实体识别
        basic_entities = self._count_entities(basic_data.get('entities', {}))
        deep_entities = self._count_entities(deep_data.get('entities', {}))
        print(f"  实体识别: {basic_entities} vs {deep_entities}")
        
        # 内容聚类
        basic_clusters = len(basic_data.get('content_clusters', []))
        deep_clusters = len(deep_data.get('content_clusters', []))
        print(f"  内容聚类: {basic_clusters} vs {deep_clusters}")
        
        # 结构化总结
        basic_summary = 'structured_summary' in basic_data
        deep_summary = 'structured_summary' in deep_data
        print(f"  结构化总结: {'有' if basic_summary else '无'} vs {'有' if deep_summary else '无'}")
        
        # 总结差异
        print(f"\n💡 差异总结:")
        
        if deep_method != basic_method:
            print(f"  ✅ 使用了不同的分析方法")
        
        if deep_entities > basic_entities:
            print(f"  ✅ 深度分析识别了更多实体 (+{deep_entities - basic_entities})")
        
        if deep_clusters > basic_clusters:
            print(f"  ✅ 深度分析提供了更多聚类 (+{deep_clusters - basic_clusters})")
        
        if deep_summary and not basic_summary:
            print(f"  ✅ 深度分析提供了结构化总结")
        
        if deep_themes > basic_themes:
            print(f"  ✅ 深度分析提取了更多主题 (+{deep_themes - basic_themes})")
        
        # 判断是否有实质性差异
        has_difference = (
            deep_method != basic_method or
            deep_entities > basic_entities or
            deep_clusters > basic_clusters or
            (deep_summary and not basic_summary) or
            deep_themes > basic_themes
        )
        
        if has_difference:
            print(f"\n🎯 结论: enable_deep_analysis 参数确实有效果！")
        else:
            print(f"\n⚠️ 结论: enable_deep_analysis 参数效果不明显")
    
    def _count_entities(self, entities: Dict[str, List]) -> int:
        """统计实体总数"""
        return sum(len(entity_list) for entity_list in entities.values())


def main():
    """主函数"""
    print("🧪 深度分析 vs 基础分析对比测试")
    print("验证 enable_deep_analysis 参数是否真的有区别")
    
    tester = DeepVsBasicAnalysisTest()
    
    # 测试场景
    test_cases = [
        {
            'query': '交通违法处理程序',
            'files': ['道路交通.txt'],
            'description': '法律文档分析'
        },
        {
            'query': '战争策略分析',
            'files': ['三国演义.txt'],
            'description': '文学作品分析'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"📋 测试场景 {i}: {case['description']}")
        
        try:
            tester.test_analysis_difference(case['query'], case['files'])
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
    
    print(f"\n✅ 对比测试完成!")
    print(f"\n💡 如果深度分析和基础分析有明显差异，说明参数设置有效")
    print(f"   如果差异不明显，可能需要进一步优化实现")


if __name__ == '__main__':
    main()
