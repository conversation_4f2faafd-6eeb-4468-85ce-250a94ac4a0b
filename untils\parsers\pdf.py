import fitz

from untils.common import Timer
from loguru import logger

class PdfParser:
    def parse(self, pdf_path):
        text = self.extract_text_from_pdf(pdf_path)
        return text

    @Timer
    def extract_text_from_pdf(self, pdf_path):
        try:
            doc = fitz.open(pdf_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
            return text
    
        except Exception as e:
                logger.error(f"Failed to parse the .pdf file: {e}")
                return None
