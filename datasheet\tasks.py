from celery import shared_task
from datasheet.verctor_db import VectorDB
from untils.parsers.parser import DocumentParser
from untils.common import get_all_files, calculate_md5
import os
import shutil
import zipfile
from untils.exception import ExceptException
from datasheet.models import DataFrame
from django.conf import settings

from loguru import logger
from celery import shared_task
import gc
from typing import List
from langchain.docstore.document import Document
from django.core.cache import cache
from .models import Page
# @shared_task(bind=True)
# def parser_to_vector(
#     self, 
#     target_folder_dir, 
#     data_base, 
#     collecte_id,  
#     username,
#     chunksize=512, 
#     chunkoverlap=200):
#     def callback(current: float, stage: str):
        
#         progress = (current + 1) / stage * 100
#         self.update_state(
#             state="PROGRESS", 
#             meta={
#                 "progress": "{:.2f}%".format(progress),
#                 "total": stage,
#                 "current": current})
#     try:
#         doc_parser = DocumentParser()
#         saved_folder_dir = doc_parser.parse(target_folder_dir,username)
#         # shutil.rmtree(target_folder_dir)  # 如果需要删除解析过的目录，取消注释此行
#         cnt = flush_vector_db_from_folder(
#             saved_folder_dir, 
#             data_base, 
#             collecte_id,  
#             chunksize, 
#             chunkoverlap,
#             callback)

#         # 任务完成后删除临时目录
#         if os.path.exists(target_folder_dir) and os.path.isdir(target_folder_dir):
#             files = get_all_files(target_folder_dir)
#             for file in files:
#                 file_name=os.path.basename(file)
#                 file_size = os.path.getsize(file)
#                 logger.warning(f'remove parser folder{target_folder_dir}/{file_name}/{file_size}')
#                 # logger.info()  # 获取文件大小)
#                 f = DataFrame.objects.get(file_name=file_name)
#                 f.available = not f.available
#                 f.save()
#             shutil.rmtree(target_folder_dir)
#         # 任务完成后删除解析目录
#         if os.path.exists(saved_folder_dir) and os.path.isdir(saved_folder_dir):
#             logger.warning(f'remove parser folder{saved_folder_dir}/*')
#             shutil.rmtree(saved_folder_dir)

#         # 任务结束回收内存
#         gc.collect()

#         # 生成训练扣费记录
#         # try:
#         #     user = Members.objects.get(id = user_id)

#         #     recordobj=Record.objects.create(
#         #         pay_amount=cnt*2/int(settings.RATES),
#         #         record_from=4,
#         #         uuid_user=user,
#         #     )
#         #     if recordobj:
#         #         return user.update_balance(cnt)         
#         # except Exception as e:
#         #     logger.error(str(e))

#     except Exception as e:
#         logger.error(f"Task failed: {str(e)}")
#         raise  # 重新抛出异常，以便Celery识别任务失败

#     return cnt

# def flush_vector_db_from_folder(
#         target_folder_dir, 
#         database_name, 
#         collection_name,  
#         chunk_size, 
#         chunk_overlap,
#         callback):
#     vector_db = VectorDB()
#     cnt = vector_db.insert_from_folder(
#         target_folder_dir, 
#         database_name, 
#         collection_name,  
#         chunk_size, 
#         chunk_overlap,
#         callback )
#     return cnt

from untils.untils import MixinUntils
from datasheet.models import DataFrame
from .sheetserializers import FrameSerializer
import platform
from untils.ai_client import AIClient
import json
import asyncio
from untils.webextract.webCommon import ContentExtractorService, WebExtractorConfig,FileProcessor
import time,gc


def extract_web_content(
    urls: List[str], username: str, uid_sheet: str, user_uuid: str, folder_name: str
):
    """第一步：提取网页内容 - 保持原有逻辑"""
    try:
        # 创建服务实例
        service = ContentExtractorService(WebExtractorConfig())
        processor = FileProcessor()

        # 创建目标目录
        target_dir = processor.create_target_directory(username, folder_name)

        async def process_urls():
            results = []
            for url in urls:
                try:
                    file_path = await service.extract_and_save(url, username)
                    results.append({"url": url, "file_path": file_path})
                except Exception as e:
                    logger.error(f"提取URL失败 {url}: {str(e)}")
                    results.append({"error": str(e), "url": url})
            return results

        # 运行异步任务
        file_paths = asyncio.run(process_urls())

        # 处理文件
        processed_files = []
        for items in file_paths:
            if "error" in items:
                processed_files.append(items)
                continue
            file_path = items["file_path"]
            original_url = items["url"]  # Retrieve original URL

            try:
                result = processor.process_file(
                    file_path, target_dir, uid_sheet, user_uuid, original_url
                )
                processed_files.append({"file_path": file_path, "result": result})
                print(f"Processed file: {result}")
                print(f"Processed file: {file_path}")
                extract_preview.delay(file_path, result['instance_id'])
            except Exception as e:
                logger.error(f"处理文件失败 {file_path}: {str(e)}")
                processed_files.append({"file_path": file_path, "error": str(e)})

        return {
            "status": "completed",
            "target_folder": folder_name,
            "processed_files": processed_files,
        }

    except Exception as e:
        logger.error(f"网页内容提取失败: {str(e)}")
        return {"status": "failed", "error": str(e), "target_folder": folder_name}


@shared_task(bind=True, max_retries=3)
def complete_web_pipeline(
    self,
    urls: List[str],
    username: str,
    uid_sheet: str,
    user_uuid: str,
    folder_name: str = None,
):
    """
    完整的网页处理流水线：提取网页内容 -> 切片 -> 保存到MySQL -> 向量存储
    直接调用现有的类视图API
    """
    try:
        # 更新任务状态
        self.update_state(
            state="PROGRESS",
            meta={
                "stage": "extracting",
                "progress": 0,
                "message": "开始提取网页内容...",
            },
        )

        # 生成文件夹名
        if not folder_name:
            timestr = str(time.time()).replace(".", "")
            folder_name = f"web_{timestr}"

        # 第一步：提取网页内容
        extraction_result = extract_web_content(
            urls, username, uid_sheet, user_uuid, folder_name
        )

        if extraction_result.get("status") != "completed":
            return extraction_result

        self.update_state(
            state="PROGRESS",
            meta={
                "stage": "splitting",
                "progress": 25,
                "message": "网页内容提取完成，开始切片处理...",
            },
        )

        from .views import BaseView
        from django.http import HttpRequest
        from members.models import Members

        base = BaseView()
        fake_request = HttpRequest()
        fake_request.user = Members.objects.get(username=username)
        fake_request.method = "POST"

        # 直接设置需要的属性
        # fake_request.POST = QueryDict('', mutable=True)

        # # 从kwargs中提取前端传来的参数
        # chunk_size = kwargs.get('chunk_size', 1000)
        # overlap = kwargs.get('overlap', 200)
        # processing_mode = kwargs.get('processing_mode', 'default')

        # fake_request.POST['chunk_size'] = str(chunk_size)
        # fake_request.POST['overlap'] = str(overlap)
        # fake_request.POST['processing_mode'] = processing_mode

        base.request = fake_request
        # 第二步：切片处理 - 直接调用 VBSplitsView
        split_result = base.handle_split_folder(folder_name)

        if split_result.get("status") != "completed":
            return split_result

        self.update_state(
            state="PROGRESS",
            meta={
                "stage": "saving_to_mysql",
                "progress": 50,
                "message": "切片完成，保存到MySQL数据库...",
            },
        )

        # 第三步：保存切片到MySQL - 直接调用 VBMySQLView
        mysql_result = base.handle_mysql(folder_name)

        if mysql_result.get("status") != "completed":
            return mysql_result

        self.update_state(
            state="PROGRESS",
            meta={
                "stage": "vectorizing",
                "progress": 75,
                "message": "数据库保存完成，开始向量化存储...",
            },
        )

        # 第四步：向量化存储 - 直接调用 VBMilvusView
        vector_result = base.handle_milvus(uid_sheet, folder_name)

        if vector_result.get("status") != "completed":
            return vector_result

        self.update_state(
            state="PROGRESS",
            meta={
                "stage": "cleanup",
                "progress": 90,
                "message": "向量化完成，清理临时文件...",
            },
        )

        # 第五步：清理临时文件
        base.cleanup_prefrom(folder_name, username)

        self.update_state(
            state="SUCCESS",
            meta={"stage": "completed", "progress": 100, "message": "所有处理完成！"},
        )

        return {
            "status": "completed",
            "folder_name": folder_name,
            "extraction_result": extraction_result,
            "split_result": split_result,
            "mysql_result": mysql_result,
            "vector_result": vector_result,
            "message": "网页内容提取、切片和向量存储全部完成",
        }

    except Exception as e:
        logger.error(f"Complete web processing pipeline failed: {str(e)}")

        # 重试机制
        if self.request.retries < self.max_retries:
            logger.error(f"Retrying after 60 seconds...")
            # raise self.retry(countdown=60, exc=e)
        else:
            return {
                "status": "failed",
                "error": str(e),
                "folder_name": folder_name,
                "message": "处理失败，已达到最大重试次数",
            }


# Celery任务定义
@shared_task(bind=True, max_retries=3)
def extract_urls_task(self, urls: List[str], username: str, uid_sheet: str, user_uuid: str, folder_name: str):
    """Celery异步任务：提取URL内容"""
    try:
        # 创建服务实例
        service = ContentExtractorService(WebExtractorConfig())
        processor = FileProcessor()

        # 创建目标目录
        target_dir = processor.create_target_directory(username, folder_name)

        async def process_urls():
            results = []
            for url in urls:
                try:
                    file_path = await service.extract_and_save(url, username)
                    results.append(file_path)
                except Exception as e:
                    # 记录错误但继续处理其他URL
                    results.append({'error': str(e), 'url': url})
            return results

        # 运行异步任务
        file_paths = asyncio.run(process_urls())

        # 处理文件
        processed_files = []
        for file_path in file_paths:
            if isinstance(file_path, dict) and 'error' in file_path:
                processed_files.append(file_path)
                continue

            try:
                result = processor.process_file(file_path, target_dir, uid_sheet, user_uuid)
                processed_files.append({
                    'file_path': file_path,
                    'result': result
                })
            except Exception as e:
                processed_files.append({
                    'file_path': file_path,
                    'error': str(e)
                })

        return {
            'status': 'completed',
            'target_folder': folder_name,
            'processed_files': processed_files
        }

    except Exception as e:
        # 重试机制
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60, exc=e)
        else:
            return {
                'status': 'failed',
                'error': str(e),
                'target_folder': folder_name
            }

@shared_task(bind=True)
def extract_preview(self,path,instan_id):
    try:
        mu = MixinUntils()
        preview = mu.parser_file(path)
        # with open(f_path,'rb') as f:
        #     preview = mu.extract_preview(path)

        logger.warning(f'preview: {len(preview)}')
        if len(preview) > 100:
            result =  AIClient().analyze_content(preview, 'summary')
            logger.warning(f'result: {result}')
            
            frame_instance = DataFrame.objects.get(id = instan_id)
            frame_instance.desc = preview
            if result.get("success"):
                data = json.loads(result.get("data", {}))
                logger.warning(f'data: {data}')
                # 根据分析类型更新不同字段
                if 'summary' in ['summary', 'full']:
                    frame_instance.summary = data.get("summary", "")
                    frame_instance.topic = data.get("topic", "")
                    frame_instance.related_questions = data.get("related_questions", "")
                    frame_instance.ai_status="completed"
            else:
                frame_instance.ai_status="failed"
                frame_instance.desc = preview
                logger.error(f"AI分析失败: {result.get('message')}")
            frame_instance.save()
    except Exception as e:
        logger.error(f"Error extracting preview: {str(e)}")
    

@shared_task(bind=True)
def split_to_mysql(self,folder:str):    
        
    # cache_data = cache.get(upload_id)
    split_id = f'split_{folder}'
    from untils.untils import MixinUntils
    cache_data = MixinUntils.get_frame_info(folder)
    docs=cache.get(split_id)
    try:
        # 根据缓存ID判断缓存中是否有对应的数据
        if not cache_data or not docs:
            raise ValueError(f"保存失败：编号{folder.split('_')[1]}，没有可存储的数据")        
        cache_map = {cdata['file_name']:cdata for cdata in cache_data}
        try:
            # 序列号数据，将Documents转成dict保存到MySQL数据库
            for doc in docs:
                doc_name = doc[0].metadata.get('file_name')
                if doc_name in cache_map:
                    frame_id = cache_map[doc_name].get('id')
                    logger.warning(f'save {doc_name} : {len(doc)} pics')
                    for doc_item in doc:
                        data={
                            'uid_frame_id':int(frame_id),
                            'folder_id':folder,
                            'page_content':doc_item.page_content,
                            'file_name':doc_item.metadata.get('file_name'),
                            'file_path':doc_item.metadata.get('file_path')
                        }
                        ser = Page(**data)
                        ser.save()
                else:
                    logger.error(f'No data found for {doc_name}')
                
        
        except Exception as err:
            logger.error(f"保存切片出错: {str(err)}")
            raise err
    except Exception as e:
        logger.error(f'Error when task insert to mysql : {str(e)}')
        self.update_state(
            state='FAILURE',
            meta={
                'exc_type': type(e).__name__,
                'exc_message': str(e)}
        )
        raise e
    
@shared_task(bind=True)
def insert_to_vector(self,cacheid:str, database: str, collection: str,filter_id):
    
    def callback(current: float, stage: str):
        progress = (current + 1) / stage * 100
        self.update_state(
            state="PROGRESS", 
            meta={
                "progress": "{:.2f}%".format(progress),
                "total": stage,
                "current": current})
    try:
        vector_db = VectorDB()
        vector_db.insert_from_cache(cacheid,database,collection,filter_id,callback)
    except Exception as e:
        logger.error(f'Error when insert to vector : {str(e)}')
        self.update_state(
            state='FAILURE',
            meta={
                'exc_type': type(e).__name__,
                'exc_message': str(e)}
        )
        raise e
    
@shared_task(bind=True)
def delete_from_vector(self,filter_id:list, database: str, collection: str,):
    
    def callback(current: float, stage: str):
        progress = (current + 1) / stage * 100
        self.update_state(
            state="PROGRESS", 
            meta={
                "progress": "{:.2f}%".format(progress),
                "total": stage,
                "current": current})
    try:
        vector_db = VectorDB()
        vector_db.delete_by_frame_id(filter_id,database,collection,callback)
    except Exception as e:
        logger.error(f'Error when delete to vector : {str(e)}')
        self.update_state(
            state='FAILURE',
            meta={
                'exc_type': type(e).__name__,
                'exc_message': str(e)}
        )
        raise e
    
def search_vector_db(search_text,region_id=None, org_id='default_coll', user_id='default_set',top_k=5,max_distance=0.64):
    vector_db = VectorDB()

    logger.info(f'search {region_id}/{org_id}/{user_id}')
    result = vector_db.search(
        search_text,
        database_name=region_id,
        collection_name=org_id,
        partition_name=user_id,
        top_k=top_k,
        max_distance = max_distance
    )
    return result