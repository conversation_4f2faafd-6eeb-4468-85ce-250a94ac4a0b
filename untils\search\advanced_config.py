#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级文件检索配置

提供多种检索策略和优化配置
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum


class SearchStrategy(Enum):
    """搜索策略枚举"""
    PRECISE = "precise"      # 精确搜索
    BROAD = "broad"          # 广泛搜索  
    ADAPTIVE = "adaptive"    # 自适应搜索
    BALANCED = "balanced"    # 平衡搜索


class QueryEnhancementLevel(Enum):
    """查询增强级别"""
    NONE = "none"           # 不增强
    BASIC = "basic"         # 基础增强
    ADVANCED = "advanced"   # 高级增强
    AGGRESSIVE = "aggressive"  # 激进增强


@dataclass
class SearchConfig:
    """搜索配置"""
    # 基础参数
    top_k: int = 10
    score_threshold: float = 0.45
    max_token_limit: int = 512
    
    # 搜索策略
    search_strategy: SearchStrategy = SearchStrategy.ADAPTIVE
    
    # 查询增强
    query_enhancement_level: QueryEnhancementLevel = QueryEnhancementLevel.BASIC
    enable_synonym_expansion: bool = True
    enable_keyword_extraction: bool = True
    
    # 结果优化
    enable_deduplication: bool = True
    enable_reranking: bool = True
    enable_fallback: bool = True
    
    # 文件过滤
    file_type_weights: Dict[str, float] = None
    content_length_preference: str = "medium"  # short, medium, long, any
    
    # 高级选项
    enable_multi_query: bool = True
    enable_context_awareness: bool = True
    enable_smart_threshold: bool = True
    
    def __post_init__(self):
        if self.file_type_weights is None:
            self.file_type_weights = {
                'pdf': 1.0,
                'docx': 0.9,
                'txt': 0.8,
                'md': 0.85,
                'html': 0.7
            }


class AdvancedSearchOptimizer:
    """高级搜索优化器"""
    
    def __init__(self, config: SearchConfig = None):
        self.config = config or SearchConfig()
    
    def optimize_parameters(self, query: str, file_info: Dict, context: Dict = None) -> Dict[str, Any]:
        """
        根据查询和上下文优化搜索参数
        
        Args:
            query: 搜索查询
            file_info: 文件信息
            context: 上下文信息
            
        Returns:
            优化后的参数
        """
        optimized = {
            'top_k': self.config.top_k,
            'score_threshold': self.config.score_threshold,
            'search_strategy': self.config.search_strategy.value
        }
        
        # 基于查询长度优化
        query_length = len(query.split())
        if query_length <= 2:
            # 短查询：降低阈值，增加结果数
            optimized['score_threshold'] = max(0.3, self.config.score_threshold - 0.15)
            optimized['top_k'] = min(self.config.top_k * 1.5, 20)
        elif query_length >= 8:
            # 长查询：提高阈值，保证质量
            optimized['score_threshold'] = min(0.7, self.config.score_threshold + 0.1)
        
        # 基于文件数量优化
        file_count = len(file_info)
        if file_count == 1:
            # 单文件：可以更宽松
            optimized['score_threshold'] = max(0.25, optimized['score_threshold'] - 0.1)
            optimized['top_k'] = min(optimized['top_k'] * 2, 25)
        elif file_count > 10:
            # 多文件：需要更严格
            optimized['score_threshold'] = min(0.8, optimized['score_threshold'] + 0.15)
        
        # 基于文件类型优化
        file_types = [info.get('type', 'unknown') for info in file_info.values()]
        if 'pdf' in file_types:
            # PDF文档通常质量较高
            optimized['score_threshold'] = min(0.75, optimized['score_threshold'] + 0.05)
        
        # 智能阈值调整
        if self.config.enable_smart_threshold:
            optimized['score_threshold'] = self._calculate_smart_threshold(
                query, file_info, optimized['score_threshold']
            )
        
        return optimized
    
    def _calculate_smart_threshold(self, query: str, file_info: Dict, base_threshold: float) -> float:
        """计算智能阈值"""
        # 基于查询复杂度
        complexity_score = 0.0
        
        # 查询长度因子
        query_words = query.split()
        if len(query_words) <= 2:
            complexity_score += 0.1  # 简单查询
        elif len(query_words) >= 6:
            complexity_score -= 0.1  # 复杂查询
        
        # 专业术语因子
        technical_terms = ['API', '接口', '配置', '系统', '错误', '异常']
        if any(term in query for term in technical_terms):
            complexity_score -= 0.05  # 技术查询通常更精确
        
        # 文件质量因子
        avg_file_quality = self._estimate_file_quality(file_info)
        if avg_file_quality > 0.8:
            complexity_score -= 0.05  # 高质量文件可以降低阈值
        elif avg_file_quality < 0.5:
            complexity_score += 0.1   # 低质量文件需要提高阈值
        
        # 应用调整
        smart_threshold = base_threshold + complexity_score
        return max(0.2, min(0.9, smart_threshold))
    
    def _estimate_file_quality(self, file_info: Dict) -> float:
        """估算文件质量"""
        if not file_info:
            return 0.5
        
        quality_scores = []
        for info in file_info.values():
            score = 0.5  # 基础分数
            
            # 文件类型权重
            file_type = info.get('type', 'unknown')
            type_weight = self.config.file_type_weights.get(file_type, 0.5)
            score += (type_weight - 0.5) * 0.4
            
            # 文件大小因子
            file_size = info.get('size', 0)
            if 1000 <= file_size <= 1000000:  # 1KB - 1MB
                score += 0.2
            elif file_size > 1000000:  # > 1MB
                score += 0.1
            
            quality_scores.append(min(1.0, max(0.0, score)))
        
        return sum(quality_scores) / len(quality_scores)
    
    def get_enhancement_config(self, query: str, context: Dict = None) -> Dict[str, Any]:
        """获取查询增强配置"""
        config = {
            'enable_enhancement': self.config.query_enhancement_level != QueryEnhancementLevel.NONE,
            'enable_synonyms': self.config.enable_synonym_expansion,
            'enable_keywords': self.config.enable_keyword_extraction,
            'enhancement_level': self.config.query_enhancement_level.value
        }
        
        # 根据查询类型调整增强策略
        if '?' in query or any(word in query.lower() for word in ['怎么', '如何', '什么']):
            # 问句类型，增强同义词扩展
            config['enable_synonyms'] = True
            config['synonym_weight'] = 0.8
        else:
            # 陈述句类型，重点关键词提取
            config['enable_keywords'] = True
            config['keyword_weight'] = 0.9
        
        return config
    
    def get_result_processing_config(self) -> Dict[str, Any]:
        """获取结果处理配置"""
        return {
            'enable_deduplication': self.config.enable_deduplication,
            'enable_reranking': self.config.enable_reranking,
            'enable_fallback': self.config.enable_fallback,
            'content_length_preference': self.config.content_length_preference,
            'dedup_threshold': 0.85,  # 内容相似度阈值
            'rerank_weights': {
                'relevance_score': 0.6,
                'content_quality': 0.2,
                'file_authority': 0.2
            }
        }


def create_preset_configs() -> Dict[str, SearchConfig]:
    """创建预设配置"""
    configs = {}
    
    # 精确搜索配置
    configs['precise'] = SearchConfig(
        top_k=5,
        score_threshold=0.7,
        search_strategy=SearchStrategy.PRECISE,
        query_enhancement_level=QueryEnhancementLevel.BASIC,
        enable_fallback=False,
        enable_smart_threshold=True
    )
    
    # 广泛搜索配置
    configs['broad'] = SearchConfig(
        top_k=20,
        score_threshold=0.3,
        search_strategy=SearchStrategy.BROAD,
        query_enhancement_level=QueryEnhancementLevel.ADVANCED,
        enable_fallback=True,
        enable_multi_query=True
    )
    
    # 平衡搜索配置
    configs['balanced'] = SearchConfig(
        top_k=10,
        score_threshold=0.45,
        search_strategy=SearchStrategy.BALANCED,
        query_enhancement_level=QueryEnhancementLevel.BASIC,
        enable_fallback=True,
        enable_smart_threshold=True
    )
    
    # 快速搜索配置
    configs['fast'] = SearchConfig(
        top_k=8,
        score_threshold=0.5,
        search_strategy=SearchStrategy.ADAPTIVE,
        query_enhancement_level=QueryEnhancementLevel.NONE,
        enable_deduplication=False,
        enable_reranking=False
    )
    
    # 深度搜索配置
    configs['deep'] = SearchConfig(
        top_k=30,
        score_threshold=0.25,
        search_strategy=SearchStrategy.BROAD,
        query_enhancement_level=QueryEnhancementLevel.AGGRESSIVE,
        enable_multi_query=True,
        enable_context_awareness=True,
        enable_fallback=True
    )
    
    return configs


# 全局预设配置
PRESET_CONFIGS = create_preset_configs()


def get_config(preset_name: str = 'balanced') -> SearchConfig:
    """获取预设配置"""
    return PRESET_CONFIGS.get(preset_name, PRESET_CONFIGS['balanced'])
