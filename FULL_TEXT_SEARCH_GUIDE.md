# 全文检索功能使用指南

## 🎯 功能概述

全文检索功能专门用于解决**需要全面分析文档内容**的场景，如：
- 📚 总结书籍的所有战役和典故
- 👥 提取文档中的所有人物信息  
- 🎭 分析文档的完整主题结构
- 📊 对比多个文档的内容差异

## 🔍 与普通检索的区别

| 功能 | 普通检索 | 全文检索 |
|------|----------|----------|
| **结果数量** | topK个片段(如10个) | 数百个相关片段 |
| **覆盖范围** | 最相关的部分 | 全面覆盖主题 |
| **适用场景** | 快速查找答案 | 深度分析总结 |
| **处理方式** | 直接返回结果 | 聚类分析+结构化 |

## 📡 API接口

### 端点
```
POST /datasheet/fulltextsearch/
```

### 请求参数
```json
{
    "text": "经典战役和典故",
    "selected_files": ["三国演义.txt"],
    "database": ["default"],
    "collection": ["chzngk228"],
    
    // 全文检索专用参数
    "analysis_type": "summary",      // 分析类型: summary/extract/classify
    "batch_size": 50,               // 每批检索数量
    "min_score": 0.2,               // 最低相关度分数
    "max_results": 500,             // 最大结果数量
    "enable_clustering": true       // 启用聚类分析
}
```

### 响应格式
```json
{
    "code": 200,
    "data": {
        "query": "经典战役和典故",
        "analysis_type": "summary",
        "total_segments": 156,
        
        "processed_results": {
            "total_segments": 156,
            "avg_score": 0.45,
            "score_distribution": {
                "high_quality_count": 23,
                "medium_quality_count": 89,
                "low_quality_count": 44
            },
            "content_analysis": {
                "main_themes": [
                    {"theme": "官渡之战", "frequency": 8, "type": "战役"},
                    {"theme": "赤壁之战", "frequency": 12, "type": "战役"}
                ]
            },
            "clusters": [
                {
                    "theme": "官渡之战",
                    "count": 8,
                    "avg_score": 0.72,
                    "segments": [...]
                }
            ]
        },
        
        "structured_output": {
            "title": "《三国演义》经典战役和典故相关内容总结",
            "overview": {
                "total_segments": 156,
                "main_themes_count": 15,
                "clusters_count": 8
            },
            "main_battles_and_stories": [
                {
                    "name": "赤壁之战",
                    "segment_count": 12,
                    "relevance_score": 0.78,
                    "content_preview": "赤壁之战是三国时期..."
                }
            ]
        }
    }
}
```

## 🎯 使用场景示例

### 场景1: 总结《三国演义》的经典战役

```python
import requests

# 全面分析三国演义的战役和典故
response = requests.post('http://localhost:8000/datasheet/fulltextsearch/', json={
    'text': '经典战役 典故 计策',
    'selected_files': ['三国演义.txt'],
    'analysis_type': 'summary',
    'batch_size': 50,
    'min_score': 0.2,
    'max_results': 300,
    'enable_clustering': True
})

result = response.json()
data = result['data']

# 获取结构化总结
structured = data['structured_output']
print(f"标题: {structured['title']}")

# 主要战役列表
battles = structured['main_battles_and_stories']
for battle in battles:
    print(f"战役: {battle['name']}")
    print(f"相关片段: {battle['segment_count']}个")
    print(f"相关度: {battle['relevance_score']:.3f}")
    print(f"内容预览: {battle['content_preview']}")
    print("-" * 40)
```

### 场景2: 提取文档中的所有人物

```python
# 提取三国演义中的主要人物
response = requests.post('http://localhost:8000/datasheet/fulltextsearch/', json={
    'text': '主要人物 英雄 将军',
    'selected_files': ['三国演义.txt'],
    'analysis_type': 'extract',
    'batch_size': 40,
    'min_score': 0.25,
    'max_results': 200
})

result = response.json()
processed = result['data']['processed_results']

# 获取人物主题
themes = processed['content_analysis']['main_themes']
characters = [theme for theme in themes if theme['type'] == '人物']

print("主要人物:")
for char in characters:
    print(f"- {char['theme']}: 出现{char['frequency']}次")
```

### 场景3: 对比分析多个文档

```python
# 对比三国演义和西游记的主题
response = requests.post('http://localhost:8000/datasheet/fulltextsearch/', json={
    'text': '英雄 冒险 智慧 策略',
    'selected_files': ['三国演义.txt', '西游记.txt'],
    'analysis_type': 'classify',
    'batch_size': 60,
    'min_score': 0.15,
    'max_results': 500
})

result = response.json()
clusters = result['data']['processed_results']['clusters']

print("主题对比:")
for cluster in clusters:
    print(f"主题: {cluster['theme']}")
    print(f"片段数: {cluster['count']}")
    print(f"平均相关度: {cluster['avg_score']:.3f}")
```

## ⚙️ 参数调优指南

### 1. batch_size (批次大小)
```python
# 小文档或精确查找
"batch_size": 20

# 中等文档
"batch_size": 50  # 推荐

# 大文档或全面分析
"batch_size": 100
```

### 2. min_score (最低分数)
```python
# 严格筛选，高质量结果
"min_score": 0.4

# 平衡质量和覆盖面
"min_score": 0.2  # 推荐

# 最大覆盖面，包含更多相关内容
"min_score": 0.1
```

### 3. max_results (最大结果数)
```python
# 快速概览
"max_results": 100

# 标准分析
"max_results": 300  # 推荐

# 深度分析
"max_results": 500
```

### 4. analysis_type (分析类型)
```python
# 总结分析 - 适用于战役、事件等
"analysis_type": "summary"

# 提取分析 - 适用于人物、地点等
"analysis_type": "extract"  

# 分类分析 - 适用于主题对比
"analysis_type": "classify"
```

## 🚀 高级用法

### 1. 自定义主题分析
```python
# 分析军事策略
payload = {
    'text': '军事策略 兵法 计谋 战术 布阵',
    'selected_files': ['三国演义.txt'],
    'analysis_type': 'summary',
    'min_score': 0.3,  # 提高质量要求
    'enable_clustering': True
}
```

### 2. 多轮检索策略
```python
# 第一轮：高质量结果
round1 = {
    'min_score': 0.5,
    'max_results': 100
}

# 第二轮：扩大范围
round2 = {
    'min_score': 0.2,
    'max_results': 300
}
```

### 3. 结果后处理
```python
def process_battle_results(structured_output):
    """处理战役分析结果"""
    battles = structured_output['main_battles_and_stories']
    
    # 按相关度排序
    sorted_battles = sorted(battles, 
                           key=lambda x: x['relevance_score'], 
                           reverse=True)
    
    # 分类战役
    major_battles = [b for b in sorted_battles if b['segment_count'] >= 5]
    minor_battles = [b for b in sorted_battles if b['segment_count'] < 5]
    
    return {
        'major_battles': major_battles,
        'minor_battles': minor_battles,
        'total_count': len(sorted_battles)
    }
```

## 📊 性能优化建议

### 1. 分批处理大文档
```python
# 对于非常大的文档，分批处理
def analyze_large_document(file_name, themes):
    results = []
    for theme in themes:
        result = full_text_search(
            text=theme,
            selected_files=[file_name],
            batch_size=30,  # 较小的批次
            max_results=200
        )
        results.append(result)
    return merge_results(results)
```

### 2. 缓存策略
```python
# 缓存常用分析结果
import hashlib

def get_cache_key(query, files, params):
    content = f"{query}_{files}_{params}"
    return hashlib.md5(content.encode()).hexdigest()

def cached_full_text_search(query, files, **params):
    cache_key = get_cache_key(query, files, params)
    
    # 检查缓存
    if cache_key in cache:
        return cache[cache_key]
    
    # 执行搜索
    result = full_text_search(query, files, **params)
    
    # 存储缓存
    cache[cache_key] = result
    return result
```

## 🎉 总结

全文检索功能通过以下方式解决了普通检索的局限性：

1. **🔄 多轮检索**: 使用不同策略获取更全面的结果
2. **🧠 智能聚类**: 按主题自动分组相关内容
3. **📊 结构化输出**: 提供易于理解的分析结果
4. **⚡ 批量处理**: 高效处理大量文档片段
5. **🎯 主题分析**: 深度挖掘文档的主题结构

这使得你可以轻松回答如下问题：
- "《三国演义》中有哪些经典战役？"
- "这本技术文档涵盖了哪些主要概念？"
- "这些法律条文涉及哪些具体情况？"

通过全文检索，你可以获得比普通topK检索**10-50倍**的信息覆盖面！
