import asyncio
import re
import time
import json
from typing import Op<PERSON>, <PERSON>, <PERSON><PERSON>
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>rowserContext, Page
from bs4 import BeautifulSoup
from .webtype import XiaohongshuContent

class XiaohongshuExtractor:
    """小红书内容提取器 - 基于 Playwright"""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.playwright = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._init_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def close(self):
        """关闭浏览器"""
        try:
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
        except Exception as e:
            print(f"关闭浏览器时出错: {e}")
        finally:
            self.browser = None
            self.context = None
            self.playwright = None
    
    async def _init_browser(self):
        """初始化浏览器"""
        if not self.browser:
            self.playwright = await async_playwright().start()
            
            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-infobars',
                    '--disable-extensions',
                    '--no-default-browser-check'
                ]
            )
            
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Referer': 'https://www.xiaohongshu.com/',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Upgrade-Insecure-Requests': '1'
                }
            )
            
            # 添加初始化脚本来隐藏webdriver特征
            await self.context.add_init_script('''
                // 隐藏webdriver特征
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // 模拟真实的chrome对象
                window.chrome = {
                    runtime: {},
                    loadTimes: function() {},
                    csi: function() {},
                    app: {}
                };
                
                // 模拟插件
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                // 模拟语言
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en'],
                });
            ''')
    
    def is_xiaohongshu_url(self, url: str) -> bool:
        """判断是否为小红书链接"""
        return any(domain in url for domain in ['xiaohongshu.com', 'xhslink.com'])
    
    async def _simulate_guest_session(self, page: Page):
        """模拟游客会话，增加获取第二种格式的概率"""
        try:
            # 设置一些常见的游客状态cookie
            await page.context.add_cookies([
                {
                    'name': 'guest_id',
                    'value': f'guest_{int(time.time() * 1000)}',
                    'domain': '.xiaohongshu.com',
                    'path': '/'
                },
                {
                    'name': 'xhsTrackerId',
                    'value': f'track_{int(time.time() * 1000)}',
                    'domain': '.xiaohongshu.com',
                    'path': '/'
                },
                {
                    'name': 'web_session',
                    'value': f'session_{int(time.time() * 1000)}',
                    'domain': '.xiaohongshu.com',
                    'path': '/'
                }
            ])
            
            # 执行一些JavaScript来模拟游客行为（避免localStorage）
            await page.evaluate('''
                () => {
                    try {
                        // 模拟一些用户行为
                        window.dispatchEvent(new Event('resize'));
                        document.dispatchEvent(new Event('DOMContentLoaded'));
                        
                        // 模拟滚动行为
                        window.scrollTo(0, 100);
                        
                        // 设置一些window属性来模拟游客状态
                        if (window) {
                            window.isGuest = true;
                            window.guestId = 'guest_' + Date.now();
                        }
                        
                    } catch (e) {
                        console.log('模拟游客行为时出错:', e);
                    }
                }
            ''')
            
        except Exception as e:
            print(f"模拟游客会话时出错: {e}")
    
    async def get_content_info(self, url: str) -> Optional[XiaohongshuContent]:
        """获取小红书内容信息"""
        try:
            # 确保浏览器已初始化
            if not self.browser:
                await self._init_browser()
            
            page = await self.context.new_page()
            
            try:
                # 处理短链接
                if 'xhslink.com' in url:
                    url = await self._resolve_short_url(page, url)
                
                # 模拟游客会话
                await self._simulate_guest_session(page)
                
                # 首先访问主页，模拟正常用户行为
                try:
                    await page.goto('https://www.xiaohongshu.com/', wait_until='domcontentloaded', timeout=10000)
                    await page.wait_for_timeout(1000)
                except:
                    print("访问主页失败，直接访问目标页面")
                
                # 访问目标页面
                await page.goto(url, wait_until='networkidle', timeout=30000)
                
                # 随机等待时间，模拟真实用户行为
                import random
                wait_time = random.uniform(2000, 5000)
                await page.wait_for_timeout(int(wait_time))
                
                # 尝试等待特定元素加载
                try:
                    await page.wait_for_selector('meta[property="og:title"]', timeout=10000)
                    await page.wait_for_selector('meta[name="og:type"]', timeout=10000)
                    print("检测到og:type标签已加载")
                except:
                    print("等待元素超时，继续执行...")
                
                # 尝试多次获取数据，增加成功率
                js_data = await self._get_page_data_with_retry(page)
                
                # 获取页面HTML
                html_content = await page.content()
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # 验证是否获取到了完整内容
                og_type_tag = soup.find('meta', {'name': 'og:type'})
                if og_type_tag:
                    print(f"✓ 成功获取到og:type: {og_type_tag.get('content')}")
                else:
                    print("⚠ 未获取到og:type标签")
                
                # 将JS数据转换为字符串以便正则匹配
                js_data_str = json.dumps(js_data, ensure_ascii=False)
                html_text = html_content + js_data_str
                
                # 提取基本信息
                title = self._extract_title(soup, html_text)
                content = self._extract_content(soup, html_text)
                author = self._extract_author(soup, html_text)
                publish_time = self._extract_publish_time(soup, html_text)
                # 提取统计数据
                like_count = self._extract_like_count(soup, html_text)
                comment_count = self._extract_comment_count(soup, html_text)
                collect_count = self._extract_collect_count(soup, html_text)
                
                # 判断内容类型并提取相应信息
                content_type = self._detect_content_type(soup, html_text)
                
                images = []
                video_url = ""
                duration = 0
                
                if content_type == 'video':
                    video_url, duration = self._extract_video_info(soup, html_text)
                    # 如果检测为视频但没有视频URL，也提取图片作为缩略图
                    if not video_url:
                        images = self._extract_images(soup, html_text)
                else:
                    images = self._extract_images(soup, html_text)
                
                # 提取标签
                tags = self._extract_tags(soup, html_text)
                
                return XiaohongshuContent(
                    title=title,
                    content=content,
                    author=author,
                    publish_time=publish_time,
                    like_count=like_count,
                    comment_count=comment_count,
                    collect_count=collect_count,
                    content_type=content_type,
                    url=url,
                    images=images,
                    video_url=video_url,
                    duration=duration,
                    tags=tags
                )
                
            finally:
                await page.close()
                
        except Exception as e:
            print(f"获取小红书内容时出错: {e}")
            import traceback
            traceback.print_exc()
            return None
        finally:
            await self.close()
    
    async def _get_page_data_with_retry(self, page: Page, max_retries: int = 3):
        """带重试机制的数据获取"""
        for attempt in range(max_retries):
            try:
                # 执行JavaScript获取动态数据
                js_data = await page.evaluate('''
                    () => {
                        // 尝试获取页面中的各种数据源
                        const data = {};
                        
                        // 获取window对象中的数据
                        if (window.__INITIAL_STATE__) {
                            data.initialState = window.__INITIAL_STATE__;
                        }
                        if (window.__INITIAL_SSR_STATE__) {
                            data.ssrState = window.__INITIAL_SSR_STATE__;
                        }
                        if (window.__APP_DATA__) {
                            data.appData = window.__APP_DATA__;
                        }
                        
                        // 获取所有script标签中的JSON数据
                        const scripts = document.querySelectorAll('script');
                        data.scriptData = [];
                        scripts.forEach(script => {
                            const text = script.textContent;
                            if (text && (text.includes('noteId') || text.includes('author') || text.includes('title'))) {
                                try {
                                    // 尝试解析JSON
                                    const jsonMatch = text.match(/({.*})/);
                                    if (jsonMatch) {
                                        const parsed = JSON.parse(jsonMatch[1]);
                                        data.scriptData.push(parsed);
                                    }
                                } catch (e) {
                                    // 如果不是JSON，保存原始文本
                                    data.scriptData.push(text.slice(0, 1000));
                                }
                            }
                        });
                        
                        // 同时获取当前的meta标签信息
                        const metaTags = [];
                        document.querySelectorAll('meta').forEach(meta => {
                            const attrs = {};
                            for (let attr of meta.attributes) {
                                attrs[attr.name] = attr.value;
                            }
                            metaTags.push(attrs);
                        });
                        data.metaTags = metaTags;
                        
                        // 检查是否获取到了有效的用户信息
                        const hasValidUserInfo = data.initialState?.user?.userInfo?.userId || 
                                               data.ssrState?.user?.userInfo?.userId ||
                                               data.appData?.user?.userInfo?.userId;
                        
                        data.hasValidUserInfo = !!hasValidUserInfo;
                        
                        return data;
                    }
                ''')
                
                # 检查是否获取到了期望的数据格式
                js_data_str = json.dumps(js_data, ensure_ascii=False)
                if '"userInfo"' in js_data_str and '"userId"' in js_data_str:
                    print(f"✓ 成功获取到userInfo格式的数据 (尝试 {attempt + 1}/{max_retries})")
                    return js_data
                elif '"loggedIn"' in js_data_str and '"_value": false' in js_data_str:
                    print(f"⚠ 获取到loggedIn格式，尝试刷新 (尝试 {attempt + 1}/{max_retries})")
                    if attempt < max_retries - 1:
                        await page.reload(wait_until='networkidle')
                        await page.wait_for_timeout(2000)
                        continue
                
                return js_data
                
            except Exception as e:
                print(f"获取数据时出错 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    await page.wait_for_timeout(1000)
                    continue
                raise e
        
        return js_data
    
    async def _resolve_short_url(self, page: Page, short_url: str) -> str:
        """解析短链接"""
        try:
            await page.goto(short_url, wait_until='networkidle', timeout=10000)
            await page.wait_for_timeout(2000)
            final_url = page.url
            print(f"短链接解析: {short_url} -> {final_url}")
            return final_url
        except Exception as e:
            print(f"解析短链接失败: {e}")
            return short_url
    
    def _detect_content_type(self, soup: BeautifulSoup, html_text: str) -> str:
        """检测内容类型 - 优化版本"""
        
        # 方法1: 检查 og:type meta 标签
        og_type_tag = soup.find('meta', attrs={'name': 'og:type'})
        if og_type_tag and og_type_tag.get('content'):
            og_type = og_type_tag['content'].lower()
            print(f"找到 og:type: {og_type}")
            if 'video' in og_type:
                return 'video'
            elif 'article' in og_type:
                return 'article'
        else:
            print("未找到 og:type meta 标签")
        return 'note'
    
    def _extract_title(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取标题"""
        # 优先从og:title中提取
        og_title = soup.find('meta', attrs={'name': 'og:title'})
        if og_title and og_title.get('content'):
            title = og_title['content']
            # 移除 " - 小红书" 后缀
            title = re.sub(r'\s*-\s*小红书\s*$', '', title)
            if title.strip():
                return title.strip()
        
        return ""
    
    def _extract_content(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取内容"""
        # 从meta description中提取
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc and meta_desc.get('content'):
            content = meta_desc['content']
            if content and content != "3 亿人的生活经验，都在小红书":
                return content.strip()
        
        return ""
    
    def _extract_author(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取作者"""
        # 从JSON数据中提取
        author_patterns = [
            r'"nickname":\s*"([^"]*)"',
            r'"username":\s*"([^"]*)"',
            r'"author":\s*"([^"]*)"',
            r'"userInfo":\s*{[^}]*"nickname":\s*"([^"]*)"',
            r'"user":\s*{[^}]*"nickname":\s*"([^"]*)"'
        ]
        
        for pattern in author_patterns:
            match = re.search(pattern, html_text)
            if match and match.group(1).strip():
                return match.group(1).strip()
        
        return ""
    
    def _extract_publish_time(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取发布时间"""
        time_patterns = [
            r'"time":\s*"([^"]*)"',
            r'"publishTime":\s*"([^"]*)"',
            r'"createTime":\s*(\d+)',
            r'"updateTime":\s*(\d+)',
            r'"time":\s*(\d+)',
            r'"lastUpdateTime":\s*(\d+)'
        ]
        
        for pattern in time_patterns:
            match = re.search(pattern, html_text)
            if match:
                time_str = match.group(1)
                # 如果是时间戳（纯数字）
                if time_str.isdigit():
                    timestamp = int(time_str)
                    # 如果是毫秒时间戳，转换为秒
                    if timestamp > 9999999999:
                        timestamp = timestamp // 1000
                    try:
                        return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
                    except:
                        continue
                elif time_str:
                    return time_str
        
        return ""
    
    def _extract_like_count(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取点赞数"""
        # 从meta标签中提取
        like_meta = soup.find('meta', attrs={'name':'og:xhs:note_like'})
        
        if like_meta and like_meta.get('content'):
            return like_meta['content']
        return ""
    def _extract_comment_count(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取评论数"""
        # 从meta标签中提取
        comment_meta = soup.find('meta', attrs={'name':'og:xhs:note_comment'})
        if comment_meta and comment_meta.get('content'):
            return comment_meta['content']
        return ""
            
    def _extract_collect_count(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取收藏数"""
        # 从meta标签中提取
        collect_meta = soup.find('meta',  attrs={'name': 'og:xhs:note_collect'})
        if collect_meta and collect_meta.get('content'):
            return collect_meta['content']
        
        return ""
    def _extract_share_count(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取分享数"""
        # 从JSON数据中提取分享数
        share_patterns = [
            r'"shareCount":\s*"([^"]*)"',
            r'"interactInfo":\s*{[^}]*"shareCount":\s*"([^"]*)"'
        ]
        
        for pattern in share_patterns:
            match = re.search(pattern, html_text)
            if match and match.group(1).strip():
                return match.group(1).strip()
        
        return ""    
    def _extract_images(self, soup: BeautifulSoup, html_text: str) -> List[str]:
        """提取图片列表"""
        images = []
        
        # 从og:image meta标签中提取
        og_images = soup.find_all('meta',  attrs={'name': 'og:image'})
        for og_img in og_images:
            if og_img.get('content'):
                img_url = og_img['content']
                images.append(img_url)
        
        return images
    
    def _extract_video_info(self, soup: BeautifulSoup, html_text: str) -> Tuple[str, int]:
        """提取视频信息"""
        video_url = ""
        duration = 0
        
        # 从og:video meta标签中提取
        og_video = soup.find('meta', attrs={'name':'og:videotime'})
        if og_video and og_video.get('content'):
            duration = og_video['content']
        
        return video_url, duration
    
    def _extract_tags(self, soup: BeautifulSoup, html_text: str) -> List[str]:
        """提取标签"""
        tags = []
        
        # 从meta keywords中提取
        keywords_meta = soup.find('meta', attrs={'name': 'keywords'})
        if keywords_meta and keywords_meta.get('content'):
            keywords = keywords_meta['content']
            # 按逗号分割关键词
            keyword_list = [kw.strip() for kw in keywords.split(',') if kw.strip()]
            tags.extend(keyword_list)
        
        # 去重并过滤空标签
        unique_tags = []
        for tag in tags:
            if tag and tag.strip() and tag.strip() not in unique_tags:
                unique_tags.append(tag.strip())
        
        return unique_tags