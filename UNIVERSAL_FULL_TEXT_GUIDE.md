# 通用全文检索功能指南

## 🎯 解决的核心问题

**问题**: 之前的实现硬编码了"三国演义"相关的战役关键词，缺乏通用性

**解决方案**: 完全通用的文档分析系统，不依赖任何领域特定的硬编码规则

## ✨ 新版本特性

### 🔧 完全通用化
- ❌ 不再硬编码"官渡之战"、"赤壁之战"等特定关键词
- ✅ 自动识别任何文档中的关键主题和实体
- ✅ 适用于小说、技术文档、法律条文、学术论文等任何类型

### 🧠 智能分析
- ✅ 通用关键词提取（TF-IDF + TextRank）
- ✅ 实体识别（人物、地点、时间、事件、概念）
- ✅ 智能内容聚类
- ✅ 自适应主题分析

### 🎛️ 灵活配置
- ✅ 可调节的分析深度
- ✅ 多种检索策略
- ✅ 自定义分析类型
- ✅ 智能回退机制

## 📡 API接口

### 请求格式
```json
{
    "text": "你想分析的主题",
    "selected_files": ["任何文件.txt"],
    "analysis_type": "general",
    "batch_size": 50,
    "min_score": 0.2,
    "max_results": 300,
    "enable_deep_analysis": true
}
```

### 响应格式
```json
{
    "code": 200,
    "data": {
        "query": "你的查询",
        "total_segments": 156,
        "analysis_method": "universal_analyzer",
        
        "basic_statistics": {
            "total_documents": 156,
            "average_score": 0.45,
            "total_characters": 50000
        },
        
        "key_themes": [
            {"word": "战争", "weight": 0.85, "category": "military", "frequency": 12},
            {"word": "策略", "weight": 0.72, "category": "concept", "frequency": 8}
        ],
        
        "entities": {
            "人物": [
                {"text": "主角姓名", "frequency": 15, "confidence": 0.8}
            ],
            "地点": [
                {"text": "重要地点", "frequency": 8, "confidence": 0.9}
            ],
            "事件": [
                {"text": "重要事件", "frequency": 6, "confidence": 0.7}
            ]
        },
        
        "content_clusters": [
            {
                "name": "主要主题1",
                "document_count": 25,
                "avg_score": 0.68,
                "preview": "相关内容预览..."
            }
        ],
        
        "structured_summary": {
            "title": "关于「你的查询」的内容分析",
            "key_findings": [
                "最重要的主题是「战争」，出现12次",
                "主要人物：主角姓名（出现15次）"
            ],
            "recommendations": [
                "建议针对特定主题进行深入分析"
            ]
        }
    }
}
```

## 🎯 使用场景示例

### 1. 分析任何小说的主题

```python
# 三国演义 - 战役分析
response = requests.post('/datasheet/fulltextsearch/', json={
    'text': '战争 战役 军事 策略',
    'selected_files': ['三国演义.txt'],
    'analysis_type': 'general'
})

# 西游记 - 冒险分析  
response = requests.post('/datasheet/fulltextsearch/', json={
    'text': '冒险 妖怪 法术 神通',
    'selected_files': ['西游记.txt'],
    'analysis_type': 'general'
})

# 现代小说 - 情感分析
response = requests.post('/datasheet/fulltextsearch/', json={
    'text': '爱情 友情 亲情 情感',
    'selected_files': ['现代小说.txt'],
    'analysis_type': 'general'
})
```

### 2. 分析技术文档

```python
# API文档分析
response = requests.post('/datasheet/fulltextsearch/', json={
    'text': 'API 接口 参数 返回值 错误码',
    'selected_files': ['API文档.pdf'],
    'analysis_type': 'technical'
})

# 系统架构分析
response = requests.post('/datasheet/fulltextsearch/', json={
    'text': '架构 模块 组件 服务 数据库',
    'selected_files': ['系统设计.docx'],
    'analysis_type': 'technical'
})
```

### 3. 分析法律文档

```python
# 交通法规分析
response = requests.post('/datasheet/fulltextsearch/', json={
    'text': '违法 处罚 罚款 扣分 责任',
    'selected_files': ['道路交通.txt'],
    'analysis_type': 'legal'
})

# 合同条款分析
response = requests.post('/datasheet/fulltextsearch/', json={
    'text': '条款 义务 权利 责任 违约',
    'selected_files': ['合同模板.docx'],
    'analysis_type': 'legal'
})
```

### 4. 对比分析

```python
# 对比不同文档的主题
response = requests.post('/datasheet/fulltextsearch/', json={
    'text': '主要概念 核心思想',
    'selected_files': ['文档A.txt', '文档B.txt'],
    'analysis_type': 'comparative',
    'max_results': 400
})
```

## 🔧 参数调优指南

### analysis_type 分析类型
```python
'general'      # 通用分析（默认）
'technical'    # 技术文档分析
'legal'        # 法律文档分析  
'comparative'  # 对比分析
'academic'     # 学术文档分析
```

### 质量控制参数
```python
# 高质量结果
{
    'min_score': 0.4,
    'max_results': 150,
    'enable_deep_analysis': True
}

# 全面覆盖
{
    'min_score': 0.15,
    'max_results': 500,
    'enable_deep_analysis': True
}

# 快速预览
{
    'min_score': 0.3,
    'max_results': 50,
    'enable_deep_analysis': False
}
```

## 🧠 智能分析原理

### 1. 通用关键词提取
```python
# 不再硬编码关键词，而是动态提取
keywords = extract_keywords_with_tfidf_and_textrank(content)
# 自动分类：military, person, place, concept, time, general
```

### 2. 实体识别
```python
# 通用实体模式，适用于任何文档
entity_patterns = {
    'person': [r'[\u4e00-\u9fa5]{2,3}(?:先生|女士|老师|教授)'],
    'place': [r'[\u4e00-\u9fa5]{2,6}(?:市|省|县|区|镇)'],
    'event': [r'[\u4e00-\u9fa5]{2,8}(?:战争|战役|会议|活动)'],
    # ... 更多通用模式
}
```

### 3. 智能聚类
```python
# 基于关键词相似度的动态聚类
# 不预设主题，让数据自己说话
clusters = cluster_by_keyword_similarity(documents, keywords)
```

## 📊 效果对比

| 方面 | 旧版本（硬编码） | 新版本（通用） |
|------|----------------|---------------|
| **适用范围** | 仅三国演义 | 任何文档 |
| **关键词** | 硬编码战役名 | 自动提取 |
| **实体识别** | 固定人物名 | 通用模式 |
| **主题分析** | 预设类别 | 动态发现 |
| **扩展性** | 需要修改代码 | 无需修改 |

## 🎉 使用建议

### 1. 根据文档类型选择分析类型
```python
# 小说、故事类
analysis_type = 'general'

# 技术文档、API文档
analysis_type = 'technical'

# 法律条文、政策文件
analysis_type = 'legal'

# 多文档对比
analysis_type = 'comparative'
```

### 2. 根据需求调整参数
```python
# 需要全面分析
max_results = 500
min_score = 0.15

# 需要精确结果
max_results = 100  
min_score = 0.4

# 快速预览
max_results = 50
enable_deep_analysis = False
```

### 3. 利用结构化输出
```python
result = response.json()['data']

# 获取关键发现
key_findings = result['structured_summary']['key_findings']

# 获取主要主题
main_themes = [theme['word'] for theme in result['key_themes'][:5]]

# 获取实体信息
entities = result['entities']
```

## 🎯 总结

新的通用全文检索功能彻底解决了硬编码问题：

✅ **完全通用** - 适用于任何类型的文档  
✅ **智能分析** - 自动识别主题和实体  
✅ **灵活配置** - 丰富的参数选项  
✅ **结构化输出** - 易于理解和使用  
✅ **无需维护** - 不需要更新领域知识  

现在你可以用同一个接口分析《三国演义》的战役、《西游记》的冒险、技术文档的API、法律条文的规定，或者任何其他类型的文档！
