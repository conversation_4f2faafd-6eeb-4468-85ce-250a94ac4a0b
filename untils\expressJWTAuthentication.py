from loguru import logger
from rest_framework_simplejwt.authentication import J<PERSON><PERSON><PERSON>entication
from rest_framework_simplejwt.exceptions import InvalidToken, AuthenticationFailed

class ExpressJWTAuthentication(JWTAuthentication):
    def get_user(self, validated_token):
        """
        根据Express.js生成的token获取用户
        """
        logger.info('get_user')
        try:
            user_id = validated_token['id']
        except KeyError:
            raise InvalidToken('Token不包含有效的用户标识')
        
        # 从数据库获取用户
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        try:
            user = User.objects.get(user_id=user_id)
        except User.DoesNotExist:
            raise AuthenticationFailed('此token对应的用户不存在')
        
        return user