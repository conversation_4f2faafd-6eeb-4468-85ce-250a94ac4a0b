from rest_framework_simplejwt.tokens import RefreshToken

class CustomRefreshToken(RefreshToken):

    @classmethod
    def for_user(cls, user):
        # 调用父类方法生成token
        token = super().for_user(user)

        # 添加额外的信息到access token的payload
        # 注意：这里是直接添加到access token，对于refresh token需要另外处理
        access_token = token.access_token
        # access_token['user_id'] = user.id
        # access_token['id'] = "65bb348e770b9b3d3Cc66093"
        access_token['id'] = str(user.user_id)
        access_token['username'] = user.username
        access_token['email'] = user.username
        access_token['provider'] = user.provider
        # print('ffffffff',access_token)
        return access_token
