# Generated by Django 5.1.7 on 2025-06-20 03:05

import datasheet.models
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('datasheet', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NoteBook',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('title', models.CharField(max_length=64, verbose_name='笔记本名称')),
                ('isShared', models.BooleanField(default=False, null=True, verbose_name='是否共享')),
                ('date', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('uuid_user', models.ForeignKey(default='', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notebook_user', to=settings.AUTH_USER_MODEL, to_field='uuid_user')),
            ],
            options={
                'db_table': 'ai_notebook',
            },
        ),
        migrations.CreateModel(
            name='NoteBookFrame',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('1', 'link'), ('2', 'file'), ('3', 'text')], default='2', max_length=16, null=True, verbose_name='来源类型')),
                ('name', models.CharField(max_length=1024, null=True, verbose_name='文件名')),
                ('url', models.CharField(max_length=1024, null=True, verbose_name='链接地址')),
                ('publishedDate', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('available', models.CharField(choices=[('1', '可用'), ('2', '未上传'), ('3', '上传中'), ('4', '失败')], default='2', max_length=16, null=True, verbose_name='文件状态')),
                ('sql_available', models.CharField(choices=[('1', '可用'), ('2', '未上传'), ('3', '上传中'), ('4', '失败')], default='4', max_length=16, null=True, verbose_name='切片状态')),
                ('file_size', models.PositiveBigIntegerField(null=True, verbose_name='文件大小')),
                ('cache_id', models.CharField(default='', max_length=1024, null=True, verbose_name='缓存ID')),
                ('file', models.FileField(max_length=1024, upload_to=datasheet.models.file_dir)),
                ('summary', models.TextField(blank=True, default='', null=True, verbose_name='摘要')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('uid_sheet', models.ForeignKey(default='', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notebook_frame', to='datasheet.notebook')),
                ('uuid_user', models.ForeignKey(default='', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notebook_frame_user', to=settings.AUTH_USER_MODEL, to_field='uuid_user')),
            ],
            options={
                'db_table': 'ai_notebook_frame',
            },
        ),
        migrations.CreateModel(
            name='NoteBookFrameDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('folder_id', models.CharField(max_length=1024, null=True, verbose_name='文本ID')),
                ('page_content', models.TextField(blank=True, default='', null=True, verbose_name='文本内容')),
                ('file_name', models.CharField(max_length=1024, null=True, verbose_name='文件名称')),
                ('file_path', models.CharField(max_length=1024, null=True, verbose_name='文件名称')),
                ('keyTopics', models.CharField(blank=True, default='法律', max_length=1024, null=True, verbose_name='知识库类型')),
                ('uid_frame', models.ForeignKey(default='', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='frame_id', to='datasheet.notebookframe')),
            ],
            options={
                'db_table': 'ai_notebook_detail',
            },
        ),
    ]
