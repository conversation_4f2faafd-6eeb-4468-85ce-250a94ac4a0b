import os

from loguru import logger
from untils.vector.lc_milvus import Mi<PERSON><PERSON><PERSON>
from pymilvus import MilvusException


class MilvusDriver:
     def __init__(
        self, embeddings, database_name="default", collection_name="default"
    ) -> None:
        try:
            self.milvus_host = os.getenv("MILVUS_HOST", "localhost")
            self.milvus_port = os.getenv("MILVUS_PORT", "19955")
            self.milvus_user = os.getenv("MILVUS_USER", "localhost")
            self.milvus_pass = os.getenv("MILVUS_PASSWORD", "19955")

            self.vector_store = Milvus(
                embedding_function=embeddings.emb,
                collection_name=collection_name,
                database_name=database_name,
                connection_args={"host": self.milvus_host, "port": self.milvus_port,"user":self.milvus_user,"password":self.milvus_pass},
            )
            logger.info(f"Milvus connection: {os.environ['MILVUS_HOST']}")
        except MilvusException as e:
            logger.error(f"Milvus connection error: {str(e)}")
            raise ConnectionError(f"Could not connect to Milvus server: {e}")
        except Exception as e:
            logger.error(f"MilvusDriver initialization failed: {e}")
            raise RuntimeError(f"MilvusDriver Initialization failed: {e}")