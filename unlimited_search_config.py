# ===================================================================
# 此文件已被注释 - 用于测试和演示目的
# 如需使用，请取消注释
# ===================================================================

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无限制搜索配置

提供真正无限制的搜索配置，最大化检索效果
"""

import requests
import json
from typing import Dict, List, Any


class UnlimitedSearchConfig:
    """无限制搜索配置器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
    
    def get_unlimited_file_search_config(self) -> Dict[str, Any]:
        """获取无限制的文件检索配置"""
        return {
            'top_k': 100,                    # 大幅提高结果数量
            'score_threshold': 0.1,          # 极低阈值，最大覆盖
            'query_enhancement': True,       # 启用查询增强
            'smart_optimization': True,      # 启用智能优化
            'search_strategy': 'broad',      # 使用广泛搜索策略
            'enable_fallback': True          # 启用回退策略
        }
    
    def get_unlimited_full_text_config(self) -> Dict[str, Any]:
        """获取无限制的全文检索配置"""
        return {
            'batch_size': 200,               # 大批次检索
            'min_score': 0.05,               # 极低分数阈值
            'max_results': 5000,             # 极大结果数量
            'enable_deep_analysis': True,    # 启用深度分析
            'analysis_type': 'general'       # 通用分析类型
        }
    
    def get_maximum_entity_config(self) -> Dict[str, Any]:
        """获取最大实体识别配置"""
        return {
            'max_entities_per_type': 100,    # 每类最多100个实体
            'max_keywords': 100,             # 最多100个关键词
            'max_clusters': 50,              # 最多50个聚类
            'max_findings': 50               # 最多50个关键发现
        }
    
    def test_unlimited_file_search(self, query: str, files: List[str]) -> Dict[str, Any]:
        """测试无限制文件检索"""
        print(f"🚀 无限制文件检索测试")
        print(f"查询: {query}")
        print(f"文件: {files}")
        
        config = self.get_unlimited_file_search_config()
        
        payload = {
            'text': query,
            'selected_files': files,
            'database': ['default'],
            'collection': ['chzngk228'],
            **config
        }
        
        try:
            response = requests.post(f"{self.base_url}/datasheet/filesearch/", 
                                   json=payload, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                data = result.get('data', {})
                results = data.get('results', [])
                
                print(f"✅ 成功获得 {len(results)} 个结果")
                
                if results:
                    scores = [r.get('metadata', {}).get('score', 0) for r in results]
                    avg_score = sum(scores) / len(scores)
                    print(f"📊 平均分数: {avg_score:.3f}")
                    print(f"📈 分数范围: {min(scores):.3f} - {max(scores):.3f}")
                
                return result
            else:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                return {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            return {'error': str(e)}
    
    def test_unlimited_full_text_search(self, query: str, files: List[str]) -> Dict[str, Any]:
        """测试无限制全文检索"""
        print(f"\n📚 无限制全文检索测试")
        print(f"查询: {query}")
        print(f"文件: {files}")
        
        config = self.get_unlimited_full_text_config()
        
        payload = {
            'text': query,
            'selected_files': files,
            'database': ['default'],
            'collection': ['chzngk228'],
            **config
        }
        
        try:
            response = requests.post(f"{self.base_url}/datasheet/fulltextsearch/", 
                                   json=payload, timeout=120)
            
            if response.status_code == 200:
                result = response.json()
                data = result.get('data', {})
                
                print(f"✅ 成功获得 {data.get('total_segments', 0)} 个片段")
                print(f"🔬 分析方法: {data.get('analysis_method', 'unknown')}")
                
                # 检查实体识别结果
                if 'entities' in data:
                    entities = data['entities']
                    total_entities = sum(len(entity_list) for entity_list in entities.values())
                    print(f"🏷️  识别实体: {total_entities} 个")
                    
                    for entity_type, entity_list in entities.items():
                        if entity_list:
                            print(f"   - {entity_type}: {len(entity_list)} 个")
                
                # 检查关键主题
                if 'key_themes' in data:
                    themes = data['key_themes']
                    print(f"🔑 关键主题: {len(themes)} 个")
                
                # 检查内容聚类
                if 'content_clusters' in data:
                    clusters = data['content_clusters']
                    print(f"🎭 内容聚类: {len(clusters)} 个")
                
                # 检查结构化总结
                if 'structured_summary' in data:
                    summary = data['structured_summary']
                    key_findings = summary.get('key_findings', [])
                    print(f"📋 关键发现: {len(key_findings)} 个")
                
                return result
            else:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                return {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            return {'error': str(e)}
    
    def compare_limited_vs_unlimited(self, query: str, files: List[str]):
        """对比限制版本 vs 无限制版本"""
        print(f"\n🆚 限制版本 vs 无限制版本对比")
        print("=" * 60)
        
        # 测试限制版本（原默认配置）
        print("\n1️⃣ 限制版本测试:")
        limited_config = {
            'text': query,
            'selected_files': files,
            'top_k': 10,                     # 原来的限制
            'score_threshold': 0.45,         # 原来的限制
            'query_enhancement': False       # 原来的限制
        }
        
        try:
            response = requests.post(f"{self.base_url}/datasheet/filesearch/", 
                                   json=limited_config, timeout=30)
            if response.status_code == 200:
                limited_result = response.json()
                limited_count = len(limited_result.get('data', {}).get('results', []))
                print(f"   结果数量: {limited_count}")
            else:
                limited_count = 0
                print(f"   ❌ 失败: HTTP {response.status_code}")
        except Exception as e:
            limited_count = 0
            print(f"   ❌ 异常: {str(e)}")
        
        # 测试无限制版本
        print("\n2️⃣ 无限制版本测试:")
        unlimited_result = self.test_unlimited_file_search(query, files)
        
        if 'error' not in unlimited_result:
            unlimited_count = len(unlimited_result.get('data', {}).get('results', []))
            
            # 效果对比
            print(f"\n📊 效果对比:")
            print(f"   限制版本: {limited_count} 个结果")
            print(f"   无限制版本: {unlimited_count} 个结果")
            
            if unlimited_count > limited_count:
                improvement = ((unlimited_count - limited_count) / max(limited_count, 1)) * 100
                print(f"   🚀 提升: +{improvement:.1f}%")
            else:
                print(f"   ⚠️ 无明显提升")
        
        # 测试全文检索
        print(f"\n3️⃣ 全文检索无限制测试:")
        self.test_unlimited_full_text_search(query, files)


def demo_unlimited_search():
    """演示无限制搜索"""
    config = UnlimitedSearchConfig()
    
    print("🚀 无限制搜索功能演示")
    print("移除所有不必要的限制，最大化检索效果")
    
    # 测试数据
    test_cases = [
        {
            'query': '交通违法处理',
            'files': ['道路交通.txt'],
            'description': '法律文档分析'
        },
        {
            'query': 'API接口调用',
            'files': ['新建 文本文档 (2).txt'],
            'description': '技术文档分析'
        }
    ]
    
    for case in test_cases:
        print(f"\n{'='*60}")
        print(f"📋 测试场景: {case['description']}")
        
        # 对比测试
        config.compare_limited_vs_unlimited(case['query'], case['files'])
    
    print(f"\n✅ 无限制搜索演示完成!")
    print(f"\n💡 配置建议:")
    print(f"   - 文件检索: top_k=100, score_threshold=0.1")
    print(f"   - 全文检索: max_results=5000, min_score=0.05")
    print(f"   - 实体识别: 每类最多100个实体")
    print(f"   - 关键主题: 最多100个关键词")


def get_unlimited_configs():
    """获取所有无限制配置"""
    config = UnlimitedSearchConfig()
    
    return {
        'file_search': config.get_unlimited_file_search_config(),
        'full_text_search': config.get_unlimited_full_text_config(),
        'entity_recognition': config.get_maximum_entity_config()
    }


if __name__ == '__main__':
    demo_unlimited_search()
