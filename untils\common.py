"""
@File    :   common.py
@Time    :   2023/09/19 16:05:53
<AUTHOR>   weny
@Version :   1.0
@Desc    :   工具类
"""


import hashlib
import json
import os

import yaml,re
from loguru import logger

def milvus_naming(nameString,prefix='chzn'):
    return prefix+re.sub(r'[^a-zA-Z0-9_]', '', nameString)

def calculate_md5(file_path):
    md5_hash = hashlib.md5()
    with open(file_path, "rb") as file:
        # 分块读取文件内容，适用于大文件
        for chunk in iter(lambda: file.read(4096), b""):
            md5_hash.update(chunk)
    return md5_hash.hexdigest()


def Singleton(cls):
    _instance = {}

    def inner(*args, **kargs):
        if cls not in _instance:
            _instance[cls] = cls(*args, **kargs)
        return _instance[cls]

    return inner


def Timer(func):
    """函数运行时间计时器"""

    def wrapper(*args, **kwargs):
        import time

        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        logger.info(f"{func.__name__} cost {round(end - start, 5)}s")
        return result

    return wrapper


def get_all_files(folder_path):
    # 获取文件夹下所有的文件，如果是文件夹，递归获取
    files = []
    for file in os.listdir(folder_path):
        file_path = os.path.join(folder_path, file)
        if os.path.isdir(file_path):
            files.extend(get_all_files(file_path))
        else:
            files.append(file_path)
    return files


def read_yaml(file_path):
    with open(file_path, "r", encoding="utf-8") as f:
        return yaml.load(f, Loader=yaml.FullLoader)


def write_yaml(file_path, data):
    with open(file_path, "w", encoding="utf-8") as f:
        yaml.dump(data, f, allow_unicode=True)


def read_json(file_path):
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)


def write_json(file_path, data):
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=4)


def recursive_character_length_function(text):
    # 不包含换行符的长度
    return len(text.replace("\n", ""))
