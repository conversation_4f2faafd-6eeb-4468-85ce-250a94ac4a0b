import mammoth
import platform
from loguru import logger

class DocParser:
    def __init__(self) -> None:
        pass

    def parse(self, file_path):
        # 获取操作系统类型
        system = platform.system()

        try:
            # Windows 使用 win32com
            if system == 'Windows':
                import win32com.client
                word = win32com.client.Dispatch("Word.Application")
                doc = word.Documents.Open('D:\\project\\LtdOrg\\related\\'+file_path)
                text = doc.Content.Text
                doc.Close()
                word.Quit()
                text = text.replace('\r\n', '\n').replace('\r', '\n')  # 统一处理换行符

                # text = "\n".join([i for i in text.split("\n") if i.strip() != ""])
                text = "\n".join([i.strip() for i in text.split("\n") if i.strip() != ""])
                
                return text

            # Linux/Mac 使用 antiword
            elif system in ['Linux', 'Darwin']:
                import subprocess
                result = subprocess.run(['catdoc', '/server/'+file_path], 
                                        capture_output=True, 
                                        text=True, 
                                        check=True)
                text=result.stdout
                
                text = text.replace('\r\n', '\n').replace('\r', '\n')  # 统一处理换行符

                # text = "\n".join([i for i in text.split("\n") if i.strip() != ""])
                text = "\n".join([i.strip() for i in text.split("\n") if i.strip() != ""])
                return text

            # 其他系统
            else:
                raise OSError(f"不支持的操作系统: {system}")

        except Exception as e:
            print(f"读取文档出错: {e}")
            return ""


class DocxParser:
    def parse(self, file_path):
        
        try:
            with open(file_path, "rb") as docx_file:
                result = mammoth.extract_raw_text(docx_file)
                text = result.value

            text = "\n".join([i for i in text.split("\n") if i.strip() != ""])
            return text
        
        except Exception as e:
                logger.error(f"Failed to parse the .docx file: {e}")
                return None
