"""
URL configuration for related project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path,include
from django.conf import settings
from django.conf.urls.static import static

def health(request):
    from django.db import connections
    from django.db.utils import OperationalError
    db=connections['default']
    try:
        db.cursor()
    except OperationalError:
        db_ok=False
    else:
        db_ok=True
    
    import os,redis
    from django.http import JsonResponse
    redis_url =  os.environ.get('CELERY_BROKER_URL')
    try:
        r = redis.from_url(redis_url)
        redis_ok = r.ping()
    except (redis.ConnectionError, redis.TimeoutError):
        redis_ok=False
    
    if db_ok and redis_ok:
        return JsonResponse({'status':'ok'},status=200)
    else:
        return JsonResponse({'status': 'unhealthy', 'db': db_ok, 'redis': redis_ok}, status=500)
    
urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/v1/',include('datasheet.urls')),
    path('api/v1/',include('members.urls')),
    path('api/v1/health/',health)
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
