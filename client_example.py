#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Milvus文件检索客户端示例

这个示例展示了如何使用新的文件检索API进行向量搜索
"""

import requests
import json
from typing import List, Optional, Dict, Any


class MilvusFileSearchClient:
    """Milvus文件检索客户端"""
    
    def __init__(self, base_url: str, api_key: Optional[str] = None):
        """
        初始化客户端
        
        Args:
            base_url: API基础URL，例如 'http://localhost:8000'
            api_key: API密钥（如果需要认证）
        """
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        
        if api_key:
            self.session.headers.update({'Authorization': f'Bearer {api_key}'})
    
    def search_by_files(
        self,
        query_text: str,
        database: List[str],
        collection: List[str],
        selected_files: Optional[List[str]] = None,
        selected_file_ids: Optional[List[int]] = None,
        uid_sheet: Optional[int] = None,
        top_k: int = 10,
        score_threshold: float = 0.45
    ) -> Dict[str, Any]:
        """
        根据勾选的文件进行向量检索
        
        Args:
            query_text: 搜索文本
            database: 数据库名称列表
            collection: 集合名称列表
            selected_files: 勾选的文件名列表
            selected_file_ids: 勾选的文件ID列表
            uid_sheet: 知识库ID
            top_k: 返回结果数量
            score_threshold: 分数阈值
            
        Returns:
            搜索结果字典
        """
        url = f"{self.base_url}/datasheet/filesearch/"
        
        payload = {
            'text': query_text,
            'database': database,
            'collection': collection,
            'top_k': top_k,
            'score_threshold': score_threshold
        }
        
        if selected_files:
            payload['selected_files'] = selected_files
        
        if selected_file_ids:
            payload['selected_file_ids'] = selected_file_ids
            
        if uid_sheet:
            payload['uid_sheet'] = uid_sheet
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {'error': str(e)}
    
    def get_file_list(self, uid_sheet: int, keyword: Optional[str] = None) -> Dict[str, Any]:
        """
        获取文件列表
        
        Args:
            uid_sheet: 知识库ID
            keyword: 搜索关键词
            
        Returns:
            文件列表
        """
        url = f"{self.base_url}/datasheet/frame/"
        params = {'uid_sheet': uid_sheet}
        
        if keyword:
            params['keyword'] = keyword
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {'error': str(e)}


def main():
    """示例用法"""
    # 初始化客户端
    client = MilvusFileSearchClient(
        base_url='http://localhost:8000',
        api_key='your_api_key_here'  # 如果需要认证
    )
    
    # 示例1：根据文件名进行检索
    print("=== 示例1：根据文件名检索 ===")
    result1 = client.search_by_files(
        query_text="如何使用API",
        database=['default'],
        collection=['default_coll'],
        selected_files=['API接口文档.pdf', '用户手册.docx'],
        top_k=5,
        score_threshold=0.4
    )
    
    if 'error' in result1:
        print(f"检索失败: {result1['error']}")
    else:
        data = result1.get('data', {})
        results = data.get('results', [])
        print(f"找到 {len(results)} 个结果")
        
        for i, result in enumerate(results[:3], 1):  # 只显示前3个结果
            metadata = result.get('metadata', {})
            score = metadata.get('score', 'N/A')
            file_name = metadata.get('file_name', '未知文件')
            content = result.get('page_content', '')[:100] + '...'  # 截取前100字符
            
            print(f"\n结果 {i}:")
            print(f"  文件: {file_name}")
            print(f"  相关度: {score}")
            print(f"  内容: {content}")
    
    print("\n" + "="*50 + "\n")
    
    # 示例2：根据文件ID进行检索
    print("=== 示例2：根据文件ID检索 ===")
    result2 = client.search_by_files(
        query_text="产品功能介绍",
        database=['default'],
        collection=['default_coll'],
        selected_file_ids=[1, 3, 5],  # 假设这些是文件的ID
        top_k=8,
        score_threshold=0.3
    )
    
    if 'error' in result2:
        print(f"检索失败: {result2['error']}")
    else:
        data = result2.get('data', {})
        print(f"过滤表达式: {data.get('filter_expr', 'None')}")
        print(f"总结果数: {data.get('total_results', 0)}")
    
    print("\n" + "="*50 + "\n")
    
    # 示例3：获取文件列表
    print("=== 示例3：获取文件列表 ===")
    file_list = client.get_file_list(uid_sheet=1, keyword="文档")
    
    if 'error' in file_list:
        print(f"获取文件列表失败: {file_list['error']}")
    else:
        files = file_list.get('data', [])
        print(f"找到 {len(files)} 个文件:")
        
        for file_info in files[:5]:  # 只显示前5个文件
            print(f"  ID: {file_info.get('id')}, 名称: {file_info.get('file_name')}")


def advanced_search_example():
    """高级搜索示例"""
    client = MilvusFileSearchClient(base_url='http://localhost:8000')
    
    # 多条件组合搜索
    search_configs = [
        {
            'name': '技术文档搜索',
            'query': 'API接口调用方法',
            'files': ['API文档.pdf', '开发指南.docx'],
            'threshold': 0.5
        },
        {
            'name': '用户手册搜索',
            'query': '如何安装软件',
            'files': ['用户手册.pdf', '安装指南.txt'],
            'threshold': 0.4
        },
        {
            'name': '故障排除搜索',
            'query': '常见错误解决方案',
            'files': ['FAQ.md', '故障排除.docx'],
            'threshold': 0.6
        }
    ]
    
    print("=== 高级搜索示例 ===")
    
    for config in search_configs:
        print(f"\n{config['name']}:")
        result = client.search_by_files(
            query_text=config['query'],
            database=['default'],
            collection=['default_coll'],
            selected_files=config['files'],
            top_k=3,
            score_threshold=config['threshold']
        )
        
        if 'error' in result:
            print(f"  搜索失败: {result['error']}")
        else:
            data = result.get('data', {})
            results = data.get('results', [])
            print(f"  找到 {len(results)} 个结果")
            
            if results:
                best_result = results[0]
                metadata = best_result.get('metadata', {})
                score = metadata.get('score', 'N/A')
                print(f"  最佳匹配: {metadata.get('file_name', '未知')} (相关度: {score})")


if __name__ == '__main__':
    print("Milvus文件检索客户端示例\n")
    
    # 运行基本示例
    main()
    
    # 运行高级示例
    advanced_search_example()
    
    print("\n示例运行完成！")
