from django.db import models
from django.contrib.auth.models import AbstractBaseUser

# Create your models here.
from django.contrib.auth.hashers import make_password

from members.models import Members

class DataSheet(AbstractBaseUser):
    '''数据集'''
    '''
    名称,知识库类型(公共,企业,员工),创建时间,修改时间,是否可用
    可为空:对应企业ID,对应员工ID    
    '''
    # 定义类型选项
    TYPE_CHOICES = (
        ('private', 'Private'),
        ('public', 'Public'),
        ('person', 'person'),
        ('company', 'company'), #公司知识库
        ('laws', 'laws'),
        ('notebook', 'notebook'),
    )

    sheet_name = models.CharField(verbose_name='数据名称',max_length=64)
    sheet_label = models.CharField(verbose_name='标签',null=True,max_length=255,default='这个人很懒什么都没说')
    sheet_model = models.CharField(verbose_name='索引模型',default='embedding',max_length=128)
    sheet_avatar_addr = models.CharField(verbose_name='头像',default='暂无头像',null=True,max_length=1024)
    sheet_level = models.IntegerField(verbose_name='知识库层级',default=2)
    sheet_type = models.CharField(verbose_name='类型（私有和公共）', max_length=16, choices=TYPE_CHOICES, default='private')  # 默认值为'private'
    sector_name = models.CharField(verbose_name='知识库分类名称',null=True,blank=True,default=None,max_length=255)    
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    password = models.CharField(max_length=255, verbose_name='知识库访问密码',default='123456')
    uuid_user = models.ForeignKey(
        Members, related_name='sheet_user', on_delete=models.CASCADE, default='',null=True,to_field='uuid_user')
    
    # 摘要相关字段
    summary = models.TextField(blank=True, null=True, verbose_name="摘要",default='')
    related_questions = models.CharField(max_length=1024, blank=True, null=True, verbose_name="相关问题",default='')
    username = models.CharField(max_length=20, verbose_name='用户姓名',default='',blank='',null='')
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
        
    class Meta:
        db_table = 'ai_datasheet'
        # abstract = True

    def __str__(self):
        return self.sheet_name
    
    def save(self,*args,**kwargs):
        self.password = make_password(self.password)
        super(DataSheet,self).save(*args,**kwargs)

def file_dir(instance,filename):
        # return '/'.join(['data/raw',str(instance.file_name),filename])
        return 'data/raw/{0}/{1}'.format(instance.uuid_user.username,filename)

class DataFrame(models.Model):
    # 文件状态
    AVA_CHOICES=(
        ('1','可用'),
        ('2','未上传'),
        ('3','上传中'),
        ('4','失败')
    )
    # 是否保存切片
    SQL_CHOICES=(
        ('1','已保存'),
        ('2','未保存'),
        ('3','已切片'),
        ('4','未切片'),
    )
    available = models.CharField(verbose_name='文件状态',choices=AVA_CHOICES, max_length=16,default='2',null=True)
    sql_available = models.CharField(verbose_name='切片状态',choices=AVA_CHOICES, max_length=16,default='4',null=True)
    create_time = models.DateTimeField(auto_now_add=True, verbose_name='上传时间')
    file_size= models.PositiveBigIntegerField(verbose_name='文件大小',null=True)#以字节为单位
    file_name = models.CharField(verbose_name='文件名',null=True,max_length=1024)
    rename = models.CharField(verbose_name='重命名文件',null=True,default='',max_length=1024)
    cache_id = models.CharField(verbose_name='缓存ID',null=True,default='',max_length=1024)
    file=models.FileField(upload_to=file_dir ,max_length=1024)

    sector=models.CharField(verbose_name='知识库类型',null=True,blank=True,default='法律',max_length=1024)
    desc=models.TextField(verbose_name='文件描述',null=True,blank=True,default='')
    url=models.TextField(verbose_name='提取地址',null=True,blank=True,default='')
    # 摘要相关字段
    summary = models.TextField(blank=True, null=True, verbose_name="摘要")
    topic = models.CharField(max_length=500, blank=True, null=True, verbose_name="主题")
    related_questions = models.CharField(max_length=1024, blank=True, null=True, verbose_name="相关问题")
    ai_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', '待分析'),
            ('processing', '分析中'),
            ('completed', '已完成'),
            ('failed', '失败')
        ],
        default='pending',
        verbose_name="AI分析状态"
    )
    
    is_temp = models.BooleanField(verbose_name='临时文件',default=False)

    uid_sheet = models.ForeignKey(
        DataSheet, related_name='sheet_frame', on_delete=models.CASCADE, default='',null=True)
    uuid_user = models.ForeignKey(
        Members, related_name='frame_user', on_delete=models.CASCADE, default='',null=True,to_field='uuid_user')
   
    update_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        db_table = 'ai_dataframe'
        # abstract = True

    def __str__(self):
        return f'id:{self.id},file_name:{self.file_name},rename:{self.rename}'

class Page(models.Model):
    uid_frame = models.ForeignKey(
        DataFrame, related_name='page_id', on_delete=models.CASCADE, default='',null=True)
    folder_id = models.CharField(verbose_name='文本ID',null=True,max_length=1024)
    page_content = models.TextField(verbose_name='文本内容',null=True,blank=True,default='')
    
    file_name= models.CharField(verbose_name='文件名称',null=True,max_length=1024)
    file_path= models.CharField(verbose_name='文件名称',null=True,max_length=1024)

    class Meta:
        db_table = 'ai_datadoc'
        # abstract = True

    def __str__(self):
        return self.folder_id