# Generated by Django 5.1.7 on 2025-06-20 06:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('datasheet', '0006_remove_notebookframe_uid_notebook_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='datasheet',
            name='sheet_type',
            field=models.CharField(choices=[('private', 'Private'), ('public', 'Public'), ('person', 'person'), ('company', 'company'), ('laws', 'laws'), ('notebook', 'notebook')], default='private', max_length=16, verbose_name='类型（私有和公共）'),
        ),
    ]
