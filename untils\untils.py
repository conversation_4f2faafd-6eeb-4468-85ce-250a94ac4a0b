from django.http import JsonResponse
from django.contrib.auth import login,logout,authenticate
from django.contrib.auth.backends import ModelBackend

from rest_framework import mixins,viewsets

from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import authentication_classes,permission_classes
from rest_framework_simplejwt.tokens import RefreshToken
from .exception import ExceptException
from django.core.paginator import Paginator,EmptyPage, PageNotAnInteger
from loguru import logger
from django.core.exceptions import FieldDoesNotExist
from members.Userserializers import UserGetSerializer
from datasheet.sheetserializers import FrameInfoSerializer
from datasheet.models import DataFrame
from members.models import Members,LoginLog

CODE_ERROR = {
    200:'Successful',
    400:'缺少必须参数',
    404:'当前记录不存在',
}

success={'code':200,'msg':'','data':''}

def message(data=success['data'],code=success['code'],msg=success['msg']):
    if not msg:
        msg = CODE_ERROR.get(code,'unknow error')
    return {'code':code,'msg':msg,'data':data}

import json
from uuid import UUID

class UUIDEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, UUID):
            return str(obj)
        return super().default(obj) 
class PaginationMixin(mixins.ListModelMixin):
    def paginate_data(self,queryset,page_key='pnum',per_page='psize'):
        pnum =  self.request.GET.get(page_key) if self.request.GET.get(page_key) else 1
        per_page =  self.request.GET.get(per_page) if self.request.GET.get(per_page) else 15
        paginator = Paginator(queryset,per_page)
        try:
            page = paginator.page(pnum)
        except PageNotAnInteger:
            # 如果页码不是整数，则返回第一页
            page = paginator.page(1)
        except EmptyPage:
            # 如果页码超出范围，则返回最后一页
            page = paginator.page(paginator.num_pages)
        logger.info(f'pnum:{pnum},psize:{per_page}')
        return page

    def get_paginated_response(self,queryset,serializer,page_key='pnum',per_page='psize'):
        page = self.paginate_data(queryset,page_key,per_page)
        serializer_data = serializer(page,many=True).data
        try:
            data ={
                'data':serializer_data,
                'total':page.paginator.count,
                'next':page.has_next() and page.next_page_number() or None,
                'previous':page.has_previous() and page.previous_page_number() or None
            }
            
        except Exception as e:
            es = ExceptException.handle_exception(e).get('exception_message')     
            logger.error(f'paginate exception:{es}')   
            
            return JsonResponse(message(code=400,msg=str(es))) 
        return JsonResponse(message(data=data),safe=False)

class CheckPermissions(object):
    user_field = ['uuid_user','uuid_company']    
    # 定义一个允许的用户列表
    allowed_users = ['administrator','hnch@1928']

    def get_object(self, *args, **kwargs):
        logger.info(f'验证权限,allowe:{self.request.user.username in self.allowed_users},user:{self.request.user.is_superuser,self.request.user.is_staff}')
        # print(getattr(obj, self.user_field) != self.request.user and self.request.user.username not in self.allowed_users)
        try:
            obj = super(CheckPermissions, self).get_object(*args, **kwargs)

            # 如果用户在allowed_users列表中，直接返回对象
            if self.request.user.username in self.allowed_users:
                return obj
            for field in self.user_field:
                if not hasattr(obj, field):
                    raise AttributeError(f"{obj.__class__.__name__} 必须有一个名为 {field} 的字段。")
                if getattr(obj, field) == self.request.user :
                    # raise JsonResponse(data="你没有权限访问这个对象。",safe=False)            
                    return obj        
        except Exception as e:
            es = ExceptException.handle_exception(e).get('exception_message')      
            logger.error(f'CheckPermissions raise exception:{es}')       
            return JsonResponse(message(code=400,msg=str(es))) 

class CheckUser(object):    
    def get_queryset(self):
        pass
        # # 如果当前用户是administrator，返回所有的 Members 对象
        # if self.request.user.username == 'administrator':
        #     return Members.objects.all()
        # # 仅返回与当前用户相关的QuerySet
        # return Members.objects.filter(id=self.request.user.id)
    
from pptx import Presentation
from io import BytesIO
from docx import Document
# from openpyxl import load_workbook
import os,fitz
from untils.parsers.parser import DocumentParser
import tempfile
import re
# from google import generativeai as genai
from PIL import Image as img

from datetime import datetime, timedelta
class MixinUntils(viewsets.GenericViewSet): 
    def parser_file(self,file_path):
        logger.error(file_path)
        ext = os.path.splitext(file_path)[1]
        par = DocumentParser()
        if ext in par.parse_exts:
            parser = par.parser_map.get(ext)
            file_content = parser.parse(file_path)
        elif ext in par.read_exts:
            file_content= par.read_file(file_path)
        else:
            logger.warning('skip file:{file_path}')

        return file_content   

    # def extract_image(self,path):
    #     genai.configure(api_key="AIzaSyAgmyPQ7XnFfqCnOWnS7dnCuQYb7_CWv5Q")

    #     imge = img.open(path)
    #     model = genai.GenerativeModel('gemini-pro-vision')
    #     response = model.generate_content(imge)
    #     text=''
    #     for chunk in response:
    #         text += chunk.text
    #     logger.warning(text)
    #     return text 
    
    # def chat_genai(self,input):
    #     genai.configure(api_key="AIzaSyAgmyPQ7XnFfqCnOWnS7dnCuQYb7_CWv5Q")
    #     model = genai.GenerativeModel('gemini-pro')
    #     response = model.generate_content(input)
    #     res=''
    #     for chunk in response:
    #         res += chunk.text
    #     return res 
    
    def extract_file(self,file):
        res =[]
        try:
            _, ext = os.path.splitext(file.name)
            ext = ext.lower()
            file.seek(0)  # 重置文件指针
            if ext in [".pdf", ".docx",".doc", ".pptx"]:
                    # 创建一个临时文件并将二进制数据写入其中
                with tempfile.NamedTemporaryFile(delete=False, suffix=ext) as temp_file:
                    temp_file.write(file.read())
                    # 将临时文件的路径传递给 parser_file 方法                    
                    # logger.info(temp_file.name)
                    content = self.parser_file(temp_file.name)
                    res.append(content)
            elif ext in [".txt", ".md"]:
                content = file.read().decode('utf-8')
                res.append(content)
            else:
                content = self.extract_image(file)
                res.append(content)
        except Exception as e:
            logger.error(str(e))
        return res
    # 针对文件列表提取文件内容
    def extract(self,fileobj,ispre=False,presize=512):
        try:
            res = []
            for file in fileobj:
                res.append( self.extract_file(file))
            if not ispre:
                return res
            else:
                pre = []
                for r in res:
                    pre.append(str(r).strip()[:presize])
                return pre
        except Exception as e:
            logger.error(str(e))
    # 针对单个文件，提取预览或者提取全部
    def extract_preview(self ,file,ispre=False,presize=512):
        try:
            res = self.extract_file(file)
            if ispre:
                return str(res).strip()[:presize]
            else:
                # logger.info(re.sub(r"[\[\]'\r\n]+", '', str(res)))
                return re.sub(r"[\[\]'|\\n|\\r]+", '', str(res))
        except Exception as e:
            logger.error(str(e))

    def extract_content(self,keyword):
            # 假设我们的查询集是data_frames
        data_frames = DataFrame.objects.filter(desc__icontains=keyword)

        # 处理查询集结果
        for df in data_frames:
            desc = df.desc
            try:
                # 找到关键词的位置
                keyword_pos = desc.lower().index(keyword.lower())
                # 截取关键词前后各100个字符
                start = max(keyword_pos - 100, 0)  # 确保不会是负数
                end = keyword_pos + len(keyword) + 100
                # 截取的内容
                snippet = desc[start:end]
            except ValueError:
                # 如果关键词不在desc中，这个异常将被触发
                snippet = desc

            # 现在你可以处理截取的字符串片段
            print(snippet)

    def get_frame_info(cache_id):
        data = DataFrame.objects.filter(cache_id=cache_id)
        ser =FrameInfoSerializer(data, many=True)
        return ser.data
       
    def get_value(self,request,key):
        if hasattr(request,'data') and key in request.data:
            return request.data.get(key)
        return request.GET.get(key)
    def get_request_params(self,request,*keys):
        return tuple(self.get_value(request,key) for key in keys)
    
    # 如果当前用户是管理员获取所有应用信息,如果不是只能获取该用户自己的数据信息
    def get_queryset(self):
       return self.queryset.all()

    
class Mixin(mixins.CreateModelMixin,mixins.RetrieveModelMixin,mixins.ListModelMixin,MixinUntils):     
    def get(self, request ,*args,**kwargs):  # 检索
        try:
            response = self.list(request,*args,**kwargs)
            return JsonResponse(message(data=response.data)) 
        except Exception as e:            
            es = ExceptException.handle_exception(e).get('exception_message')  
            logger.error(f'mixins method get raise exception:{es}')  
            return JsonResponse(message(code=400,msg=str(e))) 
    
    def post(self, request):	#增加
        try:
            response = self.create(request)
            return JsonResponse(message(data=response.data)) 
        except Exception as e:
            es = ExceptException.handle_exception(e).get('exception_message')       
            logger.error(f'mixins method post raise exception:{es}')  
            return JsonResponse(message(code=400,msg=str(e))) 
        
class MixinID(mixins.RetrieveModelMixin,mixins.UpdateModelMixin,mixins.DestroyModelMixin,MixinUntils):    
    def put(self, request,id,*args,**kwargs):	#修改   
        if request.user.is_authenticated or request.user.is_superuser:
            try:
                response = self.partial_update(request,*args,**kwargs)  # partial_update 部分更新                
                return JsonResponse(message(data=response.data)) 
            except Exception as e:
                es = ExceptException.handle_exception(e)      
                logger.error(f'mixins method put raise exception:{es}')       
                return JsonResponse(message(code=400,msg=str(e)))         
        else:
            return JsonResponse(message(code=401,msg='未登录,请前往登录页面'))        

    def delete(self, request, id,*args, **kwargs):	#删除
        if request.user.is_authenticated or request.user.is_superuser:        
            try:
                # response = self.destroy(request, *args, **kwargs)
                return DestroyMixin.destroy(self,request, *args, **kwargs)
            except Exception as e:
                es = ExceptException.handle_exception(e).get('exception_message')    
                logger.error(f'mixins method delete raise exception:{es}')      
                return JsonResponse(message(code=400,msg=str(e)))         
        


'''重写destory方法,实现删除后返回当前删除数据'''
class DestroyMixin(mixins.DestroyModelMixin):
    # @authentication_classes([JWTAuthentication])
    # @permission_classes([IsAuthenticated])
    def destroy(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            serializer_data = serializer.data
            instance.delete()
            return JsonResponse(message(data=serializer_data))
        except Exception as e:
            es = ExceptException.handle_exception(e).get('exception_message')   
            logger.error(f'mixins method get raise exception:{es}')  
            return JsonResponse(message(code=400,data=serializer_data,msg=str(e)))

        # return super().destroy(request, *args, **kwargs)
    
    def multiple_destroy(self, request, *args, **kwargs):
        try:
            # 从 URL 参数获取 id 列表
            ids = kwargs.get('ids', '').split(',')
            ids = [int(id_) for id_ in ids if id_.isdigit()]
            print(ids)
            
            if not ids:
                return JsonResponse(message(code=400, msg="请提供要删除的 ID 列表"))

            # 查询所有对象
            queryset = self.get_queryset().filter(id__in=ids)
            if not queryset.exists():
                return JsonResponse(message(code=404, msg="未找到要删除的对象"))

            # 序列化数据
            serializer = self.get_serializer(queryset, many=True)
            serializer_data = serializer.data

            # 执行删除
            queryset.delete()
            
            return JsonResponse(message(data=serializer_data))
        except Exception as e:
            es = ExceptException.handle_exception(e).get('exception_message')   
            logger.error(f'mixins method get raise exception:{es}')  
            return JsonResponse(message(code=400, msg=str(e)))
        
    def group_destroy(self, request, *args, **kwargs):
        try:
            cache_id = kwargs.get('cache_id')
            if not cache_id:
                return JsonResponse(message(code=400, msg="请提供缓存ID"))

            queryset = self.get_queryset().filter(cache_id=cache_id)
            if not queryset.exists():
                return JsonResponse(message(code=404, msg="未找到相关记录"))

            serializer = self.get_serializer(queryset, many=True)
            serializer_data = serializer.data
            queryset.delete()
            
            return JsonResponse(message(data=serializer_data))
        except Exception as e:
            es = ExceptException.handle_exception(e).get('exception_message')   
            logger.error(f'mixins method get raise exception:{es}')  
            return JsonResponse(message(code=400, msg=str(e)))

class LogoutMixin:    
    @authentication_classes([JWTAuthentication])
    @permission_classes([IsAuthenticated])
    def logout(self,request):
        logout(request)
        logger.info(f'退出登录')
        if request.user.is_authenticated:
            return JsonResponse(message(data="Logout failed!"))
        else:
            return JsonResponse(message(data="Logout successful!"))
import requests
class LoginMixin:
    # @authentication_classes([JWTAuthentication])
    # @permission_classes([IsAuthenticated])
    def check_lastlogin(self , user):
        return True
        # last = user.last_login
        # if user.username in ['hnch@1928','admin']:
        #     return True

        # if last is None:
        #     return True        
        # # 当前时间
        # current_time = datetime.now(last.tzinfo)  # 确保使用相同的时区
        # # 定义时间间隔：16小时
        # time_interval = timedelta(hours=4)

        # return current_time - last > time_interval
    def check_staffuser(self, username):
    # 这里应该是一个请求到总后端的逻辑，下面的代码是示例，需要根据你的实际后端API来修改
        import requests
        try:
            base_url=os.environ.get('VALIDATIONURL')
            response = requests.post(base_url, json={'username': username})
            response_data = response.json()
            return response_data.get('is_staff', False)
        except requests.RequestException as e:
            logger.error(f"请求超级管理员验证失败: {str(e)}")
            return False
    def login(self,request,*args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # # 假设这是您的用户认证中心的API地址
        # AUTH_API_URL = 'http://10.0.0.120:8087/api'
        username=serializer.validated_data['username']
        password=serializer.validated_data['password']

        # res = requests.post(f'{AUTH_API_URL}/user/login/',json={'username': username, 'password': password})
        
        # data = res.json()
        # logger.info(data)
        # return JsonResponse(data)
        try:
            user = Members.objects.get(username = username)
        except Members.DoesNotExist:
            logger.error("用户不存在")        
            return JsonResponse(message(code=500,msg="用户不存在"))
        
        
        if not user.is_active:
            return JsonResponse(message(code=500,msg=" 暂未激活请联系管理员"))
        
        if self.check_lastlogin(user):
            try:
                user = authenticate(username=username,password=password)
                
                if user is not None:
                    # print('登录',serializer.validated_data['username'],serializer.validated_data['password'])
                    refresh = RefreshToken.for_user(user)

                    # 根据员工查询对应的企业所有的部门和职位
                    # usr_data= CompanyAllSerializer(user.com_uid)
                    # usr_data= CompanySerializer(user.com_uid)

                    # 根据员工查询对应的企业 部门 职业
                    usr_data =UserGetSerializer(user)

                    # dep_data = DepartmentSerializer(user.role_uid)
                    # pos_data = PositionSerializer(dep_data.department)
                    data = usr_data.data
                    access_token = str(refresh.access_token)
                    # data = user.to_json()
                    data.setdefault('token' , access_token)
                    login(request, user)
                    return JsonResponse(message(data=data))
                else:
                    return JsonResponse(message(code=400,data=serializer.data,msg='Failed'))
            except Exception as e:
                return JsonResponse(message(code=400,data=serializer.data,msg=str(e)))

        
        else:
            return JsonResponse(message(code=500,msg=" 已绑定设备"))
    def LoginByUsername(self,request,*args, **kwargs):
        username=request.data.get('username')
        logger.warning(username)
        
        login_logs = LoginLog.objects.filter(username=username, status=1)
        
        if  not login_logs.exists():
            return JsonResponse(message(code=500,msg="未查询到记录,无法登录"))
        
        try:
            user = Members.objects.get(username = username)
        except Members.DoesNotExist:
            logger.error("用户不存在,系统自动创建用户")
            try:
                user = Members.objects.create(
                    username = username,
                    is_active=True,
                )
                user.save()
                logger.info(f"成功创建用户: {username}")
            except Exception as e:
                logger.error(f"创建用户失败: {str(e)}")        
                return JsonResponse(message(code=500,msg="用户不存在"))
        
        if not user.is_active:
            return JsonResponse(message(code=500,msg=" 暂未激活请联系管理员"))
        
        if self.check_lastlogin(user):
            try:                
                if user is not None:
                    # print('登录',serializer.validated_data['username'],serializer.validated_data['password'])
                    if int (user.roles) == 3 :
                        if not self.check_staffuser(username):
                            logger.info('验证用户权限')
                            
                            user.roles = 2
                            user.save()
                        else:
                            user.roles = 1
                            user.save()
                        # # 判断role并修改
                        # return JsonResponse(message(code=403,msg='您没有操作权限,请联系管理员'))
                    # else:
                    from untils.refreshToken import CustomRefreshToken
                    # refresh = RefreshToken.for_user(user)
                    refresh = CustomRefreshToken.for_user(user)

                    # 根据员工查询对应的企业所有的部门和职位
                    # usr_data= CompanyAllSerializer(user.com_uid)
                    # usr_data= CompanySerializer(user.com_uid)

                    # 根据员工查询对应的企业 部门 职业
                    usr_data =UserGetSerializer(user)
                    data = usr_data.data
                    access_token = str(refresh)
                    # data = user.to_json()
                    data.setdefault('token' , access_token)
                    print(access_token,'1111111')
                    login(request, user)
                    
                    uplog = LoginLog.objects.get(username=username, status=1)
                    uplog.status='0'
                    uplog.save()
                    return JsonResponse(message(data=data))
                else:
                    return JsonResponse(message(code=400,data=username,msg='Failed'))
            except Exception as e:
                return JsonResponse(message(code=400,data=username,msg=str(e)))

        
        else:
            return JsonResponse(message(code=500,msg=" 已绑定设备"))