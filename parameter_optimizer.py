#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全文检索参数优化器

根据不同场景自动推荐最佳参数配置
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum


class DocumentType(Enum):
    """文档类型"""
    NOVEL = "novel"              # 小说文学
    TECHNICAL = "technical"      # 技术文档
    LEGAL = "legal"             # 法律条文
    ACADEMIC = "academic"       # 学术论文
    BUSINESS = "business"       # 商业文档
    NEWS = "news"               # 新闻报道
    MANUAL = "manual"           # 用户手册
    UNKNOWN = "unknown"         # 未知类型


class AnalysisGoal(Enum):
    """分析目标"""
    QUICK_PREVIEW = "quick_preview"      # 快速预览
    THEME_ANALYSIS = "theme_analysis"    # 主题分析
    ENTITY_EXTRACTION = "entity_extraction"  # 实体提取
    COMPARATIVE = "comparative"          # 对比分析
    DEEP_RESEARCH = "deep_research"      # 深度研究
    CONTENT_SUMMARY = "content_summary"  # 内容总结


class PerformanceLevel(Enum):
    """性能要求"""
    FAST = "fast"           # 快速响应
    BALANCED = "balanced"   # 平衡性能
    THOROUGH = "thorough"   # 全面分析


@dataclass
class DocumentInfo:
    """文档信息"""
    file_names: List[str]
    estimated_size: str = "medium"  # small, medium, large
    document_type: DocumentType = DocumentType.UNKNOWN
    language: str = "zh"  # zh, en


@dataclass
class AnalysisRequirement:
    """分析需求"""
    goal: AnalysisGoal
    performance_level: PerformanceLevel
    quality_threshold: str = "medium"  # low, medium, high
    coverage_preference: str = "balanced"  # narrow, balanced, broad


class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self):
        # 预设配置模板
        self.config_templates = {
            "quick_preview": {
                "batch_size": 30,
                "min_score": 0.3,
                "max_results": 80,
                "enable_deep_analysis": False
            },
            "standard_analysis": {
                "batch_size": 50,
                "min_score": 0.2,
                "max_results": 300,
                "enable_deep_analysis": True
            },
            "deep_research": {
                "batch_size": 100,
                "min_score": 0.15,
                "max_results": 600,
                "enable_deep_analysis": True
            },
            "comparative": {
                "batch_size": 80,
                "min_score": 0.2,
                "max_results": 500,
                "enable_deep_analysis": True
            }
        }
    
    def optimize_parameters(self, 
                          query: str,
                          doc_info: DocumentInfo,
                          requirements: AnalysisRequirement) -> Dict[str, Any]:
        """
        根据输入优化参数
        
        Args:
            query: 查询文本
            doc_info: 文档信息
            requirements: 分析需求
            
        Returns:
            优化后的参数配置
        """
        # 基础配置
        config = self._get_base_config(requirements.goal)
        
        # 根据文档特征调整
        config = self._adjust_for_document(config, doc_info)
        
        # 根据查询特征调整
        config = self._adjust_for_query(config, query)
        
        # 根据性能要求调整
        config = self._adjust_for_performance(config, requirements.performance_level)
        
        # 根据质量要求调整
        config = self._adjust_for_quality(config, requirements.quality_threshold)
        
        # 根据覆盖偏好调整
        config = self._adjust_for_coverage(config, requirements.coverage_preference)
        
        # 设置分析类型
        config["analysis_type"] = self._determine_analysis_type(doc_info.document_type, requirements.goal)
        
        # 添加推荐说明
        config["_recommendations"] = self._generate_recommendations(config, doc_info, requirements)
        
        return config
    
    def _get_base_config(self, goal: AnalysisGoal) -> Dict[str, Any]:
        """获取基础配置"""
        if goal == AnalysisGoal.QUICK_PREVIEW:
            return self.config_templates["quick_preview"].copy()
        elif goal == AnalysisGoal.DEEP_RESEARCH:
            return self.config_templates["deep_research"].copy()
        elif goal == AnalysisGoal.COMPARATIVE:
            return self.config_templates["comparative"].copy()
        else:
            return self.config_templates["standard_analysis"].copy()
    
    def _adjust_for_document(self, config: Dict[str, Any], doc_info: DocumentInfo) -> Dict[str, Any]:
        """根据文档特征调整参数"""
        # 根据文档数量调整
        file_count = len(doc_info.file_names)
        if file_count == 1:
            # 单文档：可以更深入分析
            config["batch_size"] = min(config["batch_size"] * 1.2, 150)
            config["min_score"] *= 0.9
        elif file_count > 3:
            # 多文档：需要平衡性能
            config["batch_size"] = max(config["batch_size"] * 0.8, 30)
            config["min_score"] *= 1.1
        
        # 根据文档大小调整
        if doc_info.estimated_size == "small":
            config["batch_size"] = max(config["batch_size"] * 0.7, 20)
            config["max_results"] = max(config["max_results"] * 0.7, 50)
        elif doc_info.estimated_size == "large":
            config["batch_size"] = min(config["batch_size"] * 1.3, 200)
            config["max_results"] = min(config["max_results"] * 1.5, 1000)
        
        # 根据文档类型调整
        if doc_info.document_type == DocumentType.TECHNICAL:
            config["min_score"] *= 1.1  # 技术文档要求更高精确度
        elif doc_info.document_type == DocumentType.NOVEL:
            config["min_score"] *= 0.9  # 文学作品可以更宽松
        elif doc_info.document_type == DocumentType.LEGAL:
            config["min_score"] *= 1.2  # 法律文档要求最高精确度
        
        return config
    
    def _adjust_for_query(self, config: Dict[str, Any], query: str) -> Dict[str, Any]:
        """根据查询特征调整参数"""
        words = query.split()
        word_count = len(words)
        
        if word_count <= 2:
            # 短查询：降低阈值，增加结果
            config["min_score"] *= 0.8
            config["max_results"] = min(config["max_results"] * 1.3, 500)
        elif word_count >= 6:
            # 长查询：提高阈值，保证质量
            config["min_score"] *= 1.2
            config["max_results"] = max(config["max_results"] * 0.8, 100)
        
        # 检查是否包含专业术语
        technical_terms = ["API", "算法", "架构", "接口", "协议", "框架"]
        if any(term in query for term in technical_terms):
            config["min_score"] *= 1.1
        
        return config
    
    def _adjust_for_performance(self, config: Dict[str, Any], performance: PerformanceLevel) -> Dict[str, Any]:
        """根据性能要求调整参数"""
        if performance == PerformanceLevel.FAST:
            config["batch_size"] = max(config["batch_size"] * 0.6, 20)
            config["max_results"] = max(config["max_results"] * 0.5, 50)
            config["enable_deep_analysis"] = False
        elif performance == PerformanceLevel.THOROUGH:
            config["batch_size"] = min(config["batch_size"] * 1.5, 200)
            config["max_results"] = min(config["max_results"] * 1.8, 1000)
            config["enable_deep_analysis"] = True
        
        return config
    
    def _adjust_for_quality(self, config: Dict[str, Any], quality: str) -> Dict[str, Any]:
        """根据质量要求调整参数"""
        if quality == "high":
            config["min_score"] *= 1.3
        elif quality == "low":
            config["min_score"] *= 0.7
        
        return config
    
    def _adjust_for_coverage(self, config: Dict[str, Any], coverage: str) -> Dict[str, Any]:
        """根据覆盖偏好调整参数"""
        if coverage == "broad":
            config["min_score"] *= 0.8
            config["max_results"] = min(config["max_results"] * 1.5, 800)
        elif coverage == "narrow":
            config["min_score"] *= 1.2
            config["max_results"] = max(config["max_results"] * 0.7, 100)
        
        return config
    
    def _determine_analysis_type(self, doc_type: DocumentType, goal: AnalysisGoal) -> str:
        """确定分析类型"""
        if doc_type == DocumentType.TECHNICAL:
            return "technical"
        elif doc_type == DocumentType.LEGAL:
            return "legal"
        elif doc_type == DocumentType.ACADEMIC:
            return "academic"
        elif goal == AnalysisGoal.COMPARATIVE:
            return "comparative"
        else:
            return "general"
    
    def _generate_recommendations(self, config: Dict[str, Any], 
                                doc_info: DocumentInfo, 
                                requirements: AnalysisRequirement) -> List[str]:
        """生成使用建议"""
        recommendations = []
        
        if config["min_score"] > 0.4:
            recommendations.append("使用了较高的相关度阈值，确保结果质量")
        elif config["min_score"] < 0.2:
            recommendations.append("使用了较低的相关度阈值，增加内容覆盖面")
        
        if config["max_results"] > 500:
            recommendations.append("设置了较大的结果数量，适合全面分析")
        elif config["max_results"] < 100:
            recommendations.append("设置了较小的结果数量，适合快速预览")
        
        if not config["enable_deep_analysis"]:
            recommendations.append("关闭了深度分析以提升响应速度")
        
        if len(doc_info.file_names) > 1:
            recommendations.append("多文档分析，建议关注内容聚类结果")
        
        return recommendations
    
    def get_preset_configs(self) -> Dict[str, Dict[str, Any]]:
        """获取预设配置"""
        return {
            "快速预览": {
                "description": "快速了解文档主要内容",
                "config": self.config_templates["quick_preview"]
            },
            "标准分析": {
                "description": "平衡质量和覆盖面的标准分析",
                "config": self.config_templates["standard_analysis"]
            },
            "深度研究": {
                "description": "全面深入的主题分析",
                "config": self.config_templates["deep_research"]
            },
            "对比分析": {
                "description": "多文档对比分析",
                "config": self.config_templates["comparative"]
            }
        }


def demo_parameter_optimization():
    """演示参数优化"""
    optimizer = ParameterOptimizer()
    
    print("📊 参数优化器演示")
    print("=" * 50)
    
    # 测试场景
    scenarios = [
        {
            "name": "三国演义战役分析",
            "query": "战争 战役 军事 策略",
            "doc_info": DocumentInfo(
                file_names=["三国演义.txt"],
                estimated_size="large",
                document_type=DocumentType.NOVEL
            ),
            "requirements": AnalysisRequirement(
                goal=AnalysisGoal.THEME_ANALYSIS,
                performance_level=PerformanceLevel.BALANCED
            )
        },
        {
            "name": "技术文档快速预览",
            "query": "API 接口",
            "doc_info": DocumentInfo(
                file_names=["API文档.pdf"],
                estimated_size="medium",
                document_type=DocumentType.TECHNICAL
            ),
            "requirements": AnalysisRequirement(
                goal=AnalysisGoal.QUICK_PREVIEW,
                performance_level=PerformanceLevel.FAST
            )
        },
        {
            "name": "法律条文深度研究",
            "query": "违法 处罚 责任",
            "doc_info": DocumentInfo(
                file_names=["道路交通.txt", "刑法条文.pdf"],
                estimated_size="large",
                document_type=DocumentType.LEGAL
            ),
            "requirements": AnalysisRequirement(
                goal=AnalysisGoal.DEEP_RESEARCH,
                performance_level=PerformanceLevel.THOROUGH,
                quality_threshold="high"
            )
        }
    ]
    
    for scenario in scenarios:
        print(f"\n🎯 场景: {scenario['name']}")
        print(f"查询: {scenario['query']}")
        print(f"文件: {scenario['doc_info'].file_names}")
        
        config = optimizer.optimize_parameters(
            scenario['query'],
            scenario['doc_info'],
            scenario['requirements']
        )
        
        print(f"优化后的参数:")
        print(f"  - batch_size: {config['batch_size']}")
        print(f"  - min_score: {config['min_score']:.3f}")
        print(f"  - max_results: {config['max_results']}")
        print(f"  - analysis_type: {config['analysis_type']}")
        print(f"  - enable_deep_analysis: {config['enable_deep_analysis']}")
        
        if config.get('_recommendations'):
            print(f"建议:")
            for rec in config['_recommendations']:
                print(f"    • {rec}")
        
        print("-" * 40)


if __name__ == "__main__":
    demo_parameter_optimization()
