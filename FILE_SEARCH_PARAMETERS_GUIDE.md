# 选定文件检索API参数说明文档

## 📡 API端点

```
POST /datasheet/filesearch/
```

## 📋 请求参数详解

### 🔴 必需参数

#### `text` (string, 必需)
- **描述**: 搜索查询文本
- **示例**: 
  ```json
  "text": "API接口调用方法"
  "text": "交通违章处理流程"
  "text": "系统配置文件"
  ```
- **建议**: 使用具体的关键词，避免过于宽泛

#### `database` (array, 必需)
- **描述**: 数据库名称列表
- **默认值**: `["default"]`
- **示例**: `"database": ["default"]`

#### `collection` (array, 必需)
- **描述**: 集合名称列表
- **默认值**: `["chzngk228"]`
- **示例**: `"collection": ["chzngk228"]`

### 🟡 文件选择参数（至少选择一种）

#### `selected_files` (array, 可选)
- **描述**: 勾选的文件名列表
- **格式**: 字符串数组
- **示例**:
  ```json
  "selected_files": ["新建 文本文档 (2).txt", "道路交通.txt"]
  "selected_files": ["API文档.pdf"]
  ```
- **注意**: 文件名必须与知识库中完全匹配

#### `selected_file_ids` (array, 可选)
- **描述**: 勾选的文件ID列表（DataFrame表的ID）
- **格式**: 整数数组
- **示例**: `"selected_file_ids": [3591, 3592]`
- **说明**: 系统会根据ID查询对应的文件名

#### `uid_sheet` (integer, 可选)
- **描述**: 知识库ID，用于额外过滤
- **示例**: `"uid_sheet": 123`

### 🟢 检索控制参数

#### `top_k` (integer, 可选)
- **描述**: 返回结果数量
- **默认值**: `10`
- **取值范围**: 1-50
- **建议**:
  - 快速查找: `5-10`
  - 标准检索: `10-20`
  - 全面检索: `20-30`
- **示例**: `"top_k": 15`

#### `score_threshold` (float, 可选)
- **描述**: 分数阈值，过滤低相关度结果
- **默认值**: `0.45`
- **取值范围**: 0.0-1.0
- **建议**:
  - 严格筛选: `0.6-0.8`
  - 平衡质量: `0.4-0.6`
  - 宽松筛选: `0.2-0.4`
- **示例**: `"score_threshold": 0.5`

### 🔵 增强功能参数

#### `query_enhancement` (boolean, 可选)
- **描述**: 是否启用查询增强
- **默认值**: `true`
- **说明**: 启用同义词扩展和多查询策略
- **示例**: `"query_enhancement": true`

#### `smart_optimization` (boolean, 可选)
- **描述**: 是否启用智能参数优化
- **默认值**: `true`
- **说明**: 根据查询和文件特征自动调整参数
- **示例**: `"smart_optimization": true`

#### `search_strategy` (string, 可选)
- **描述**: 搜索策略
- **默认值**: `"adaptive"`
- **可选值**:
  - `"precise"` - 精确搜索，高精度
  - `"broad"` - 广泛搜索，高覆盖
  - `"adaptive"` - 自适应搜索
  - `"balanced"` - 平衡搜索
- **示例**: `"search_strategy": "precise"`

#### `enable_fallback` (boolean, 可选)
- **描述**: 是否启用回退策略
- **默认值**: `true`
- **说明**: 当结果不足时自动降低阈值重试
- **示例**: `"enable_fallback": true`

## 📊 参数组合建议

### 🎯 场景1: 精确查找特定信息
```json
{
    "text": "用户登录API接口参数",
    "selected_files": ["API文档.pdf"],
    "top_k": 5,
    "score_threshold": 0.6,
    "search_strategy": "precise",
    "query_enhancement": true
}
```

### 🔍 场景2: 广泛搜索相关内容
```json
{
    "text": "系统配置相关",
    "selected_files": ["技术文档.pdf", "用户手册.docx"],
    "top_k": 20,
    "score_threshold": 0.3,
    "search_strategy": "broad",
    "enable_fallback": true
}
```

### ⚡ 场景3: 快速预览
```json
{
    "text": "主要功能介绍",
    "selected_files": ["产品说明.txt"],
    "top_k": 8,
    "score_threshold": 0.5,
    "query_enhancement": false,
    "smart_optimization": false
}
```

### 🎭 场景4: 智能自适应搜索
```json
{
    "text": "错误处理和异常情况",
    "selected_file_ids": [3591, 3592],
    "top_k": 15,
    "search_strategy": "adaptive",
    "smart_optimization": true,
    "enable_fallback": true
}
```

## 📈 响应格式

### 基础响应
```json
{
    "code": 200,
    "data": {
        "results": [
            {
                "page_content": "文档内容片段",
                "metadata": {
                    "file_name": "文件名.pdf",
                    "score": 0.75,
                    "relevance_level": "high",
                    "file_type": "pdf"
                }
            }
        ],
        "query": "搜索文本",
        "filter_expr": "file_name in ['文件1.pdf', '文件2.txt']",
        "total_results": 8
    }
}
```

### 增强响应（启用智能优化时）
```json
{
    "code": 200,
    "data": {
        "results": [...],
        "query": "搜索文本",
        "filter_expr": "过滤表达式",
        "total_results": 8,
        
        "file_info": {
            "文件1.pdf": {
                "source": "filename",
                "type": "pdf",
                "size": 1024
            }
        },
        
        "file_stats": {
            "文件1.pdf": {
                "count": 3,
                "avg_score": 0.72
            }
        },
        
        "search_params": {
            "top_k": 15,
            "score_threshold": 0.35,
            "optimized": true
        },
        
        "quality_metrics": {
            "avg_score": 0.68,
            "score_distribution": {
                "high": 2,
                "medium": 4,
                "low": 2
            },
            "content_diversity": 2
        }
    }
}
```

## ⚙️ 性能优化建议

### 根据文件数量调整
```python
# 单文件
{
    "top_k": 15,
    "score_threshold": 0.3,
    "search_strategy": "broad"
}

# 2-3个文件
{
    "top_k": 12,
    "score_threshold": 0.4,
    "search_strategy": "adaptive"
}

# 4个以上文件
{
    "top_k": 10,
    "score_threshold": 0.5,
    "search_strategy": "precise"
}
```

### 根据查询复杂度调整
```python
# 简单查询（1-2个词）
{
    "score_threshold": 0.3,
    "query_enhancement": true,
    "enable_fallback": true
}

# 复杂查询（3-5个词）
{
    "score_threshold": 0.45,
    "query_enhancement": true
}

# 精确查询（专业术语）
{
    "score_threshold": 0.6,
    "search_strategy": "precise"
}
```

## 🔧 故障排除

### 常见问题

1. **搜索结果为空**
   ```json
   // 解决方案：降低阈值，启用回退
   {
       "score_threshold": 0.2,
       "enable_fallback": true,
       "search_strategy": "broad"
   }
   ```

2. **结果质量不高**
   ```json
   // 解决方案：提高阈值，使用精确策略
   {
       "score_threshold": 0.6,
       "search_strategy": "precise",
       "query_enhancement": true
   }
   ```

3. **响应速度慢**
   ```json
   // 解决方案：减少结果数，关闭增强功能
   {
       "top_k": 8,
       "query_enhancement": false,
       "smart_optimization": false
   }
   ```

## 📝 完整请求示例

```json
{
    "text": "API接口调用错误处理",
    "database": ["default"],
    "collection": ["chzngk228"],
    "selected_files": ["API文档.pdf", "错误处理指南.docx"],
    "top_k": 12,
    "score_threshold": 0.4,
    "query_enhancement": true,
    "smart_optimization": true,
    "search_strategy": "adaptive",
    "enable_fallback": true
}
```

## 🎯 最佳实践

1. **文件选择**: 优先使用`selected_files`，文件名更直观
2. **参数调优**: 先用默认参数测试，再根据结果调整
3. **策略选择**: 不确定时使用`adaptive`策略
4. **质量控制**: 平衡`score_threshold`和`top_k`
5. **性能优化**: 大量文件时关闭部分增强功能
6. **回退机制**: 重要查询时启用`enable_fallback`
