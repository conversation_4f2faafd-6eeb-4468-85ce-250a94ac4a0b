
from dataclasses import dataclass
from typing import List

@dataclass
class ToutiaoContent:
    """今日头条内容数据类"""
    title: str
    content: str
    author: str
    publish_time: str
    read_count: str
    comment_count: str
    like_count: str     #点赞
    content_type: str  # 'article' 或 'video'
    url: str
    cover_image: str
    video_url: str = ""
    duration: int = 0   #视频时长
    view_count: int = 0 #播放量

@dataclass
class XiaohongshuContent:
    """小红书内容数据类"""
    title: str
    content: str
    author: str
    publish_time: str
    like_count: str     #点赞
    comment_count: str
    collect_count: str
    content_type: str  # 'note' 或 'video'
    url: str
    images: List[str]
    video_url: str = ""
    duration: int = 0   #视频时长
    tags: List[str] = None
    view_count: int = 0 #播放量

@dataclass
class DouyinContent:
    """抖音内容数据类"""
    title: str
    content: str
    author: str
    publish_time: str
    like_count: str     #点赞
    comment_count: str
    share_count: str
    content_type: str  # 'video'
    url: str
    cover_image: str
    video_url: str = ""
    duration: int = 0   #视频时长
    music_title: str = ""
    view_count: int = 0 #播放量
    
@dataclass
class WeChatArticleInfo:
    """微信公众号文章信息数据类"""
    title: str
    content: str
    author: str
    account_name: str
    publish_time: str
    read_count: str
    like_count: str     #点赞
    url: str
    cover_image: str    

@dataclass
class VideoInfo:
    """视频信息数据类"""
    title: str
    description: str
    duration: int       #视频时长
    view_count: int     #播放量
    like_count: int     #点赞
    upload_date: str
    uploader: str
    thumbnail: str
    url: str
    platform: str

@dataclass 
class SubtitleText:
    """字幕文本数据类"""
    language: str
    content: str  # 纯文本内容
    source: str   # 来源：subtitle/whisper    