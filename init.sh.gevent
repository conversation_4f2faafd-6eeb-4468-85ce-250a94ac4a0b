#!/bin/bash

LOCK_FILE="migrate.lock"
DJANGO_MANAGE="python manage.py"
GUNICORN_BIND="0.0.0.0:8088"
GUNICORN_WORKER_CLASS="gevent"  # 使用 gevent 作为 worker 类型
CELERY_APP="related"
NUM_CORES=$(grep -c ^processor /proc/cpuinfo)  # 获取 CPU 核心数
NUM_WORKERS=$(($NUM_CORES * 4))  # 计算 worker 数量，这里假设每核心4个

if [ ! -f "$LOCK_FILE" ]; then
    echo "Checking for migrations..."
    $DJANGO_MANAGE makemigrations --check --dry-run
    if [ $? -eq 1 ]; then
        echo "Migrations needed, applying..."
        $DJANGO_MANAGE makemigrations
        $DJANGO_MANAGE migrate
        touch "$LOCK_FILE"
    fi
fi

echo "Starting Gunicorn with $NUM_WORKERS workers..."
gunicorn --bind $GUNICORN_BIND --workers $NUM_WORKERS --worker-class $GUNICORN_WORKER_CLASS --timeout 360 --limit-request-field_size 0 --limit-request-line 0 ${CELERY_APP}.wsgi:application &

echo "Waiting for Gunicorn to start..."
sleep 10

echo "Starting Celery..."
celery -A $CELERY_APP worker --pool=prefork --concurrency=50 --loglevel=info --max-tasks-per-child=500

exec "$@"
