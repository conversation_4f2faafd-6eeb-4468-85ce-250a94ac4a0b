# 增强文件检索功能使用指南

## 🚀 新增优化功能概览

### 1. **智能查询增强**
- ✅ 自动同义词扩展
- ✅ 关键词提取和权重分析
- ✅ 查询意图识别
- ✅ 多查询策略组合

### 2. **自适应参数优化**
- ✅ 基于查询长度的智能调整
- ✅ 基于文件数量的参数优化
- ✅ 基于文件类型的权重调整
- ✅ 智能阈值计算

### 3. **多策略搜索引擎**
- ✅ 精确搜索 (precise)
- ✅ 广泛搜索 (broad)
- ✅ 自适应搜索 (adaptive)
- ✅ 平衡搜索 (balanced)

### 4. **结果质量优化**
- ✅ 智能去重算法
- ✅ 多维度重排序
- ✅ 回退策略机制
- ✅ 质量指标评估

## 📡 API接口升级

### 新增请求参数

```json
{
    "text": "搜索文本",
    "database": ["default"],
    "collection": ["chzngk228"],
    "selected_files": ["文件1.txt", "文件2.pdf"],
    
    // 🆕 新增优化参数
    "query_enhancement": true,           // 启用查询增强
    "smart_optimization": true,          // 启用智能优化
    "search_strategy": "adaptive",       // 搜索策略
    "enable_fallback": true,            // 启用回退策略
    "top_k": 10,
    "score_threshold": 0.45
}
```

### 增强响应格式

```json
{
    "code": 200,
    "data": {
        "results": [...],
        "query": "原始查询",
        "filter_expr": "过滤表达式",
        "total_results": 8,
        
        // 🆕 新增响应字段
        "file_info": {                   // 文件信息
            "文件1.txt": {
                "source": "filename",
                "type": "txt",
                "size": 1024
            }
        },
        "file_stats": {                  // 文件统计
            "文件1.txt": {
                "count": 3,
                "avg_score": 0.75
            }
        },
        "search_params": {               // 搜索参数
            "top_k": 15,                // 优化后的参数
            "score_threshold": 0.35,
            "optimized": true
        },
        "quality_metrics": {             // 质量指标
            "avg_score": 0.68,
            "score_distribution": {
                "high": 2,
                "medium": 4,
                "low": 2
            },
            "content_diversity": 2
        }
    }
}
```

## 🎯 使用场景和策略选择

### 场景1: 精确技术文档查询
```python
# 用户明确知道要在API文档中查找特定接口
payload = {
    "text": "用户登录接口参数说明",
    "selected_files": ["API接口文档.pdf"],
    "search_strategy": "precise",        # 精确搜索
    "query_enhancement": true,           # 启用查询增强
    "score_threshold": 0.6              # 较高阈值
}
```

### 场景2: 广泛探索性搜索
```python
# 用户想了解某个主题的全面信息
payload = {
    "text": "项目管理最佳实践",
    "selected_files": ["管理手册.docx", "项目指南.pdf", "经验总结.txt"],
    "search_strategy": "broad",          # 广泛搜索
    "query_enhancement": true,
    "score_threshold": 0.3,             # 较低阈值
    "top_k": 20                         # 更多结果
}
```

### 场景3: 智能自适应搜索
```python
# 让系统自动选择最佳策略
payload = {
    "text": "系统配置问题排查",
    "selected_files": ["技术文档.pdf", "故障排除.txt"],
    "search_strategy": "adaptive",       # 自适应搜索
    "smart_optimization": true,          # 智能优化
    "enable_fallback": true             # 启用回退
}
```

## 🔧 高级配置选项

### 1. 搜索策略详解

| 策略 | 适用场景 | 特点 | 参数调整 |
|------|----------|------|----------|
| **precise** | 精确查找 | 高阈值、严格匹配 | ef↑, nprobe↑ |
| **broad** | 探索发现 | 低阈值、宽松匹配 | ef↓, nprobe↓ |
| **adaptive** | 智能选择 | 根据条件自适应 | 动态调整 |
| **balanced** | 平衡搜索 | 中等参数、稳定 | 默认参数 |

### 2. 查询增强级别

```python
# 基础增强 (推荐)
"query_enhancement": true,
"enhancement_level": "basic"

# 高级增强 (复杂查询)
"query_enhancement": true, 
"enhancement_level": "advanced"

# 激进增强 (探索性搜索)
"query_enhancement": true,
"enhancement_level": "aggressive"
```

### 3. 智能优化配置

```python
# 完整智能优化配置
{
    "smart_optimization": true,
    "enable_smart_threshold": true,      // 智能阈值
    "enable_context_awareness": true,    // 上下文感知
    "enable_multi_query": true,         // 多查询策略
    "content_length_preference": "medium" // 内容长度偏好
}
```

## 📊 性能优化建议

### 1. 根据文件数量选择策略

```python
file_count = len(selected_files)

if file_count == 1:
    # 单文件：可以更宽松，获取更多结果
    config = {
        "score_threshold": 0.3,
        "top_k": 20,
        "search_strategy": "broad"
    }
elif file_count <= 5:
    # 少量文件：平衡策略
    config = {
        "score_threshold": 0.45,
        "top_k": 10,
        "search_strategy": "adaptive"
    }
else:
    # 多文件：提高精确度
    config = {
        "score_threshold": 0.6,
        "top_k": 8,
        "search_strategy": "precise"
    }
```

### 2. 根据查询类型优化

```python
def optimize_by_query_type(query):
    query_lower = query.lower()
    
    if any(word in query_lower for word in ["怎么", "如何", "方法"]):
        # 操作指导类查询
        return {
            "query_enhancement": True,
            "search_strategy": "broad",
            "score_threshold": 0.4
        }
    elif any(word in query_lower for word in ["错误", "问题", "故障"]):
        # 故障排除类查询
        return {
            "query_enhancement": True,
            "search_strategy": "precise",
            "score_threshold": 0.5
        }
    elif len(query.split()) <= 2:
        # 短查询
        return {
            "query_enhancement": True,
            "search_strategy": "broad",
            "score_threshold": 0.3,
            "top_k": 15
        }
    else:
        # 默认配置
        return {
            "search_strategy": "adaptive",
            "smart_optimization": True
        }
```

## 🧪 测试和调优

### 1. 运行测试脚本

```bash
# 基础功能测试
python test_enhanced_search.py

# 性能对比测试
python precision_comparison_test.py

# 快速验证
python quick_precision_test.py
```

### 2. 监控关键指标

```python
# 关注这些指标来评估效果
metrics_to_monitor = {
    "avg_score": "平均相关性分数",
    "content_diversity": "内容多样性",
    "response_time": "响应时间", 
    "fallback_rate": "回退策略触发率",
    "user_satisfaction": "用户满意度"
}
```

### 3. 调优建议

1. **如果结果太少**：
   - 降低 `score_threshold`
   - 使用 `broad` 策略
   - 启用 `enable_fallback`

2. **如果结果质量不高**：
   - 提高 `score_threshold`
   - 使用 `precise` 策略
   - 启用 `query_enhancement`

3. **如果响应太慢**：
   - 减少 `top_k`
   - 关闭 `query_enhancement`
   - 使用 `fast` 预设配置

## 🎉 总结

通过这些优化，文件检索功能现在具备了：

- 🧠 **智能化**: 自动优化参数和查询
- 🎯 **精确性**: 多策略提升匹配精度
- 🚀 **灵活性**: 丰富的配置选项
- 📊 **可观测**: 详细的质量指标
- 🔄 **鲁棒性**: 回退和容错机制

这些改进将显著提升用户的搜索体验和结果质量！
