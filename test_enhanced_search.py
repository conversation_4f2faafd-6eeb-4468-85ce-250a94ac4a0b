#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强文件检索功能测试

测试所有新增的优化功能
"""

import requests
import json
import time
from typing import Dict, List, Any


class EnhancedSearchTester:
    """增强搜索测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.test_results = []
    
    def test_basic_file_search(self):
        """测试基础文件检索"""
        print("🔍 测试基础文件检索...")
        
        test_cases = [
            {
                "name": "单文件精确搜索",
                "query": "API接口调用方法",
                "selected_files": ["新建 文本文档 (2).txt"],
                "expected": "高精确度结果"
            },
            {
                "name": "多文件综合搜索",
                "query": "交通违章处理流程",
                "selected_files": ["新建 文本文档 (2).txt", "道路交通.txt"],
                "expected": "多文件相关结果"
            }
        ]
        
        for case in test_cases:
            print(f"\n📝 {case['name']}")
            result = self._call_file_search_api(
                query=case['query'],
                selected_files=case['selected_files']
            )
            
            self._analyze_basic_result(result, case)
    
    def test_enhanced_features(self):
        """测试增强功能"""
        print("\n🚀 测试增强功能...")
        
        # 测试查询增强
        print("\n1️⃣ 测试查询增强")
        result1 = self._call_file_search_api(
            query="如何配置系统",
            selected_files=["新建 文本文档 (2).txt"],
            query_enhancement=True
        )
        self._analyze_enhancement_result(result1, "查询增强")
        
        # 测试智能参数优化
        print("\n2️⃣ 测试智能参数优化")
        result2 = self._call_file_search_api(
            query="API",  # 短查询
            selected_files=["新建 文本文档 (2).txt"],
            smart_optimization=True
        )
        self._analyze_optimization_result(result2, "短查询优化")
        
        # 测试搜索策略
        print("\n3️⃣ 测试搜索策略")
        strategies = ['precise', 'broad', 'adaptive', 'balanced']
        for strategy in strategies:
            result = self._call_file_search_api(
                query="文档编辑操作指南",
                selected_files=["新建 文本文档 (2).txt", "道路交通.txt"],
                search_strategy=strategy
            )
            self._analyze_strategy_result(result, strategy)
        
        # 测试回退策略
        print("\n4️⃣ 测试回退策略")
        result4 = self._call_file_search_api(
            query="非常特殊的不存在的内容",
            selected_files=["新建 文本文档 (2).txt"],
            enable_fallback=True,
            score_threshold=0.8  # 高阈值触发回退
        )
        self._analyze_fallback_result(result4)
    
    def test_performance_comparison(self):
        """测试性能对比"""
        print("\n⚡ 性能对比测试...")
        
        query = "系统配置文档说明"
        selected_files = ["新建 文本文档 (2).txt", "道路交通.txt"]
        
        # 原始检索
        start_time = time.time()
        result_basic = self._call_file_search_api(
            query=query,
            selected_files=selected_files,
            query_enhancement=False,
            smart_optimization=False
        )
        basic_time = time.time() - start_time
        
        # 增强检索
        start_time = time.time()
        result_enhanced = self._call_file_search_api(
            query=query,
            selected_files=selected_files,
            query_enhancement=True,
            smart_optimization=True
        )
        enhanced_time = time.time() - start_time
        
        self._compare_performance(result_basic, result_enhanced, basic_time, enhanced_time)
    
    def _call_file_search_api(self, query: str, selected_files: List[str], **kwargs) -> Dict[str, Any]:
        """调用文件检索API"""
        url = f"{self.base_url}/datasheet/filesearch/"
        
        payload = {
            'text': query,
            'database': ['default'],
            'collection': ['chzngk228'],
            'selected_files': selected_files,
            'top_k': kwargs.get('top_k', 10),
            'score_threshold': kwargs.get('score_threshold', 0.45),
            **kwargs
        }
        
        try:
            response = requests.post(url, json=payload, timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}: {response.text}'}
        except Exception as e:
            return {'error': str(e)}
    
    def _analyze_basic_result(self, result: Dict, test_case: Dict):
        """分析基础结果"""
        if 'error' in result:
            print(f"   ❌ 错误: {result['error']}")
            return
        
        data = result.get('data', {})
        results = data.get('results', [])
        
        print(f"   📊 结果数量: {len(results)}")
        print(f"   🎯 过滤表达式: {data.get('filter_expr', 'None')}")
        
        if results:
            avg_score = sum(r.get('metadata', {}).get('score', 0) for r in results) / len(results)
            print(f"   📈 平均分数: {avg_score:.3f}")
            
            # 显示最佳结果
            best_result = results[0]
            content = best_result.get('page_content', '')[:100] + '...'
            print(f"   🏆 最佳匹配: {content}")
    
    def _analyze_enhancement_result(self, result: Dict, feature_name: str):
        """分析增强功能结果"""
        if 'error' in result:
            print(f"   ❌ {feature_name}失败: {result['error']}")
            return
        
        data = result.get('data', {})
        print(f"   ✅ {feature_name}成功")
        print(f"   📊 结果数量: {len(data.get('results', []))}")
        
        # 检查是否有增强相关的元数据
        results = data.get('results', [])
        if results:
            enhanced_count = sum(1 for r in results 
                               if 'enhanced_query' in r.get('metadata', {}))
            print(f"   🔄 增强查询结果: {enhanced_count}/{len(results)}")
    
    def _analyze_optimization_result(self, result: Dict, optimization_type: str):
        """分析优化结果"""
        if 'error' in result:
            print(f"   ❌ {optimization_type}失败: {result['error']}")
            return
        
        data = result.get('data', {})
        search_params = data.get('search_params', {})
        
        print(f"   ✅ {optimization_type}成功")
        print(f"   ⚙️ 优化参数: top_k={search_params.get('top_k')}, "
              f"threshold={search_params.get('score_threshold')}")
        print(f"   🎛️ 是否优化: {search_params.get('optimized', False)}")
    
    def _analyze_strategy_result(self, result: Dict, strategy: str):
        """分析策略结果"""
        if 'error' in result:
            print(f"   ❌ {strategy}策略失败: {result['error']}")
            return
        
        data = result.get('data', {})
        results = data.get('results', [])
        quality_metrics = data.get('quality_metrics', {})
        
        print(f"   📋 {strategy}策略:")
        print(f"      结果数: {len(results)}")
        print(f"      平均分数: {quality_metrics.get('avg_score', 0):.3f}")
        print(f"      文件多样性: {quality_metrics.get('content_diversity', 0)}")
    
    def _analyze_fallback_result(self, result: Dict):
        """分析回退策略结果"""
        if 'error' in result:
            print(f"   ❌ 回退策略失败: {result['error']}")
            return
        
        data = result.get('data', {})
        results = data.get('results', [])
        
        print(f"   🔄 回退策略结果:")
        print(f"      最终结果数: {len(results)}")
        
        if results:
            print(f"      回退策略生效: 找到了结果")
        else:
            print(f"      回退策略无效: 仍无结果")
    
    def _compare_performance(self, basic_result: Dict, enhanced_result: Dict, 
                           basic_time: float, enhanced_time: float):
        """比较性能"""
        print(f"\n📊 性能对比:")
        print(f"   ⏱️ 基础检索时间: {basic_time:.2f}秒")
        print(f"   ⏱️ 增强检索时间: {enhanced_time:.2f}秒")
        print(f"   📈 时间增加: {((enhanced_time - basic_time) / basic_time * 100):.1f}%")
        
        # 结果质量对比
        basic_data = basic_result.get('data', {})
        enhanced_data = enhanced_result.get('data', {})
        
        basic_count = len(basic_data.get('results', []))
        enhanced_count = len(enhanced_data.get('results', []))
        
        print(f"   📊 基础结果数: {basic_count}")
        print(f"   📊 增强结果数: {enhanced_count}")
        
        if enhanced_count > basic_count:
            print(f"   ✅ 增强检索找到更多结果")
        elif enhanced_count == basic_count:
            print(f"   ➖ 结果数量相同")
        else:
            print(f"   ⚠️ 增强检索结果较少")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始增强文件检索功能测试\n")
        
        try:
            self.test_basic_file_search()
            self.test_enhanced_features()
            self.test_performance_comparison()
            
            print("\n✅ 所有测试完成!")
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现错误: {str(e)}")


def main():
    """主函数"""
    tester = EnhancedSearchTester()
    tester.run_all_tests()


if __name__ == '__main__':
    main()
