<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件检索示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .file-list {
            flex: 1;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .search-area {
            flex: 2;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .file-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .file-item:last-child {
            border-bottom: none;
        }
        .file-checkbox {
            margin-right: 10px;
        }
        .search-input {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .search-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .search-button:hover {
            background-color: #0056b3;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .result-item {
            margin-bottom: 15px;
            padding: 10px;
            background-color: white;
            border-radius: 3px;
            border-left: 4px solid #007bff;
        }
        .result-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .result-content {
            line-height: 1.5;
        }
        .loading {
            text-align: center;
            color: #666;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Milvus文件检索示例</h1>
    
    <div class="container">
        <!-- 文件列表 -->
        <div class="file-list">
            <h3>选择文件</h3>
            <div>
                <label>
                    <input type="checkbox" id="selectAll" class="file-checkbox"> 全选
                </label>
            </div>
            <hr>
            <div id="fileList">
                <!-- 文件列表将通过JavaScript动态加载 -->
            </div>
        </div>
        
        <!-- 搜索区域 -->
        <div class="search-area">
            <h3>搜索</h3>
            <input type="text" id="searchInput" class="search-input" placeholder="输入搜索内容...">
            
            <div style="margin-bottom: 10px;">
                <label>数据库: <input type="text" id="database" value="default" style="margin-left: 5px;"></label>
                <label style="margin-left: 15px;">集合: <input type="text" id="collection" value="default_coll" style="margin-left: 5px;"></label>
            </div>
            
            <div style="margin-bottom: 10px;">
                <label>返回数量: <input type="number" id="topK" value="10" min="1" max="50" style="margin-left: 5px; width: 60px;"></label>
                <label style="margin-left: 15px;">分数阈值: <input type="number" id="scoreThreshold" value="0.45" min="0" max="1" step="0.01" style="margin-left: 5px; width: 60px;"></label>
            </div>
            
            <button class="search-button" onclick="performSearch()">搜索</button>
            
            <div id="results" class="results" style="display: none;">
                <h4>搜索结果</h4>
                <div id="resultsContent"></div>
            </div>
        </div>
    </div>

    <script>
        // 模拟文件数据（实际应用中应该从API获取）
        const mockFiles = [
            { id: 1, name: "产品说明书.pdf", size: "2.3MB" },
            { id: 2, name: "用户手册.docx", size: "1.8MB" },
            { id: 3, name: "技术文档.txt", size: "0.5MB" },
            { id: 4, name: "FAQ常见问题.md", size: "0.3MB" },
            { id: 5, name: "API接口文档.pdf", size: "3.1MB" }
        ];

        // 初始化文件列表
        function initFileList() {
            const fileListContainer = document.getElementById('fileList');
            fileListContainer.innerHTML = '';
            
            mockFiles.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <input type="checkbox" class="file-checkbox" value="${file.name}" data-id="${file.id}">
                    <span>${file.name} (${file.size})</span>
                `;
                fileListContainer.appendChild(fileItem);
            });
        }

        // 全选功能
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.file-list .file-checkbox:not(#selectAll)');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // 执行搜索
        async function performSearch() {
            const searchText = document.getElementById('searchInput').value.trim();
            if (!searchText) {
                alert('请输入搜索内容');
                return;
            }

            // 获取选中的文件
            const selectedFiles = [];
            const selectedFileIds = [];
            const checkboxes = document.querySelectorAll('.file-list .file-checkbox:not(#selectAll):checked');
            
            checkboxes.forEach(checkbox => {
                selectedFiles.push(checkbox.value);
                selectedFileIds.push(parseInt(checkbox.dataset.id));
            });

            if (selectedFiles.length === 0) {
                alert('请至少选择一个文件');
                return;
            }

            // 准备搜索参数
            const searchParams = {
                text: searchText,
                database: [document.getElementById('database').value],
                collection: [document.getElementById('collection').value],
                selected_files: selectedFiles,
                selected_file_ids: selectedFileIds,
                top_k: parseInt(document.getElementById('topK').value),
                score_threshold: parseFloat(document.getElementById('scoreThreshold').value)
            };

            // 显示加载状态
            const resultsDiv = document.getElementById('results');
            const resultsContent = document.getElementById('resultsContent');
            resultsDiv.style.display = 'block';
            resultsContent.innerHTML = '<div class="loading">搜索中...</div>';

            try {
                // 发送搜索请求
                const response = await fetch('/datasheet/filesearch/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Authorization': 'Bearer YOUR_API_KEY', // 如果需要认证
                    },
                    body: JSON.stringify(searchParams)
                });

                const data = await response.json();

                if (response.ok) {
                    displayResults(data.data);
                } else {
                    throw new Error(data.error || '搜索失败');
                }
            } catch (error) {
                resultsContent.innerHTML = `<div class="error">搜索出错: ${error.message}</div>`;
            }
        }

        // 显示搜索结果
        function displayResults(data) {
            const resultsContent = document.getElementById('resultsContent');
            
            if (!data.results || data.results.length === 0) {
                resultsContent.innerHTML = '<div>未找到相关结果</div>';
                return;
            }

            let html = `<div>找到 ${data.total_results} 个结果</div><hr>`;
            
            data.results.forEach((result, index) => {
                const metadata = result.metadata || {};
                const score = metadata.score ? metadata.score.toFixed(3) : 'N/A';
                
                html += `
                    <div class="result-item">
                        <div class="result-meta">
                            文件: ${metadata.file_name || '未知'} | 
                            相关度: ${score} | 
                            结果 ${index + 1}
                        </div>
                        <div class="result-content">${result.page_content}</div>
                    </div>
                `;
            });
            
            resultsContent.innerHTML = html;
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initFileList();
        });
    </script>
</body>
</html>
