import asyncio
import re
import time
import json
from typing import Op<PERSON>, <PERSON>, Tu<PERSON>
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>rowserContext, Page
from bs4 import BeautifulSoup
from .webtype import ToutiaoContent

class ToutiaoExtractor:
    """今日头条内容提取器 - 基于 Playwright 优化版"""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.playwright = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._init_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def close(self):
        """关闭浏览器"""
        try:
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
        except Exception as e:
            print(f"关闭浏览器时出错: {e}")
        finally:
            self.browser = None
            self.context = None
            self.playwright = None
    
    async def _init_browser(self):
        """初始化浏览器"""
        if not self.browser:
            self.playwright = await async_playwright().start()
            
            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-infobars',
                    '--disable-extensions',
                    '--no-default-browser-check'
                ]
            )
            
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Referer': 'https://www.toutiao.com/',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Sec-Fetch-User': '?1',
                    'Upgrade-Insecure-Requests': '1'
                }
            )
            
            # 添加初始化脚本来隐藏webdriver特征
            await self.context.add_init_script('''
                // 隐藏webdriver特征
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // 模拟真实的chrome对象
                window.chrome = {
                    runtime: {},
                    loadTimes: function() {},
                    csi: function() {},
                    app: {}
                };
                
                // 模拟插件
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                // 模拟语言
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en'],
                });
            ''')
    
    def is_toutiao_url(self, url: str) -> bool:
        """判断是否为今日头条链接"""
        return any(domain in url for domain in ['toutiao.com', 't.tt', 'ixigua.com'])
    
    async def _simulate_user_session(self, page: Page):
        """模拟用户会话"""
        try:
            # 设置一些常见的用户状态cookie
            await page.context.add_cookies([
                {
                    'name': 'user_session',
                    'value': f'session_{int(time.time() * 1000)}',
                    'domain': '.toutiao.com',
                    'path': '/'
                },
                {
                    'name': 'tt_webid',
                    'value': f'webid_{int(time.time() * 1000)}',
                    'domain': '.toutiao.com',
                    'path': '/'
                },
                {
                    'name': 'MONITOR_WEB_ID',
                    'value': f'monitor_{int(time.time() * 1000)}',
                    'domain': '.toutiao.com',
                    'path': '/'
                }
            ])
            
            # 执行JavaScript模拟用户行为
            await page.evaluate('''
                () => {
                    try {
                        // 模拟用户行为
                        window.dispatchEvent(new Event('resize'));
                        document.dispatchEvent(new Event('DOMContentLoaded'));
                        
                        // 模拟滚动行为
                        window.scrollTo(0, 100);
                        
                        // 设置一些window属性
                        if (window) {
                            window.userId = 'user_' + Date.now();
                            window.deviceId = 'device_' + Date.now();
                        }
                        
                    } catch (e) {
                        console.log('模拟用户行为时出错:', e);
                    }
                }
            ''')
            
        except Exception as e:
            print(f"模拟用户会话时出错: {e}")
    
    async def get_content_info(self, url: str) -> Optional[ToutiaoContent]:
        """获取今日头条内容信息"""
        try:
            # 确保浏览器已初始化
            if not self.browser:
                await self._init_browser()
            
            page = await self.context.new_page()
            
            try:
                # 处理短链接
                if 't.tt' in url:
                    url = await self._resolve_short_url(page, url)
                
                print(f"访问链接: {url}")
                
                # 模拟用户会话
                await self._simulate_user_session(page)
                
                # 首先访问主页，模拟正常用户行为
                try:
                    await page.goto('https://www.toutiao.com/', wait_until='domcontentloaded', timeout=10000)
                    await page.wait_for_timeout(1000)
                except:
                    print("访问主页失败，直接访问目标页面")
                
                # 访问目标页面
                await page.goto(url, wait_until='networkidle', timeout=30000)
                
                # 等待页面动态内容加载
                await self._wait_for_content_load(page)
                
                # 随机等待时间，模拟真实用户行为
                import random
                wait_time = random.uniform(2000, 5000)
                await page.wait_for_timeout(int(wait_time))
                
                # 尝试多次获取数据，增加成功率
                js_data = await self._get_page_data_with_retry(page)
                
                # 获取页面HTML
                html_content = await page.content()
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # 验证是否获取到了页面标题
                page_title = soup.title.string if soup.title else "无标题"
                print(f"页面标题: {page_title}")
                
                # 判断内容类型
                content_type = self._detect_content_type(url, soup, html_content)
                print(f"检测到内容类型: {content_type}")
                
                # 将JS数据转换为字符串以便正则匹配
                js_data_str = json.dumps(js_data, ensure_ascii=False)
                html_text = html_content + js_data_str
                
                # 提取基本信息
                title = self._extract_title(soup, html_text)
                content = self._extract_content(soup, html_text, content_type)
                author = self._extract_author(soup, html_text)
                publish_time = self._extract_publish_time(soup, html_text)
                
                # 改进的交互数据提取
                read_count = await self._extract_read_count_dynamic(page, soup, html_text)
                comment_count = await self._extract_comment_count_dynamic(page, soup, html_text)
                like_count = await self._extract_like_count_dynamic(page, soup, html_text)
                
                cover_image = self._extract_cover_image(soup, html_text)
                
                # 如果是视频类型，提取视频信息
                video_url = ""
                duration = 0
                if content_type == 'video':
                    video_url, duration = self._extract_video_info(soup, html_text)
                
                return ToutiaoContent(
                    title=title,
                    content=content,
                    author=author,
                    publish_time=publish_time,
                    read_count=read_count,
                    comment_count=comment_count,
                    like_count=like_count,
                    content_type=content_type,
                    url=url,
                    cover_image=cover_image,
                    video_url=video_url,
                    duration=duration
                )
                
            finally:
                await page.close()
                
        except Exception as e:
            print(f"获取今日头条内容时出错: {e}")
            import traceback
            traceback.print_exc()
            return None
        finally:
            await self.close()
    
    async def _wait_for_content_load(self, page: Page):
        """等待页面内容加载完成"""
        try:
            # 等待常见的内容元素
            content_selectors = [
                'article',
                '.article-content',
                '.content',
                '.article-detail',
                '.video-content',
                '.article-body'
            ]
            
            # 尝试等待任一内容元素出现
            for selector in content_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=5000)
                    print(f"✓ 检测到内容元素: {selector}")
                    break
                except:
                    continue
            
            # 等待交互元素加载
            interaction_selectors = [
                '.interact-wrap',
                '.article-actions',
                '.interaction-bar',
                '.toolbar',
                '.article-footer'
            ]
            
            for selector in interaction_selectors:
                try:
                    await page.wait_for_selector(selector, timeout=3000)
                    print(f"✓ 检测到交互元素: {selector}")
                    break
                except:
                    continue
            
            # 额外等待JavaScript执行
            await page.wait_for_timeout(2000)
            
        except Exception as e:
            print(f"等待内容加载时出错: {e}")
    
    async def _get_page_data_with_retry(self, page: Page, max_retries: int = 3):
        """带重试机制的数据获取"""
        for attempt in range(max_retries):
            try:
                # 执行JavaScript获取动态数据
                js_data = await page.evaluate('''
                    () => {
                        // 尝试获取页面中的各种数据源
                        const data = {};
                        
                        // 获取window对象中的数据
                        if (window.__INITIAL_STATE__) {
                            data.initialState = window.__INITIAL_STATE__;
                        }
                        if (window.__SSR_HYDRATED_DATA__) {
                            data.ssrHydratedData = window.__SSR_HYDRATED_DATA__;
                        }
                        if (window.__ARTICLE_INFO__) {
                            data.articleInfo = window.__ARTICLE_INFO__;
                        }
                        if (window.__STATE__) {
                            data.state = window.__STATE__;
                        }
                        
                        // 获取所有script标签中的JSON数据
                        const scripts = document.querySelectorAll('script');
                        data.scriptData = [];
                        scripts.forEach(script => {
                            const text = script.textContent;
                            if (text && (text.includes('title') || text.includes('author') || text.includes('content'))) {
                                try {
                                    // 尝试解析JSON
                                    const jsonMatch = text.match(/({.*})/);
                                    if (jsonMatch) {
                                        const parsed = JSON.parse(jsonMatch[1]);
                                        data.scriptData.push(parsed);
                                    }
                                } catch (e) {
                                    // 如果不是JSON，保存原始文本
                                    data.scriptData.push(text.slice(0, 1000));
                                }
                            }
                        });
                        
                        // 获取meta标签信息
                        const metaTags = [];
                        document.querySelectorAll('meta').forEach(meta => {
                            const attrs = {};
                            for (let attr of meta.attributes) {
                                attrs[attr.name] = attr.value;
                            }
                            metaTags.push(attrs);
                        });
                        data.metaTags = metaTags;
                        
                        return data;
                    }
                ''')
                
                # 检查是否获取到了有效数据
                js_data_str = json.dumps(js_data, ensure_ascii=False)
                if any(key in js_data_str for key in ['title', 'author', 'content', 'article']):
                    print(f"✓ 成功获取到有效数据 (尝试 {attempt + 1}/{max_retries})")
                    return js_data
                elif attempt < max_retries - 1:
                    print(f"⚠ 数据不完整，尝试刷新 (尝试 {attempt + 1}/{max_retries})")
                    await page.reload(wait_until='networkidle')
                    await page.wait_for_timeout(2000)
                    continue
                
                return js_data
                
            except Exception as e:
                print(f"获取数据时出错 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    await page.wait_for_timeout(1000)
                    continue
                raise e
        
        return js_data
    
    async def _resolve_short_url(self, page: Page, short_url: str) -> str:
        """解析短链接"""
        try:
            await page.goto(short_url, wait_until='networkidle', timeout=10000)
            await page.wait_for_timeout(2000)
            final_url = page.url
            print(f"短链接解析: {short_url} -> {final_url}")
            return final_url
        except Exception as e:
            print(f"解析短链接失败: {e}")
            return short_url
    
    def _detect_content_type(self, url: str, soup: BeautifulSoup, html_text: str) -> str:
        """检测内容类型"""
        # 从URL判断
        if 'ixigua.com' in url or '/video/' in url:
            return 'video'
        
        # 从meta标签判断
        og_type = soup.find('meta', property='og:type')
        if og_type and og_type.get('content'):
            og_type_value = og_type['content'].lower()
            if 'video' in og_type_value:
                return 'video'
        
        # 从页面内容判断
        if any(keyword in html_text for keyword in ['video_url', 'play_url', 'video_duration']):
            return 'video'
        
        return 'article'
    
    def _extract_title(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取标题"""
        # 方法1: 从meta标签提取
        meta_selectors = [
            'meta[property="og:title"]',
            'meta[name="twitter:title"]',
            'meta[name="title"]'
        ]
        
        for selector in meta_selectors:
            meta_tag = soup.select_one(selector)
            if meta_tag and meta_tag.get('content'):
                title = meta_tag['content'].strip()
                # 移除网站名称后缀
                title = re.sub(r'\s*[-–—]\s*(今日头条|西瓜视频|头条)\s*$', '', title)
                if title:
                    return title
        
        # 方法2: 从HTML结构提取
        title_selectors = [
            'h1.article-title',
            'h1.title',
            '.article-content h1',
            '.video-title',
            'h1'
        ]
        
        for selector in title_selectors:
            element = soup.select_one(selector)
            if element and element.get_text().strip():
                return element.get_text().strip()
        
        # 方法3: 从page title提取
        if soup.title and soup.title.string:
            title = soup.title.string.strip()
            title = re.sub(r'\s*[-–—]\s*(今日头条|西瓜视频|头条)\s*$', '', title)
            if title:
                return title
        
        # 方法4: 从JSON数据提取
        title_patterns = [
            r'"title":\s*"([^"]*)"',
            r'"article_title":\s*"([^"]*)"',
            r'"video_title":\s*"([^"]*)"'
        ]
        
        for pattern in title_patterns:
            match = re.search(pattern, html_text)
            if match and match.group(1).strip():
                return match.group(1).strip()
        
        return ""
    
    def _extract_content(self, soup: BeautifulSoup, html_text: str, content_type: str) -> str:
        """提取内容"""
        if content_type == 'video':
            return self._extract_video_description(soup, html_text)
        else:
            return self._extract_article_content(soup, html_text)
    
    def _extract_article_content(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取文章内容 - 改进版本，专门处理span标签"""
        # 方法1: 从文章主体提取完整内容
        content_selectors = [
            '.article-content',
            '.content',
            '.article-body',
            'article',
            '.post-content',
            '.article-detail',
            '.detail-content'
        ]
        
        for selector in content_selectors:
            element = soup.select_one(selector)
            if element:
                # 创建副本并清理
                content_copy = soup.new_tag('div')
                content_copy.append(element.__copy__())
                
                # 移除不需要的元素
                for unwanted in content_copy.find_all(['script', 'style', 'nav', 'header', 'footer', 'aside']):
                    unwanted.decompose()
                
                # 特别处理span标签 - 提取所有span中的文本
                spans = content_copy.find_all('span')
                span_texts = []
                for span in spans:
                    text = span.get_text().strip()
                    if text and len(text) > 5:  # 过滤掉过短的文本
                        span_texts.append(text)
                
                # 获取所有文本内容
                all_text = content_copy.get_text().strip()
                
                # 如果有span内容，优先使用
                if span_texts:
                    content_text = ' '.join(span_texts)
                    if len(content_text) > 100:  # 确保内容足够长
                        return content_text
                
                # 否则使用完整文本
                if len(all_text) > 50:
                    return all_text
        
        # 方法2: 专门查找包含文章内容的段落
        paragraph_selectors = [
            '.article-content p',
            '.content p',
            '.article-body p',
            'article p',
            '.post-content p'
        ]
        
        for selector in paragraph_selectors:
            paragraphs = soup.select(selector)
            if paragraphs:
                paragraph_texts = []
                for p in paragraphs:
                    # 提取段落中的所有span文本
                    spans = p.find_all('span')
                    if spans:
                        for span in spans:
                            text = span.get_text().strip()
                            if text and len(text) > 10:
                                paragraph_texts.append(text)
                    else:
                        # 如果没有span，直接获取段落文本
                        text = p.get_text().strip()
                        if text and len(text) > 10:
                            paragraph_texts.append(text)
                
                if paragraph_texts:
                    return '\n'.join(paragraph_texts)
        
        # 方法3: 从meta description提取
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc and meta_desc.get('content'):
            content = meta_desc['content'].strip()
            if content and len(content) > 20:
                return content
        
        # 方法4: 从JSON数据提取
        content_patterns = [
            r'"content":\s*"([^"]*)"',
            r'"article_content":\s*"([^"]*)"',
            r'"article":\s*"([^"]*)"',
            r'"abstract":\s*"([^"]*)"'
        ]
        
        for pattern in content_patterns:
            match = re.search(pattern, html_text)
            if match and match.group(1).strip():
                return match.group(1).strip()
        
        return ""
    
    def _extract_video_description(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取视频描述"""
        # 方法1: 从meta description提取
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc and meta_desc.get('content'):
            return meta_desc['content'].strip()
        
        # 方法2: 从视频描述元素提取
        desc_selectors = [
            '.video-desc',
            '.description',
            '.video-description'
        ]
        
        for selector in desc_selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text().strip()
        
        # 方法3: 从JSON数据提取
        desc_patterns = [
            r'"description":\s*"([^"]*)"',
            r'"video_description":\s*"([^"]*)"',
            r'"summary":\s*"([^"]*)"'
        ]
        
        for pattern in desc_patterns:
            match = re.search(pattern, html_text)
            if match and match.group(1).strip():
                return match.group(1).strip()
        
        return ""
    
    def _extract_author(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取作者"""
        # 方法1: 从meta标签提取
        meta_author = soup.find('meta', attrs={'name': 'author'})
        if meta_author and meta_author.get('content'):
            return meta_author['content'].strip()
        
        # 方法2: 从HTML结构提取
        author_selectors = [
            '.article-meta .name',
            '.author-name',
            '.author',
            '.byline'
        ]
        
        for selector in author_selectors:
            element = soup.select_one(selector)
            if element:
                text = element.get_text().strip()
                if text and len(text) < 50:
                    return text
        
        # 方法3: 从JSON数据提取
        author_patterns = [
            r'"author":\s*"([^"]*)"',
            r'"author_name":\s*"([^"]*)"',
            r'"username":\s*"([^"]*)"',
            r'"nickname":\s*"([^"]*)"'
        ]
        
        for pattern in author_patterns:
            match = re.search(pattern, html_text)
            if match and match.group(1).strip():
                return match.group(1).strip()
        
        return ""
    
    def _extract_publish_time(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取发布时间"""
        # 方法1: 从meta标签提取
        time_meta = soup.find('meta', attrs={'property': 'article:published_time'})
        if time_meta and time_meta.get('content'):
            return time_meta['content'].strip()
        
        # 方法2: 从HTML结构提取
        time_selectors = [
            'time[datetime]',
            '.publish-time',
            '.time'
        ]
        
        for selector in time_selectors:
            element = soup.select_one(selector)
            if element:
                datetime_attr = element.get('datetime')
                if datetime_attr:
                    return datetime_attr
                text = element.get_text().strip()
                if text:
                    return text
        
        # 方法3: 从JSON数据提取
        time_patterns = [
            r'"publish_time":\s*"([^"]*)"',
            r'"create_time":\s*"([^"]*)"',
            r'"time":\s*"([^"]*)"',
            r'"date":\s*"([^"]*)"'
        ]
        
        for pattern in time_patterns:
            match = re.search(pattern, html_text)
            if match and match.group(1).strip():
                return match.group(1).strip()
        
        return ""
    
    async def _extract_read_count_dynamic(self, page: Page, soup: BeautifulSoup, html_text: str) -> str:
        """动态提取阅读数"""
        try:
            # 首先尝试从DOM中获取
            read_count = await page.evaluate('''
                () => {
                    const selectors = [
                        '.read-count',
                        '.views-count',
                        '.play-count',
                        '[data-type="read"]',
                        '.article-actions .count'
                    ];
                    
                    for (const selector of selectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            const text = element.textContent.trim();
                            const match = text.match(/(\d+)/);
                            if (match) {
                                return match[1];
                            }
                        }
                    }
                    
                    // 从页面中查找包含阅读数的元素
                    const elements = document.querySelectorAll('*');
                    for (const el of elements) {
                        const text = el.textContent;
                        if (text && (text.includes('阅读') || text.includes('播放'))) {
                            const match = text.match(/(\d+)/);
                            if (match) {
                                return match[1];
                            }
                        }
                    }
                    
                    return null;
                }
            ''')
            
            if read_count:
                return str(read_count)
            
        except Exception as e:
            print(f"动态提取阅读数失败: {e}")
        
        # 回退到静态提取
        return self._extract_read_count(soup, html_text)
    
    async def _extract_comment_count_dynamic(self, page: Page, soup: BeautifulSoup, html_text: str) -> str:
        """动态提取评论数"""
        try:
            # 首先尝试从DOM中获取
            comment_count = await page.evaluate('''
                () => {
                    const selectors = [
                        '.comment-count',
                        '.reply-count',
                        '[data-type="comment"]',
                        '.article-actions .comment .count',
                        '.interaction-bar .comment-count',
                        '.toolbar .comment .count'
                    ];
                    
                    for (const selector of selectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            const text = element.textContent.trim();
                            const match = text.match(/(\d+)/);
                            if (match) {
                                return match[1];
                            }
                        }
                    }
                    
                    // 从页面中查找包含评论数的元素
                    const elements = document.querySelectorAll('*');
                    for (const el of elements) {
                        const text = el.textContent;
                        if (text && (text.includes('评论') || text.includes('回复'))) {
                            const match = text.match(/(\d+)/);
                            if (match) {
                                return match[1];
                            }
                        }
                    }
                    
                    return null;
                }
            ''')
            
            if comment_count:
                return str(comment_count)
            
        except Exception as e:
            print(f"动态提取评论数失败: {e}")
        
        # 回退到静态提取
        return self._extract_comment_count(soup, html_text)
    
    def _extract_comment_count(self, soup: BeautifulSoup, html_text: str) -> str:
        """静态提取评论数"""
        # 方法1: 从HTML结构提取
        comment_selectors = [
            '.comment-count',
            '.reply-count',
            '[data-type="comment"]',
            '.article-actions .comment .count',
            '.interaction-bar .comment-count'
        ]
        
        for selector in comment_selectors:
            element = soup.select_one(selector)
            if element:
                text = element.get_text().strip()
                match = re.search(r'(\d+)', text)
                if match:
                    return match.group(1)
        
        # 方法2: 从JSON数据提取
        comment_patterns = [
            r'"comment_count":\s*(\d+)',
            r'"reply_count":\s*(\d+)',
            r'"comments":\s*(\d+)',
            r'"comment_num":\s*(\d+)'
        ]
        
        for pattern in comment_patterns:
            match = re.search(pattern, html_text)
            if match:
                return match.group(1)
        
        return "0"
    
    async def _extract_like_count_dynamic(self, page: Page, soup: BeautifulSoup, html_text: str) -> str:
        """动态提取点赞数"""
        try:
            # 首先尝试从DOM中获取
            like_count = await page.evaluate('''
                () => {
                    const selectors = [
                        '.like-count',
                        '.praise-count',
                        '.thumbs-up-count',
                        '[data-type="like"]',
                        '[data-type="praise"]',
                        '.article-actions .like .count',
                        '.interaction-bar .like-count',
                        '.toolbar .like .count'
                    ];
                    
                    for (const selector of selectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            const text = element.textContent.trim();
                            const match = text.match(/(\d+)/);
                            if (match) {
                                return match[1];
                            }
                        }
                    }
                    
                    // 从页面中查找包含点赞数的元素
                    const elements = document.querySelectorAll('*');
                    for (const el of elements) {
                        const text = el.textContent;
                        if (text && (text.includes('赞') || text.includes('点赞') || text.includes('👍'))) {
                            const match = text.match(/(\d+)/);
                            if (match) {
                                return match[1];
                            }
                        }
                    }
                    
                    return null;
                }
            ''')
            
            if like_count:
                return str(like_count)
            
        except Exception as e:
            print(f"动态提取点赞数失败: {e}")
        
        # 回退到静态提取
        return self._extract_like_count(soup, html_text)
    
    def _extract_like_count(self, soup: BeautifulSoup, html_text: str) -> str:
        """静态提取点赞数"""
        # 方法1: 从HTML结构提取
        like_selectors = [
            '.like-count',
            '.digg-icon',
            '.praise-count',
            '.thumbs-up-count',
            '[data-type="like"]',
            '[data-type="praise"]',
            '.article-actions .like .count',
            '.interaction-bar .like-count'
        ]
        
        for selector in like_selectors:
            element = soup.select_one(selector)
            if element:
                text = element.get_text().strip()
                match = re.search(r'(\d+)', text)
                if match:
                    return match.group(1)
        
        # 方法2: 从JSON数据提取
        like_patterns = [
            r'"like_count":\s*(\d+)',
            r'"praise_count":\s*(\d+)',
            r'"thumbs_up":\s*(\d+)',
            r'"likes":\s*(\d+)',
            r'"digg_count":\s*(\d+)'
        ]
        
        for pattern in like_patterns:
            match = re.search(pattern, html_text)
            if match:
                return match.group(1)
        
        return "0"
    
    def _extract_cover_image(self, soup: BeautifulSoup, html_text: str) -> str:
        """提取封面图片"""
        # 方法1: 从meta标签提取
        meta_selectors = [
            'meta[property="og:image"]',
            'meta[name="twitter:image"]',
            'meta[name="image"]'
        ]
        
        for selector in meta_selectors:
            meta_tag = soup.select_one(selector)
            if meta_tag and meta_tag.get('content'):
                image_url = meta_tag['content'].strip()
                if image_url and image_url.startswith('http'):
                    return image_url
        
        # 方法2: 从HTML结构提取
        image_selectors = [
            '.article-cover img',
            '.cover-image img',
            '.article-header img',
            '.article-content img:first-child',
            '.video-cover img',
            '.thumbnail img'
        ]
        
        for selector in image_selectors:
            img_element = soup.select_one(selector)
            if img_element:
                src = img_element.get('src') or img_element.get('data-src')
                if src and src.startswith('http'):
                    return src
        
        # 方法3: 从JSON数据提取
        image_patterns = [
            r'"cover_image":\s*"([^"]*)"',
            r'"image_url":\s*"([^"]*)"',
            r'"thumb_url":\s*"([^"]*)"',
            r'"cover_url":\s*"([^"]*)"',
            r'"thumbnail":\s*"([^"]*)"',
            r'"image_uri":\s*"([^"]*)"'
        ]
        
        for pattern in image_patterns:
            match = re.search(pattern, html_text)
            if match and match.group(1).strip():
                image_url = match.group(1).strip()
                if image_url.startswith('http'):
                    return image_url
                # 如果是相对路径，尝试构建完整URL
                elif image_url.startswith('/'):
                    return f"https://p3.pstatp.com{image_url}"
        
        # 方法4: 从script标签中的JSON数据提取
        script_tags = soup.find_all('script', type='application/ld+json')
        for script in script_tags:
            try:
                data = json.loads(script.string)
                if isinstance(data, dict):
                    # 查找图片相关字段
                    image_fields = ['image', 'thumbnail', 'thumbnailUrl', 'logo']
                    for field in image_fields:
                        if field in data:
                            image_data = data[field]
                            if isinstance(image_data, str) and image_data.startswith('http'):
                                return image_data
                            elif isinstance(image_data, dict) and 'url' in image_data:
                                return image_data['url']
                            elif isinstance(image_data, list) and len(image_data) > 0:
                                first_image = image_data[0]
                                if isinstance(first_image, str) and first_image.startswith('http'):
                                    return first_image
                                elif isinstance(first_image, dict) and 'url' in first_image:
                                    return first_image['url']
            except json.JSONDecodeError:
                continue
        
        return ""
    
    def _extract_video_info(self, soup: BeautifulSoup, html_text: str) -> Tuple[str, int]:
        """提取视频信息"""
        video_url = ""
        duration = 0
        
        # 方法1: 从video标签提取
        video_element = soup.find('video')
        if video_element:
            video_url = video_element.get('src') or video_element.get('data-src')
            if not video_url:
                # 尝试从source标签获取
                source_element = video_element.find('source')
                if source_element:
                    video_url = source_element.get('src')
        
        # 方法2: 从JSON数据提取视频URL
        video_url_patterns = [
            r'"video_url":\s*"([^"]*)"',
            r'"play_url":\s*"([^"]*)"',
            r'"video_uri":\s*"([^"]*)"',
            r'"video_src":\s*"([^"]*)"',
            r'"media_url":\s*"([^"]*)"'
        ]
        
        for pattern in video_url_patterns:
            match = re.search(pattern, html_text)
            if match and match.group(1).strip():
                video_url = match.group(1).strip()
                break
        
        # 方法3: 从复杂的JSON结构中提取
        video_complex_patterns = [
            r'"video":\s*{[^}]*"play_url":\s*"([^"]*)"',
            r'"video_info":\s*{[^}]*"video_url":\s*"([^"]*)"',
            r'"media":\s*{[^}]*"url":\s*"([^"]*)"'
        ]
        
        for pattern in video_complex_patterns:
            match = re.search(pattern, html_text)
            if match and match.group(1).strip():
                video_url = match.group(1).strip()
                break
        
        # 提取视频时长
        duration_patterns = [
            r'"duration":\s*(\d+)',
            r'"video_duration":\s*(\d+)',
            r'"length":\s*(\d+)',
            r'"time":\s*(\d+)'
        ]
        
        for pattern in duration_patterns:
            match = re.search(pattern, html_text)
            if match:
                duration = int(match.group(1))
                break
        
        # 从HTML结构提取时长
        duration_selectors = [
            '.video-duration',
            '.duration',
            '.time-duration',
            '[data-duration]'
        ]
        
        for selector in duration_selectors:
            element = soup.select_one(selector)
            if element:
                duration_attr = element.get('data-duration')
                if duration_attr and duration_attr.isdigit():
                    duration = int(duration_attr)
                    break
                
                duration_text = element.get_text().strip()
                # 解析时长格式如 "01:30" 或 "1:30"
                time_match = re.search(r'(\d+):(\d+)', duration_text)
                if time_match:
                    minutes = int(time_match.group(1))
                    seconds = int(time_match.group(2))
                    duration = minutes * 60 + seconds
                    break
        
        # 如果提取到的视频URL是相对路径，构建完整URL
        if video_url and not video_url.startswith('http'):
            if video_url.startswith('/'):
                video_url = f"https://v.ixigua.com{video_url}"
            else:
                video_url = f"https://v.ixigua.com/{video_url}"
        
        return video_url, duration
    
    def _extract_read_count(self, soup: BeautifulSoup, html_text: str) -> str:
        """静态提取阅读数"""
        # 方法1: 从HTML结构提取
        read_selectors = [
            '.read-count',
            '.views-count',
            '.play-count',
            '[data-type="read"]',
            '.article-actions .count'
        ]
        
        for selector in read_selectors:
            element = soup.select_one(selector)
            if element:
                text = element.get_text().strip()
                match = re.search(r'(\d+)', text)
                if match:
                    return match.group(1)
        
        # 方法2: 从JSON数据提取
        read_patterns = [
            r'"read_count":\s*(\d+)',
            r'"view_count":\s*(\d+)',
            r'"play_count":\s*(\d+)',
            r'"pv":\s*(\d+)',
            r'"views":\s*(\d+)'
        ]
        
        for pattern in read_patterns:
            match = re.search(pattern, html_text)
            if match:
                return match.group(1)
        
        return "0"