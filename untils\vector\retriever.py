from langchain_core.vectorstores import VectorStore
from typing import (
    Any,
    ClassVar,
    Collection,
    Dict,
    List,
)

from langchain_core.documents import Document
from langchain_core.language_models import BaseLanguageModel
from pydantic import Field, model_validator
from langchain_core.retrievers import BaseRetriever
from loguru import logger

class MultiVectorStoreRetriever(BaseRetriever):
    """Base Retriever class for VectorStore."""

    vectorstore_list: List[VectorStore]
    """VectorStore to use for retrieval."""
    search_type: str = "similarity"
    """Type of search to perform. Defaults to "similarity"."""
    search_kwargs: dict = Field(default_factory=dict)
    """Keyword arguments to pass to the search function."""
    allowed_search_types: ClassVar[Collection[str]] = (
        "similarity",
        "similarity_score_threshold",
        "mmr",
    )
    
    max_token_limit: int = 2000
    # llm: BaseLanguageModel

    class Config:
        """Configuration for this pydantic object."""

        arbitrary_types_allowed = True

    @model_validator(mode="after")
    def validate_search_type(cls, values) -> Dict:
        """Validate search type."""
        search_type = getattr(values,"search_type",None)
        if search_type not in cls.allowed_search_types:
            raise ValueError(
                f"search_type of {search_type} not allowed. Valid values are: "
                f"{cls.allowed_search_types}"
            )
        if search_type == "similarity_score_threshold":
            search_kwargs = getattr(values,"search_kwargs",{})
            score_threshold = search_kwargs.get("score_threshold")
            if (score_threshold is None) or (not isinstance(score_threshold, float)):
                raise ValueError(
                    "`score_threshold` is not specified with a float value(0~1) "
                    "in `search_kwargs`."
                )
        return values

    def _get_relevant_documents(
        self, query: str, *, run_manager
    ) -> List[Document]:
        docs = []
        if self.search_type == "similarity":
            for vectorstore in self.vectorstore_list:
                docs.extend(vectorstore.similarity_search(query, **self.search_kwargs))
        elif self.search_type == "similarity_score_threshold":
            for vectorstore in self.vectorstore_list:
                docs_and_similarities = (
                    vectorstore.similarity_search_with_relevance_scores(
                        query, **self.search_kwargs
                    )
                )# 提取文档和分数
                for doc, score in docs_and_similarities:
                    logger.info(f"Document: {doc.metadata}, Score: {score}")
                    # 将分数存储到文档的 metadata 中
                    doc.metadata["score"] = score
                    docs.append(doc)  # 如果需要保留文档，可以继续添加到 docs 列表中
        elif self.search_type == "mmr":
            for vectorstore in self.vectorstore_list:
                docs.extend(
                    vectorstore.max_marginal_relevance_search(
                        query, **self.search_kwargs
                    )
                )
        else:
            raise ValueError(f"search_type of {self.search_type} not allowed.")
        # logger.info(docs)
        # docs = self._filter_documents_with_token_limit(docs)
        # logger.info(docs)
        
        logger.info(f"LangChain VectorStore Search: {query}, Found {len(docs)} relevant documents.")
        return docs
    
    def _filter_documents_with_token_limit(self, docs: List[Document]) -> List[Document]:
        """Filter documents with token limit."""

        # Count the number of tokens in the buffer
        def get_doc_num_tokens(docs: List[Document]) -> int:
            return sum(self.llm.get_num_tokens(doc.page_content) for doc in docs)

        curr_buffer_length = get_doc_num_tokens(docs)
        if curr_buffer_length > self.max_token_limit:
            pruned_memory = []
            while curr_buffer_length > self.max_token_limit:
                pruned_memory.append(docs.pop(0))
                curr_buffer_length = get_doc_num_tokens(docs)
        if not docs:
            logger.warning(
                "All documents were pruned from the buffer. This may be because "
                "the max_token_limit is too low."
            )
        return docs


    async def _aget_relevant_documents(
        self, query: str, *, run_manager
    ) -> List[Document]:
        raise NotImplementedError

    def add_documents(self, documents: List[Document], **kwargs: Any) -> List[str]:
        """Add documents to vectorstore."""
        raise NotImplementedError

    async def aadd_documents(
        self, documents: List[Document], **kwargs: Any
    ) -> List[str]:
        """Add documents to vectorstore."""
        raise NotImplementedError