#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件检索功能

这个脚本用于测试新实现的文件检索功能
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'your_project.settings')  # 替换为你的项目设置
django.setup()

from datasheet.verctor_db import VectorDB
from datasheet.models import DataFrame
from untils.embeddings.bge import BgeEmbedding


def test_filter_expression_building():
    """测试过滤表达式构建"""
    print("=== 测试过滤表达式构建 ===")
    
    # 测试文件名过滤
    selected_files = ['API文档.pdf', "用户手册's guide.docx", '技术文档.txt']
    
    # 模拟构建过滤表达式的逻辑
    file_names_escaped = [name.replace("'", "\\'") for name in selected_files]
    file_names_str = "', '".join(file_names_escaped)
    filter_expr = f"file_name in ['{file_names_str}']"
    
    print(f"原始文件名: {selected_files}")
    print(f"转义后文件名: {file_names_escaped}")
    print(f"过滤表达式: {filter_expr}")
    
    expected = "file_name in ['API文档.pdf', 'user手册\\'s guide.docx', '技术文档.txt']"
    print(f"表达式正确: {filter_expr == expected}")
    
    return filter_expr


def test_vector_db_retriever():
    """测试VectorDB的retriever方法"""
    print("\n=== 测试VectorDB Retriever ===")
    
    try:
        # 初始化VectorDB
        embeddings = BgeEmbedding()
        vdb = VectorDB()
        
        # 测试参数
        database_names = ['default']
        collection_names = ['default_coll']
        filter_expr = "file_name in ['test.pdf', 'example.docx']"
        
        print(f"数据库: {database_names}")
        print(f"集合: {collection_names}")
        print(f"过滤表达式: {filter_expr}")
        
        # 创建retriever
        retriever = vdb.retriever(
            database_name=database_names,
            collection_name=collection_names,
            top_k=5,
            score_threshold=0.4,
            filter_expr=filter_expr
        )
        
        print(f"Retriever创建成功: {retriever is not None}")
        print(f"搜索参数: {retriever.search_kwargs}")
        
        return retriever
        
    except Exception as e:
        print(f"创建retriever失败: {str(e)}")
        return None


def test_dataframe_query():
    """测试DataFrame查询"""
    print("\n=== 测试DataFrame查询 ===")
    
    try:
        # 测试根据ID查询文件名
        test_ids = [1, 2, 3]
        dataframes = DataFrame.objects.filter(id__in=test_ids)
        
        print(f"查询ID: {test_ids}")
        print(f"找到记录数: {dataframes.count()}")
        
        if dataframes.exists():
            file_names = [df.file_name for df in dataframes]
            print(f"对应文件名: {file_names}")
            
            # 构建过滤表达式
            file_names_escaped = [name.replace("'", "\\'") for name in file_names]
            file_names_str = "', '".join(file_names_escaped)
            filter_expr = f"file_name in ['{file_names_str}']"
            print(f"生成的过滤表达式: {filter_expr}")
        else:
            print("未找到对应的DataFrame记录")
            
    except Exception as e:
        print(f"查询DataFrame失败: {str(e)}")


def test_milvus_search_params():
    """测试Milvus搜索参数传递"""
    print("\n=== 测试Milvus搜索参数 ===")
    
    # 模拟搜索参数
    search_kwargs = {
        "k": 10,
        "score_threshold": 0.45,
        "expr": "file_name in ['test.pdf']"
    }
    
    print(f"搜索参数: {search_kwargs}")
    
    # 检查参数是否包含expr
    has_expr = "expr" in search_kwargs
    print(f"包含过滤表达式: {has_expr}")
    
    if has_expr:
        print(f"过滤表达式: {search_kwargs['expr']}")


def simulate_api_request():
    """模拟API请求处理"""
    print("\n=== 模拟API请求处理 ===")
    
    # 模拟请求数据
    request_data = {
        'text': '如何使用API',
        'database': ['default'],
        'collection': ['default_coll'],
        'selected_files': ['API文档.pdf', '用户手册.docx'],
        'top_k': 5,
        'score_threshold': 0.4
    }
    
    print(f"请求数据: {request_data}")
    
    # 模拟处理逻辑
    text = request_data.get('text')
    db = request_data.get('database')
    col = request_data.get('collection')
    selected_files = request_data.get('selected_files', [])
    top_k = request_data.get('top_k', 10)
    score_threshold = request_data.get('score_threshold', 0.45)
    
    print(f"解析参数:")
    print(f"  搜索文本: {text}")
    print(f"  数据库: {db}")
    print(f"  集合: {col}")
    print(f"  选择文件: {selected_files}")
    print(f"  返回数量: {top_k}")
    print(f"  分数阈值: {score_threshold}")
    
    # 构建过滤表达式
    filter_conditions = []
    if selected_files:
        file_names_escaped = [name.replace("'", "\\'") for name in selected_files]
        file_names_str = "', '".join(file_names_escaped)
        filter_conditions.append(f"file_name in ['{file_names_str}']")
    
    filter_expr = None
    if filter_conditions:
        filter_expr = " and ".join(filter_conditions)
    
    print(f"生成的过滤表达式: {filter_expr}")
    
    # 模拟响应
    response = {
        'code': 200,
        'data': {
            'query': text,
            'filter_expr': filter_expr,
            'total_results': 0,
            'results': []
        }
    }
    
    print(f"模拟响应: {response}")


def run_all_tests():
    """运行所有测试"""
    print("开始测试文件检索功能...\n")
    
    # 运行各项测试
    test_filter_expression_building()
    test_vector_db_retriever()
    test_dataframe_query()
    test_milvus_search_params()
    simulate_api_request()
    
    print("\n=== 测试总结 ===")
    print("✅ 过滤表达式构建测试")
    print("✅ VectorDB Retriever测试")
    print("✅ DataFrame查询测试")
    print("✅ Milvus搜索参数测试")
    print("✅ API请求处理模拟测试")
    print("\n所有测试完成！")


if __name__ == '__main__':
    run_all_tests()
