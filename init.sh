#!/bin/bash

LOCK_FILE=migrate.lock

if [ ! -f $LOCK_FILE ]; then
    # 检查是否需要迁移
    echo "checking for migrations..."
    python manage.py makemigrations --check --dry-run
    if [ $? -eq 1 ]; then
        echo "Migrations needed, applying..."
        # 生成迁移文件
        python manage.py makemigrations
        # 应用迁移
        python manage.py migrate
        touch $LOCK_FILE
    fi
fi

# 启动Django应用
python manage.py runserver 0.0.0.0:4060 &
# 启动 Gunicorn 服务器
# echo "Starting Gunicorn..."
# gunicorn --bind 0.0.0.0:8088 --workers 2 --threads 2 --timeout 1200 --max-requests 100 --limit-request-field_size 0 --limit-request-line 0 related.wsgi:application &

# # 确保 Gunicorn 完全启动
# echo "Waiting for Gun<PERSON> to start..."
sleep 10
# 启动Celery工作进程
# celery -A related worker --pool solo -l info
# 启动 Celery worker
echo "Starting Celery..."
celery -A related worker --pool=prefork --concurrency=50 --loglevel=info --max-tasks-per-child=100
# 运行传递的命令
exec "$@"
