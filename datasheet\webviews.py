from rest_framework.views import APIView
from untils.webextract.webMain import ContentExtractor
import time,asyncio,os
from django.http import JsonResponse
from django.core.files import File
import json
from datasheet.sheetserializers import  FrameSerializer
# 获取当前文件的根目录
# BASE_PATH = os.path.dirname(os.path.abspath(__file__))
BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

async def video_detail(url,username):
    """主函数示例"""
    # 初始化提取器（启用Whisper）
    extractor = ContentExtractor(whisper_model="base", enable_whisper=True)
    
    print(f"\n{'='*60}",BASE_PATH)
    print(f"正在提取: {url}")
    print('='*60)
    
    content =await extractor.extract_content(
        url, 
        include_comments=True,
        include_subtitles=True,
        use_whisper_if_no_subtitle=True,  # 启用语音转文字
        max_comments=20
    )
    
    if 'error' in content:
        print(f"错误: {content['error']}")
        return
    
    # 显示基本信息
    if content['video_info']:
        video = content['video_info']
        print(f"标题: {video.title}")
        print(f"作者: {video.uploader}")
        print(f"播放量: {video.view_count}")
        print(f"点赞数: {video.like_count}")
        print(f"时长: {video.duration}秒")
    
    # 显示字幕信息
    if content['subtitles']:
        print(f"\n字幕信息:")
        for i, subtitle in enumerate(content['subtitles']):
            print(f"{i+1}. {subtitle.language} ({subtitle.source})")
            preview = subtitle.content[:100] + "..." if len(subtitle.content) > 100 else subtitle.content
            print(f"   预览: {preview}")
    else:
        print("未找到字幕")
    
    # 显示评论数量
    print(f"评论数量: {len(content['comments'])}")
    
    target_folder = os.path.join(BASE_PATH,'media', 'data', 'web', username, 'youtube' )
    
    # 保存完整数据
    filename = f"{target_folder}/{int(time.time())}_{content['platform']}.json"
    extractor.save_to_file(content, filename)
    
    # 保存纯文本版本
    if not os.path.exists(target_folder):
            os.makedirs(target_folder)
    text_filename = f"{target_folder}/{int(time.time())}_{content['platform']}.txt"
    all_text = extractor.extract_all_text(content)
    with open(text_filename, 'w', encoding='utf-8') as f:
        f.write(all_text)
    print(f"纯文本已保存到: {text_filename}")
    return text_filename

def wechat(url,username):
    """微信公众号使用示例"""
    # 初始化提取器
    extractor = ContentExtractor(enable_whisper=False)  # 微信文章不需要Whisper
    
    # 提取内容
    result = extractor.extract_content(
        url, 
        include_comments=False,    # 微信文章没有评论
        include_subtitles=False,   # 微信文章没有字幕
        use_whisper_if_no_subtitle=False
    )
    
    if 'error' in result:
        print(f"提取失败: {result['error']}")
        return
    
    target_folder = os.path.join(BASE_PATH,'media', 'data', 'web', username, 'youtube' )
    # 获取纯文本
    text_content = extractor.extract_all_text(result)
    print("=== 提取的文本内容 ===")
    print(text_content)
    
    # 保存到文件
    filename = f"{target_folder}/{int(time.time())}_{result['platform']}.json"
    extractor.save_to_file(result, filename)
    
    # 保存纯文本
    text_filename = f"{target_folder}/{int(time.time())}_{result['platform']}.txt"
    with open(text_filename, 'w', encoding='utf-8') as f:
        f.write(text_content)
    print(f"纯文本已保存到: {text_filename}")
    return text_filename


class WebExtractor(APIView):
    def post(self, request):
        urls = [
        "https://www.youtube.com/watch?v=E3z-T7d2jEI",   # YouTube视频示例
        "https://www.youtube.com/watch?v=E3z-T7d2jEI"]  # B站视频示例]
        user = self.request.user
        uid_sheet = self.request.POST.get('uid_sheet')

        # 生成一串随机字符串作为文件夹名
        timestr = str(time.time()).replace(".", "")
        folder_name = self.request.POST.get('cache_id',timestr)
        RAW_FOLDER = os.getenv("TEM_FOLDER", "data/tempy")# 目标目录
        raw_dir = os.path.join(RAW_FOLDER, str(user.username), folder_name)
        if not os.path.exists(raw_dir):
            os.makedirs(raw_dir)
        async def process_urls():
            url_list = [urls] if isinstance(urls, str) else urls
            results=[]
            print(urls)
            try:
                for url in url_list:
                      
                    platform = ContentExtractor(enable_whisper=False).detect_platform(url)
                    if platform == 'wechat':
                        filepath= await wechat(url,self.request.user.username)
                        results.append(filepath)
                    elif platform in ['youtube', 'bilibili']:
                        filepath= await video_detail(url,self.request.user.username)
                        results.append(filepath)
                return results
            except Exception as e:
                return JsonResponse({'error': str(e)})
            
        try:
            # 使用sync_to_async包装器
            file_paths = asyncio.run(process_urls())
            
            for file_path in file_paths:
                print(file_path,'fffffffff')
            #     # 创建 Django File 对象
                with open(file_path, 'rb') as f:
                    file_name = os.path.basename(file_path)
                    file_size = os.path.getsize(file_path)
                    django_file = File(f, name=file_name)
                    # 获取文件信息
                    rename = str(time.time()).replace(".", "") + os.path.splitext(file_name)[1]
                                        
                    data = {
                        'cache_id':folder_name,
                        'file': django_file,
                        'file_size': file_size,     # 获取文件大小
                        'file_name': file_name,     # 获取文件名称
                        'uid_sheet': uid_sheet,     # 跟知识库绑定
                        'rename':rename,
                        'desc':'',
                        'uuid_user':    str(user.uuid_user),
                    }
                    # 创建一个新的serializer实例，将文件数据传递给它
                    new_serializer = FrameSerializer(data=data)
                    if new_serializer.is_valid():
                        instance = new_serializer.save()

                        # 文件复制操作
                        target_file_path = os.path.join(raw_dir, file_name)
                        import shutil
                        shutil.copy2(file_path, target_file_path)
                        
            return JsonResponse({'target_folder': folder_name})
        except Exception as e:
            return JsonResponse({'error': str(e)})