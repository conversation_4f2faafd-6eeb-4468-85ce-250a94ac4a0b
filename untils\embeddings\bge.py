import os
from loguru import logger
from langchain_huggingface  import HuggingFaceEmbeddings


class BgeEmbedding:
    def __init__(self) -> None:        
        self.embedding_device = os.getenv("EMBEDDING_DEVICE", "cpu")
        self.embedding_model = os.environ.get("EMBEDDING_MODEL", "bge-zh-large")
        self.embedding_batch_size = int(os.getenv("EMBEDDING_BATCH_SIZE", 512))
        self.emb = HuggingFaceEmbeddings(
            model_name=self.embedding_model,
            model_kwargs={"device": self.embedding_device},
            encode_kwargs={"normalize_embeddings": True},
        )
        # logger.info(f"Using embedding model: {self.embedding_model}")
