"""
@File    :   exception.py
@Time    :   2023/09/19 16:05:53
<AUTHOR>   weny
@Version :   1.0
@Desc    :   异常处理
"""
from django.utils.translation import gettext as _
import traceback
ERROR_MAP={
    "Invalid pk \"33\" - object does not exist.":"输入的参数值不存在",
    "A user with that username already exists.":"用户信息已存在",
    'this field is required.':'缺少必须参数',
    'No Members matches the given query':'未查询到当前数据',
    'DataSheet matching query does not exist':'当前数据不存在',
    'Authentication credentials were not provided.':'权限验证失败'
}

'''自定义异常处理'''
class ExceptException:
    def extract_error_message(exception):
        '''从异常中提取错误信息'''
        message=[]
        details= exception.detail

        for field_errors in details.values():
            for error in field_errors:
                # print(next((value for key,value in ERROR_MAP.items() if str(error)[-20:] in key),'操作错误'))
                # print(ERROR_MAP.get(str(error),'操作错误'))
                # message.append( _(str(error)) )
                message.append(next((value for key,value in ERROR_MAP.items() if str(error)[-20:] in key),'操作错误'))
        return message
    
    

    def handle_exception(e):
        """
        处理异常并封装异常信息
        :param e: 异常对象
        :return: 包含异常信息的字典
        """
        exc_type = type(e).__name__
        exc_message = str(e)
        exc_traceback = traceback.format_exc()  # 设置编码为 UTF-8
        encoded_traceback = exc_traceback.encode('utf-8')  # 手动编码为 UTF-8

        exception_details = {
            'exception_type': exc_type,
            'exception_message': exc_message,
            'exception_traceback': encoded_traceback.decode('utf-8')  # 解码为字符串

        }

        return exception_details
