# Generated by Django 5.1.7 on 2025-03-20 01:17

import datasheet.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='DataFrame',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('available', models.CharField(choices=[('1', '可用'), ('2', '未上传'), ('3', '上传中'), ('4', '失败')], default='2', max_length=16, null=True, verbose_name='文件状态')),
                ('sql_available', models.CharField(choices=[('1', '可用'), ('2', '未上传'), ('3', '上传中'), ('4', '失败')], default='4', max_length=16, null=True, verbose_name='切片状态')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('file_size', models.PositiveBigIntegerField(null=True, verbose_name='文件大小')),
                ('file_name', models.CharField(max_length=1024, null=True, verbose_name='文件名')),
                ('rename', models.CharField(default='', max_length=1024, null=True, verbose_name='重命名文件')),
                ('cache_id', models.CharField(default='', max_length=1024, null=True, verbose_name='缓存ID')),
                ('file', models.FileField(max_length=1024, upload_to=datasheet.models.file_dir)),
                ('sector', models.CharField(blank=True, default='法律', max_length=1024, null=True, verbose_name='知识库类型')),
                ('desc', models.TextField(blank=True, default='', null=True, verbose_name='文件描述')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'db_table': 'ai_dataframe',
            },
        ),
        migrations.CreateModel(
            name='DataSheet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('sheet_name', models.CharField(max_length=64, verbose_name='数据名称')),
                ('sheet_label', models.CharField(default='这个人很懒什么都没说', max_length=255, null=True, verbose_name='标签')),
                ('sheet_model', models.CharField(default='embedding', max_length=128, verbose_name='索引模型')),
                ('sheet_avatar_addr', models.CharField(default='暂无头像', max_length=1024, null=True, verbose_name='头像')),
                ('sheet_level', models.IntegerField(default=2, verbose_name='知识库层级')),
                ('sheet_type', models.CharField(choices=[('private', 'Private'), ('public', 'Public'), ('person', 'person'), ('company', 'company'), ('laws', 'laws')], default='private', max_length=7, verbose_name='类型（私有和公共）')),
                ('sector_name', models.CharField(blank=True, default=None, max_length=255, null=True, verbose_name='知识库分类名称')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('password', models.CharField(default='123456', max_length=255, verbose_name='知识库访问密码')),
                ('username', models.CharField(blank='', default='', max_length=20, null='', verbose_name='用户姓名')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'db_table': 'ai_datasheet',
            },
        ),
        migrations.CreateModel(
            name='Page',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('folder_id', models.CharField(max_length=1024, null=True, verbose_name='文本ID')),
                ('page_content', models.TextField(blank=True, default='', null=True, verbose_name='文本内容')),
                ('file_name', models.CharField(max_length=1024, null=True, verbose_name='文件名称')),
                ('file_path', models.CharField(max_length=1024, null=True, verbose_name='文件名称')),
            ],
            options={
                'db_table': 'ai_datadoc',
            },
        ),
    ]
