from django.db import models
import uuid
# Create your models here.
from django.contrib.auth.models import AbstractUser,PermissionsMixin,BaseUserManager
# import hashlib
from django.contrib.auth.hashers import make_password
from django.conf import settings
from decimal import Decimal


def generate_custom_id():
    # 使用UUID生成一个随机字符串，然后进行自定义的处理以符合您的格式要求
    # 这里简单地使用UUID的hex格式，然后根据需要调整长度和大小写
    return uuid.uuid4().hex[:24].lower()
class Members(AbstractUser,PermissionsMixin):
    """用户"""
    '''
    账号,密码
    真实姓名,所在企业,所在部门,职称,设备ID,已用token数,剩余token数,创建时间,注册时间,修改时间,最后登录,是否可用,是否可用ChatGPT,是否已注册
    '''

    MEMBER_STATUS = {
        ('0', '正常'),
        ('1', '锁定'),
        ('2', '删除'),
    }
    ROLE_STATUS = {
        ('0', '超级管理员'),
        ('1', '管理员'),
        ('2', '普通用户'),
        ('3', '新用户'),
    }
    AVAILABLE = (
        ('0', '否'),
        ('1', '是'),
    )
    password = models.CharField(max_length=255, verbose_name='登录密码')
    real_name = models.CharField(max_length=20, verbose_name='成员姓名',default='',blank='',null='')
    nick = models.CharField(default='',max_length=20, verbose_name='昵称',blank='',null='')
    phone = models.CharField(max_length=20, verbose_name='联系电话',default='',blank='',null='')
    status = models.CharField(max_length=5, choices=MEMBER_STATUS, verbose_name='用户状态',default=1)
    roles = models.CharField(max_length=5, choices=ROLE_STATUS, verbose_name='用户权限',default=3,blank=True,null=True)
    provider = models.CharField(max_length=5,  verbose_name='用户类型',default='local')
    company_id = models.CharField(max_length=255,default='',null=True,blank=True)
    user_id = models.CharField(max_length=255,default='',null=True,blank=True)

    user_available = models.BooleanField(verbose_name='账户是否可用', default=False)
    gpt_available = models.BooleanField( verbose_name='GPT是否可用', default=False)
    #用户余额
    access_token = models.DecimalField(max_digits=15, decimal_places=5, default=580, verbose_name='Authing的AccessToken')
    # id_token = models.CharField(max_length=1024, blank=True, verbose_name='Authing的IdToken')
    # authing_id = models.CharField(max_length=128, blank=True, verbose_name='Authing唯一标识码')
    uuid_user = models.UUIDField(default=uuid.uuid4,editable=False, blank=True, unique=True, verbose_name='用户唯一标识码')

    custom_id = models.CharField(max_length=24, default=generate_custom_id, unique=True)
  
    is_active = models.BooleanField(default=True,verbose_name='是否要激活')
    is_staff = models.BooleanField(default=True,verbose_name='是否激活员工')

    avatar =  models.ImageField(upload_to='data/avatars/', null=True, blank=True, verbose_name='头像')

    openID = models.CharField(unique=True,null=True, blank=True, max_length=200, verbose_name='用户微信唯一凭证')
    tmp_scene = models.CharField(null=True, blank=True, max_length=100, verbose_name='登录临时凭证')
    
    departments = models.ManyToManyField('self', related_name='departments_members', symmetrical=False,blank=True)
    depart_name = models.CharField(default='',max_length=20, verbose_name='部门名称',blank='',null='')
    
    # 移除first_name和last_name字段
    first_name = None
    last_name = None
    
    objects = BaseUserManager()
    USERNAME_FIELD='username'
    
    class Meta(AbstractUser.Meta):
        # 后台管理系统中的名称
        verbose_name_plural = '成员'
        db_table = 'ai_members'
        swappable = 'AUTH_USER_MODEL'

    def __str__(self):
        return f'id:{self.id},uuid_user:{self.uuid_user},username:{self.username},real_name:{self.real_name},nick:{self.nick},phone:{self.phone},gpt_available:{self.gpt_available},access_token:{self.access_token}'

    def to_tokens(self):
        # 800*3000 = 2400000
        return self.access_token*int(settings.RATES)
    
    def update_balance(self,use):
        m = self.access_token -Decimal(use)
        # print('**********************************************',self.access_token,m)
        self.access_token = m
        self.save()  # 保存更改到数据库
        return self.access_token
    
    def to_json(self):
        return {
            'id':self.id,
            'username':self.username,
            'real_name':self.real_name,
            'nick':self.nick,
            'phone':self.phone,
            'uuid_user':self.uuid_user,
            'gpt_available':self.gpt_available,
            'access_token':self.access_token,
            # 'com_uid':self.com_uid
        }
    
    # def save(self,*args,**kwargs):
    #     # self.password = hashlib.sha1(self.password.encode('utf-8')).hexdigest()#用此种方式authenticate登录验证无法解密
    #     if self.pk:  # 如果对象已经存在于数据库
    #         orig = Members.objects.get(pk=self.pk)
    #         if self.password != orig.password:  # 只有当密码改变时才重新哈希
    #             self.password = make_password(self.password)
    #     else:
    #         self.password = make_password(self.password)
    #     super(Members,self).save(*args,**kwargs)
    
    
# 记录是否由前端点击进入后端

from django.utils import timezone
class LoginLog(models.Model):
    AVAILABLE = (
        ('0', '否'),
        ('1', '是'),
    )
    username=models.CharField(null=True, blank=True, max_length=200, verbose_name='用户名')
    status = models.CharField(max_length=5, choices=AVAILABLE, verbose_name='登录状态',default=1)
    create_time = models.DateTimeField(default=timezone.now, verbose_name='登录时间')
    class Meta:
        db_table = 'ai_login_Log'