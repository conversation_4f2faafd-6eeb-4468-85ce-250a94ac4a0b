"""
@File    :   parser.py
@Time    :   2023/09/19 16:28:36
<AUTHOR>   Tank<PERSON>ee
@Version :   1.0
@Desc    :   Parser class
"""

import os
import time

from loguru import logger

from untils.parsers.pdf import PdfParser
from untils.parsers.ppt import <PERSON><PERSON><PERSON><PERSON><PERSON>, PptxParser
from untils.parsers.word import <PERSON><PERSON>ars<PERSON>, DocxParser
from untils.parsers.excel import XlsxParser
from untils.common import get_all_files, write_json
from django.utils.text import get_valid_filename

import chardet
from django.core.cache import cache
class DocumentParser:
    parse_exts = [".pdf", ".docx",".doc", ".pptx",".xlsx", ".xls"]
    read_exts = [".txt", ".md"]
    parser_map = {
        ".pdf": PdfParser(),
        ".docx": DocxParser(),
        ".doc": DocParser(),
        ".pptx": PptxParser(),
        ".xlsx": XlsxParser(),
        ".xls": XlsxParser(),
        ".ppt": PptParser(),
    }

    def __init__(self) -> None:
        SYSTEM_PARSE_FOLDER = os.getenv("SYSTEM_PARSE_FOLDER", "data/parsed")
        self.parse_dir = SYSTEM_PARSE_FOLDER
        
        if not os.path.exists(self.parse_dir):
            os.makedirs(self.parse_dir)
            logger.info(f"Create parse dir: {self.parse_dir}")

    def parse(self, target,username,is_dir = True, callback=None):
        files=[]
        folder = ''
        if is_dir:
            if not os.path.exists(target):
                raise FileNotFoundError("Target folder not found")
            elif os.path.isfile(target):
                raise FileNotFoundError("Target folder is a file")
            
            _,folder = os.path.split(target)

            files = get_all_files(target)
        else:
            files= [target]
            path_parts = target.split(os.sep)  # os.sep 会根据当前操作系统使用正确的路径分隔符
            folder = path_parts[-2]
            
        # 生成一串随机字符串作为文件夹名
        folder_name = str(time.time()).replace(".", "")

        parsed_results = []   
        from untils.untils import MixinUntils
        cache_data = MixinUntils.get_frame_info(folder)
        if cache_data is None :
            raise FileNotFoundError("文件解析失败:上传信息已过期")
        
        logger.warning(cache_data)
        cache_map = {cdata['file_name']:cdata for cdata in cache_data}
        for idx, file in enumerate(files):
            ext = os.path.splitext(file)[1]
            file_name = os.path.basename(file)
            file_path=os.path.join('media/data/raw/'+username+'/', get_valid_filename(file_name))
            meta_info = dict(file_path=file_path, file_name=file_name,id = cache_map[file_name].get('id'))
            if ext in self.parse_exts:
                file_content = self.parse_file(file)
                print(file)
                payload = dict(meta=meta_info, content=file_content)
                parsed_results.append(payload)
            elif ext in self.read_exts:
                file_content = self.read_file(file)
                payload = dict(meta=meta_info, content=file_content)
                parsed_results.append(payload)
            else:
                logger.info(f"Skip file: {file}")
            if callback:
                callback(idx, len(files))

        logger.info(f"Already parsed {len(parsed_results)} files")
        saved_folder_dir = os.path.join(self.parse_dir, folder_name)
        self.save_files(parsed_results, saved_folder_dir)

        return saved_folder_dir
    
    def detect_encoding(self,file_path):
        with open(file_path, 'rb') as file:
            raw_data = file.read()
        result = chardet.detect(raw_data)
        return result['encoding']

    def read_file(self, file_path):
        file_content = ""# 先检测文件编码
        detected_encoding = self.detect_encoding(file_path)
        encodings = [detected_encoding,'utf-8', 'gbk', 'latin1', 'ascii']  # 列出你想尝试的编码
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    file_content= file.read()  # 如果成功，返回文件内容
                    
                    return file_content
            except UnicodeDecodeError:
                logger.error(f'Parsing fails, try another way{file_path}')
                continue  # 如果出现解码错误，尝试下一个编码
        raise ValueError(f"所有尝试的编码都失败了，无法读取文件: {file_path}")


    def parse_file(self, file_path):
        ext = os.path.splitext(file_path)[1]
        parser = self.parser_map.get(ext)

        file_content = parser.parse(file_path)

        return file_content

    def save_files(self, parsed_results, saved_folder_dir):
        file_cnt = 1
        if not os.path.exists(saved_folder_dir):
            os.makedirs(saved_folder_dir)
            # logger.info(f"Create save dir: {saved_folder_dir}")
        for result in parsed_results:
            saved_path = os.path.join(saved_folder_dir, f"{file_cnt}.json")
            write_json(saved_path, result)
            # logger.info(f"Save parsed file to: {saved_path}")
            file_cnt += 1
