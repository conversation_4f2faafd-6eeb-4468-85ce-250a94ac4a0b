from rest_framework import serializers
from members.models import Members,LoginLog

class UserSetSerializer(serializers.ModelSerializer):

    class Meta:
        model = Members
        fields = "__all__"
        # fields = ["id",        "username",        "real_name",        "nick",        "phone",        "gpt_available",        "access_token"]
        extra_kwargs = {'password': {'write_only': True}}

class UserGetSerializer(serializers.ModelSerializer):

    # employees = RolesDepartments()

    class Meta:
        model = Members
        # fields = "__all__"
        fields = ["id",'is_superuser' ,'is_staff','roles', 'user_id','email', 'is_active'  , 'uuid_user', "username",   'avatar', 'date_joined',    "real_name",        "nick",        "phone",        "gpt_available",        "access_token"]
        extra_kwargs = {'password': {'write_only': True}}
class UserRolesSerializer(serializers.ModelSerializer):

    class Meta:
        model = Members
        # fields = "__all__"
        fields = ["id",'username' ,'roles']
    
class UserLoginSerializer(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField(write_only=True)
class UserLoginLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = LoginLog
        fields = "__all__"

class PasswordChangeSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)

class PasswordCodeSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True)
    code = serializers.CharField(required=True)
    

class MemberAvatarSerializer(serializers.ModelSerializer):
    class Meta:
        model = Members
        fields = ['avatar']
