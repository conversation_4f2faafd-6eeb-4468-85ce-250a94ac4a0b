# Milvus文件检索功能使用指南

## 概述

本功能允许你在Milvus向量检索时根据勾选的特定文件进行过滤搜索，而不是搜索整个知识库。这样可以提高搜索的精确度和相关性。

## 功能特点

- ✅ 支持根据文件名进行过滤检索
- ✅ 支持根据文件ID进行过滤检索
- ✅ 支持多文件同时选择
- ✅ 兼容现有的向量检索参数
- ✅ 支持分数阈值过滤
- ✅ 提供详细的搜索结果元数据

## API接口

### 新增接口：`/datasheet/filesearch/`

**请求方法**: POST

**请求参数**:

```json
{
    "text": "搜索文本",
    "database": ["数据库名称"],
    "collection": ["集合名称"],
    "selected_files": ["文件名1.pdf", "文件名2.docx"],  // 可选
    "selected_file_ids": [1, 2, 3],                    // 可选
    "uid_sheet": 123,                                  // 可选
    "top_k": 10,                                       // 可选，默认10
    "score_threshold": 0.45                            // 可选，默认0.45
}
```

**参数说明**:

- `text`: 必需，搜索的文本内容
- `database`: 必需，数据库名称列表
- `collection`: 必需，集合名称列表
- `selected_files`: 可选，勾选的文件名列表
- `selected_file_ids`: 可选，勾选的文件ID列表（DataFrame表的ID）
- `uid_sheet`: 可选，知识库ID，用于额外过滤
- `top_k`: 可选，返回结果数量，默认10
- `score_threshold`: 可选，分数阈值，默认0.45

**响应格式**:

```json
{
    "code": 200,
    "data": {
        "results": [
            {
                "page_content": "文档内容片段",
                "metadata": {
                    "file_name": "文件名.pdf",
                    "file_path": "/path/to/file",
                    "score": 0.85,
                    "id": "文档片段ID"
                }
            }
        ],
        "query": "搜索文本",
        "filter_expr": "file_name in ['文件名1.pdf', '文件名2.docx']",
        "total_results": 5
    }
}
```

## 使用方法

### 方法1：通过文件名过滤

```python
import requests

# 搜索特定文件中的内容
response = requests.post('http://localhost:8000/datasheet/filesearch/', json={
    'text': '如何使用API',
    'database': ['default'],
    'collection': ['default_coll'],
    'selected_files': ['API文档.pdf', '用户手册.docx'],
    'top_k': 5,
    'score_threshold': 0.4
})

result = response.json()
```

### 方法2：通过文件ID过滤

```python
# 根据DataFrame ID搜索
response = requests.post('http://localhost:8000/datasheet/filesearch/', json={
    'text': '产品功能介绍',
    'database': ['default'],
    'collection': ['default_coll'],
    'selected_file_ids': [1, 3, 5],
    'top_k': 8
})
```

### 方法3：前端JavaScript调用

```javascript
async function searchFiles() {
    const response = await fetch('/datasheet/filesearch/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            text: '搜索内容',
            database: ['default'],
            collection: ['default_coll'],
            selected_files: ['文件1.pdf', '文件2.docx'],
            top_k: 10
        })
    });
    
    const data = await response.json();
    console.log(data);
}
```

## 实现原理

### 1. 过滤表达式构建

系统会根据选择的文件构建Milvus过滤表达式：

```python
# 文件名过滤
filter_expr = "file_name in ['文件1.pdf', '文件2.docx']"

# 文件ID过滤（先查询DataFrame获取文件名）
dataframes = DataFrame.objects.filter(id__in=selected_file_ids)
file_names = [df.file_name for df in dataframes]
filter_expr = f"file_name in ['{file_names_str}']"
```

### 2. 向量检索流程

1. 接收搜索请求和文件选择参数
2. 构建Milvus过滤表达式
3. 调用VectorDB.retriever()方法，传入过滤表达式
4. MultiVectorStoreRetriever执行检索，应用过滤条件
5. 返回过滤后的搜索结果

### 3. 核心代码修改

**VectorDB.retriever()方法**:
```python
def retriever(self, database_name, collection_name, top_k=10, 
              score_threshold=0.45, max_token_limit=512, filter_expr=None):
    # ... 现有代码 ...
    
    search_kwargs = {"k": top_k, "score_threshold": score_threshold}
    if filter_expr:
        search_kwargs["expr"] = filter_expr
    
    # ... 创建retriever ...
```

**Milvus类新增方法**:
```python
def similarity_search_with_relevance_scores(self, query, k=4, expr=None, **kwargs):
    # 支持过滤表达式的相关性搜索
    results_with_scores = self.similarity_search_with_score(
        query=query, k=k, expr=expr, **kwargs
    )
    return [(doc, 1 - distance) for doc, distance in results_with_scores]
```

## 前端集成示例

### HTML结构

```html
<!-- 文件选择区域 -->
<div class="file-list">
    <h3>选择文件</h3>
    <div id="fileList">
        <!-- 动态生成文件复选框 -->
    </div>
</div>

<!-- 搜索区域 -->
<div class="search-area">
    <input type="text" id="searchInput" placeholder="输入搜索内容...">
    <button onclick="performSearch()">搜索</button>
    <div id="results"></div>
</div>
```

### JavaScript逻辑

```javascript
function performSearch() {
    // 获取选中的文件
    const selectedFiles = [];
    const checkboxes = document.querySelectorAll('.file-checkbox:checked');
    checkboxes.forEach(cb => selectedFiles.push(cb.value));
    
    // 执行搜索
    fetch('/datasheet/filesearch/', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            text: document.getElementById('searchInput').value,
            database: ['default'],
            collection: ['default_coll'],
            selected_files: selectedFiles
        })
    }).then(response => response.json())
      .then(data => displayResults(data));
}
```

## 注意事项

1. **文件名匹配**: 确保传入的文件名与Milvus中存储的file_name字段完全匹配
2. **特殊字符处理**: 文件名中的单引号会被自动转义
3. **性能考虑**: 选择的文件数量会影响过滤表达式的复杂度
4. **错误处理**: API会返回详细的错误信息，便于调试

## 故障排除

### 常见问题

1. **搜索结果为空**
   - 检查文件名是否正确
   - 确认文件已被正确索引到Milvus
   - 调整score_threshold参数

2. **过滤表达式错误**
   - 检查文件名中是否包含特殊字符
   - 确认数据库和集合名称正确

3. **API调用失败**
   - 检查API密钥是否正确
   - 确认请求格式符合要求

### 调试技巧

1. 查看返回的`filter_expr`字段，确认过滤表达式是否正确
2. 使用较低的`score_threshold`进行测试
3. 先用少量文件测试，再扩展到更多文件

## 扩展功能

未来可以考虑添加的功能：

- 支持文件类型过滤（如只搜索PDF文件）
- 支持文件大小范围过滤
- 支持文件创建时间范围过滤
- 支持文件标签过滤
- 支持正则表达式文件名匹配
