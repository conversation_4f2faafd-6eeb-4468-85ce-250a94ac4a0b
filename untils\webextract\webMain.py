from .urlYoutubeBilili import B<PERSON><PERSON>iExtractor, YouTubeExtractor,  WhisperTranscriber
from .urlWechat import WeChatExtractor
# from testurldouyin import DouyinExtractor
from .urlXiaohongshu import XiaohongshuExtractor
from .urlToutiao import <PERSON>utiaoExtractor
from typing import Dict, Any
import time
import json
import asyncio

class ContentExtractor:
    """统一内容提取器"""
    
    def __init__(self, whisper_model: str = "base", enable_whisper: bool = True):
        self.bilibili = BilibiliExtractor()
        self.youtube = YouTubeExtractor()
        self.wechat = WeChatExtractor()  
        # self.douyin = DouyinExtractor()
        self.toutiao = ToutiaoExtractor()
        self.xiaohongshu = XiaohongshuExtractor()
        self.whisper = WhisperTranscriber(whisper_model) if enable_whisper else None
    
    def detect_platform(self, url: str) -> str:
        """检测URL平台"""
        if self.bilibili.is_bilibili_url(url):
            return 'bilibili'
        elif self.youtube.is_youtube_url(url):
            return 'youtube'
        elif self.wechat.is_wechat_url(url):  # 添加这行
            return 'wechat'   
        elif self.xiaohongshu.is_xiaohongshu_url(url):  # 添加这行
            return 'xiaohongshu'
        elif self.toutiao.is_toutiao_url(url):  # 添加这行
            return 'toutiao'
        # elif self.douyin.is_douyin_url(url):  # 添加这行
        #     return 'douyin'
        else:
            return 'unknown'
    
    async def  extract_content(self, url: str, include_comments: bool = True, 
                       include_subtitles: bool = True, use_whisper_if_no_subtitle: bool = True,
                       max_comments: int = 50) -> Dict[str, Any]:
        """提取视频内容"""
        platform = self.detect_platform(url)
        
        if platform == 'unknown':
            return {'error': '不支持的平台'}
        
        result = {
            'platform': platform,
            'url': url,
            'video_info': None,
            'article_info': None,  # 添加这行
            'comments': [],
            'subtitles': [],
            'transcription': None
        }
        
        try:
            if platform == 'bilibili':
                # 获取视频信息
                result['video_info'] = self.bilibili.get_video_info(url)
                
                # 获取评论
                if include_comments:
                    result['comments'] = self.bilibili.get_comments(url, max_comments)
                
                # 获取字幕
                if include_subtitles:
                    result['subtitles'] = self.bilibili.get_subtitles(url)       
            elif platform == 'youtube':
                # 获取视频信息
                result['video_info'] = self.youtube.get_video_info(url)
                
                # 获取字幕
                if include_subtitles:
                    result['subtitles'] = self.youtube.get_subtitles(url)
                
                # YouTube评论需要额外的API密钥
                if include_comments:
                    result['comments'] = []
            elif platform == 'wechat':  # 添加微信处理
                # 获取文章信息
                result['article_info'] = self.wechat.get_article_info(url)
                
                # 微信公众号文章没有评论和字幕功能
                result['comments'] = []
                result['subtitles'] = []
            elif platform == 'xiaohongshu':  # 添加小红书处理
                res = await self.xiaohongshu.get_content_info(url)
                result['content_type'] = res.content_type
                if res.content_type=='video':
                    result['video_info'] = res
                else:
                    result['article_info'] = res
                    
            elif platform == 'toutiao':  # 添加今日头条处理
                res =await self.toutiao.get_content_info(url)   
                result['content_type'] = res.content_type             
                if res.content_type=='video':
                    result['video_info'] = res
                else:
                    result['article_info'] = res
                    
            # elif platform == 'douyin':  # 添加抖音处理
            #     res = self.douyin.get_content_info(url)
            #     if res.content_type=='video':
            #         result['video_info'] = res
            #     else:
            #         result['article_info'] = res
                

            # 如果没有字幕且启用了Whisper，则进行语音转文字
            if (use_whisper_if_no_subtitle and 
                not result['subtitles'] and 
                self.whisper and 
                result['video_info']):
                
                print("没找到字幕，正在使用Whisper进行语音识别...")
                transcription = self.whisper.transcribe_video(url, platform)
                if transcription:
                    result['transcription'] = transcription
                    result['subtitles'] = [transcription]  # 也添加到字幕列表中
            
            return result
            
        except Exception as e:
            return {'error': f'提取内容时出错: {e}'}
    
    def save_to_file(self, content: Dict[str, Any], filename: str):
        """保存内容到文件"""
        try:
            # 转换dataclass对象为字典
            def convert_dataclass(obj):
                if hasattr(obj, '__dict__'):
                    return obj.__dict__
                return str(obj)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(content, f, ensure_ascii=False, indent=2, default=convert_dataclass)
            print(f"内容已保存到: {filename}")
        except Exception as e:
            print(f"保存文件时出错: {e}")
    
    def extract_all_text(self, content: Dict[str, Any]) -> str:
        """提取所有文本内容并组合"""
        all_text = []
        
        # 视频信息
        if content.get('video_info') is not None:
            video = content['video_info']
            all_text.append(f"标题: {video.title}")
            if content.get('platform') == 'xiaohongshu':
                all_text.append(f"内容: {video.content}")
                all_text.append(f"作者: {video.author}")
                all_text.append(f"发布时间: {video.publish_time}")
                all_text.append(f"标签: {video.tags}")
            elif content.get('platform') == 'toutiao':               
                # all_text.append(f"内容: {video.content}")
                all_text.append(f"作者: {video.author}")
                all_text.append(f"发布时间: {video.publish_time}")
            else:
                all_text.append(f"描述: {video.description}")
                all_text.append(f"作者: {video.uploader}")
            all_text.append("")
        
        #  微信文章信息（添加这部分）
        if content.get('article_info') is not None:
            article = content['article_info']
            all_text.append(f"标题: {article.title}")
            
            if content.get('platform') == 'wechat':
                all_text.append(f"公众号: {article.account_name}")
            all_text.append(f"作者: {article.author}")
            if content.get('platform') == 'xiaohongshu':
                all_text.append(f"标签: {article.tags}")
                all_text.append(f"图片: {article.images}")
            
            # elif content.get('platform') == 'toutiao':               
            #     all_text.append(f"内容: {article.content}")
            #     all_text.append(f"作者: {article.author}")
            all_text.append(f"发布时间: {article.publish_time}")
            all_text.append("")
            all_text.append("=== 文章内容 ===")
            all_text.append(article.content)
            all_text.append("")
            
        # 字幕内容
        if content.get('subtitles'):
            all_text.append("=== 字幕内容 ===")
            for subtitle in content['subtitles']:
                all_text.append(f"[{subtitle.language}] ({subtitle.source})")
                all_text.append(subtitle.content)
                all_text.append("")
        
        # 评论内容
        if content.get('comments'):
            all_text.append("=== 评论内容 ===")
            for comment in content['comments']:
                all_text.append(f"{comment['author']}: {comment['content']}")
            all_text.append("")
        
        return '\n'.join(all_text)




# async def other(url):
#     """主函数示例"""
#     # 初始化提取器（启用Whisper）
#     extractor = ContentExtractor(whisper_model="base", enable_whisper=True)
    
#     print(f"\n{'='*60}")
#     print(f"正在提取: {url}")
#     print('='*60)
    
#     content =await extractor.extract_content(
#         url, 
#         include_comments=True,
#         include_subtitles=True,
#         use_whisper_if_no_subtitle=True,  # 启用语音转文字
#         max_comments=20
#     )
    
#     if 'error' in content:
#         print(f"错误: {content['error']}")
#         return
#     print(content)