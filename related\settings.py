"""
Django settings for related project.

Generated by 'django-admin startproject' using Django 5.0.3.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
# SECRET_KEY = 'django-insecure-)^nts*moq^c7y=57dyrt&r@hioe&81o@oazl$spq^0sm6nv4zc'
SECRET_KEY = 'django-insecure-vo&zuyf!3^4y53bxik@bwdz_r9%nxci8#2qk!^shso+00u2s76'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'datasheet',
    'members',
]
AUTH_USER_MODEL = 'members.Members'
APPEND_SLASH=False
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'related.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'related.wsgi.application'
from datetime import timedelta
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=4),#生命周期为60分钟
    'SLIDING_TOKEN_REFRESH_LIFETIME': timedelta(days=30),#这意味着使用这个refresh token在1天内可以获取新的access token
    'ROTATE_REFRESH_TOKENS': False,#刷新时不会为每次刷新操作创建新的refresh token
    'ALGORITHM': 'HS256',#加密算法采用HS256对token进行签名
    'SIGNING_KEY': SECRET_KEY, #用于对token进行签名的密钥，默认使用Django的SECRET_KEY。
    'VERIFYING_KEY': None,
    'AUTH_HEADER_TYPES': ('Bearer',),#指定认证头部类型为“Bearer”。
    'USER_ID_FIELD': 'user_id', #用户模型中表示用户ID的字段，默认为id。
    'USER_ID_CLAIM': 'id',#在JWT的负载中表示用户ID的声明。
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),#使用的token类。untils.refreshToken.CustomRefreshToken
    'TOKEN_TYPE_CLAIM': None,# 在JWT的负载中表示token类型的声明。'token_type'
    'JTI_CLAIM': None,  # 如果Express的token中没有jti字段
}
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'untils.expressJWTAuthentication.ExpressJWTAuthentication',
    ),
}

# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases
import os
from dotenv import load_dotenv
load_dotenv()
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.environ['SQL_DATABASE'],
        'USER': os.environ['SQL_USER'],
        'PASSWORD': os.environ['SQL_PASSWORD'],
        'HOST': os.environ['SQL_HOST'],
        'PORT': os.environ['SQL_PORT'],
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'", # Optional: helpful for strict SQL mode
        }
    }
    # 'default': {
    #     'ENGINE': 'django.db.backends.sqlite3',
    #     'NAME': BASE_DIR / 'db.sqlite3',
    # }
}


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ['CACHE_REDIS_URL'],  # 根据你的 Redis 服务器配置
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_URL = 'static/'

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Celery Configuration Options
CELERY_TIMEZONE = "Asia/Shanghai"
CELERY_TASK_TRACK_STARTED = True
# CELERY_TASK_TIME_LIMIT = 30 * 60

# Celery settings

# CELERY_BROKER_URL = 'redis://************:6699/0'
# CELERY_BROKER_URL = 'redis://127.0.0.1:6680/1'

#: Only add pickle to this list if your broker is secured
#: from unwanted access (see userguide/security.html)
CELERY_ACCEPT_CONTENT = ['json']
# CELERY_RESULT_BACKEND = 'django-db'
# CELERY_RESULT_BACKEND = 'redis://redis:6379/1'
# CELERY_RESULT_BACKEND = 'redis://************:6699/0'
BROKER_CONNECTION_RETRY_ON_STARTUP = True
# CELERY_RESULT_BACKEND = 'redis://127.0.0.1:6680/1'
CELERY_TASK_SERIALIZER = 'json'
# CELERY_CACHE_BACKEND = 'django-cache'
CELERYD_MAX_TASKS_PER_CHILD = '1'
# CELERY_TASK_RESULT_EXPIRES = 18000
CELERY_RESULT_SERIALIZER = 'json'

# Limit the size of file uploads to 500MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 524288000  # 500 MB in bytes

# Optionally, you can also limit the size of file uploads handled by file storage backends
FILE_UPLOAD_MAX_MEMORY_SIZE = 524288000  # 500 MB in bytes
