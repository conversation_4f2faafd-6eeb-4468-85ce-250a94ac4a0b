# Generated by Django 5.1.7 on 2025-03-20 01:17

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('datasheet', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='dataframe',
            name='uuid_user',
            field=models.ForeignKey(default='', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='frame_user', to=settings.AUTH_USER_MODEL, to_field='uuid_user'),
        ),
        migrations.AddField(
            model_name='datasheet',
            name='uuid_user',
            field=models.ForeignKey(default='', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sheet_user', to=settings.AUTH_USER_MODEL, to_field='uuid_user'),
        ),
        migrations.AddField(
            model_name='dataframe',
            name='uid_sheet',
            field=models.ForeignKey(default='', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sheet_frame', to='datasheet.datasheet'),
        ),
        migrations.AddField(
            model_name='page',
            name='uid_frame',
            field=models.ForeignKey(default='', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='page_id', to='datasheet.dataframe'),
        ),
    ]
