import os
from typing import List
from langchain_community.document_loaders import <PERSON><PERSON><PERSON>oader

from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.text_splitter import CharacterTextSplitter
from langchain.docstore.document import Document
from untils.embeddings.bge import BgeEmbedding
# from untils.embeddings.openai import OpenaiEmbedding
from untils.vector.retriever import MultiVectorStoreRetriever
from untils.common import get_all_files, read_json, recursive_character_length_function
from untils.vector.milvus import MilvusDriver
import shutil,json
from loguru import logger
from untils.vector.textsplit import TextSpilt
from django.core.cache import cache
from .models import DataFrame
class VectorDB:
    driver_map = {
        "milvus": MilvusDriver,
    }
    # embedding_map = {"openai": OpenaiEmbedding, "bge": BgeEmbedding}
    embedding_map = {"bge": BgeEmbedding}


    def __init__(self) -> None:
        driver_name = os.environ["VECTOR_DB_DRIVER"]
        driver_class = self.driver_map.get(driver_name)
        if not driver_class:
            raise ValueError(f"Invalid driver name: {driver_name}")

        embedding_name = os.environ["EMBEDDING_NAME"]
        embedding_class = self.embedding_map.get(embedding_name)
        if not embedding_class:
            raise ValueError(f"Invalid embedding name: {embedding_name}")

        self.embeddings = embedding_class()
        self.driver_class = driver_class

    def insert_from_folder(
        self,
        folder_path: str,
        database_name: str,
        collection_name: str,
        chunk_size: int = 512,
        chunk_overlap: int = 100,
        callback = None
    ):
        """Insert documents from a folder to the vector database.

        Args:
            folder_path (str): The folder path.
            database_name (str): The database name.
            collection_name (str): The collection name.
            partition_name (str): The partition name.
            chunk_size (int, optional): The chunk size. Defaults to 512.
            chunk_overlap (int, optional): The chunk overlap. Defaults to 200.
            callback (function, optional): The callback function. Defaults to None.
        """
        docs = []
        try:
            docs = TextSpilt.split_from_folder(folder_path)
            self.insert(docs, database_name, collection_name,callback)
        except Exception as e:
            logger.error(f"Error when loading file: {folder_path}",e)

    def insert_from_cache(
        self,
        cache_id:str,
        database_name: str,
        collection_name: str,
        filter:None,
        callback = None
    ):
        """Insert documents from a folder to the vector database.

        Args:
            folder_path (str): The folder path.
            database_name (str): The database name.
            collection_name (str): The collection name.
            partition_name (str): The partition name.
            chunk_size (int, optional): The chunk size. Defaults to 512.
            chunk_overlap (int, optional): The chunk overlap. Defaults to 200.
            callback (function, optional): The callback function. Defaults to None.
        """
        try:
            cache_data = cache.get(cache_id)
            if not cache_data:
                raise ValueError(f"{cache_id.split('_')[1]}:没有可上传的数据")
            stage = len(cache_data)
            for index, ch in enumerate(cache_data):
                logger.warning(f'{index},{len(cache_data)}+++++++++++{[filter]}')
                xlh = cache_data[index][0].metadata.get('id')
                
                if filter and xlh not in filter:
                    continue  # 跳过不在filter_xlh列表中的xlh
                try:
                    logger.info(f'-----------{xlh}')
                    self.insert(ch, database_name, collection_name,callback)
                    if callback:
                        callback(index+1, stage)
                    f = DataFrame.objects.get(id=xlh)
                    f.available = 1
                    f.save()
                    
                except Exception as item_error:
                    logger.error(f"上传第 {index+1} 出错: {str(item_error)}")
                    # 更新剩余的 DataFrame 的状态为 3
                    remaining_xlhs = [item[0].metadata.get('id') for item in cache_data[index+1:]]
                    DataFrame.objects.filter(id__in=remaining_xlhs).update(available=4)
                    
                    # Optionally log this update
                    logger.info(f"更新剩余元素状态为3，对应的数据集ID列表: {remaining_xlhs}")
                    # You can choose to handle the specific item error or raise it to stop the whole task
                    # task.update_state(
                    #     state='FAILURE',
                    #     meta={
                    #         'exc_type': type(item_error).__name__,
                    #         'exc_message': str(item_error),
                    #         'failed_index': index
                    #     }
                    # )
                    raise item_error
        except Exception as e:
            logger.error(f"Error when inserting from cache : {str(e)}")
            # task.update_state(
            #     state='FAILURE',
            #     meta={
            #         'exc_type': type(e).__name__,
            #         'exc_message': str(e),
            #         'failed_index': index if 'index' in locals() else 'N/A'  # Add index to meta if it exists
            #     }
            # )
            raise e
    def insert(
        self, documents: List[Document], database_name: str, collection_name: str,callback
    ):
        """Insert data to the vector database.

        Args:
            data (list): The data to insert.
            database_name (str): The database name.
            collection_name (str): The collection name.
            # partition_name (str): The partition name.
        """
        assert isinstance(documents, list)
        assert isinstance(database_name, str) and len(database_name) > 0
        assert isinstance(collection_name, str) and len(collection_name) > 0
        logger.warning(f'dabase:{database_name},collection:{collection_name}')
        driver = self.driver_class(self.embeddings, database_name, collection_name)

        driver.vector_store.add_documents(documents=documents,callback=callback)
    
    
    def delete_by_frame_id(
        self,
        documents:list,      
        database_name: str,
        collection_name: str,
        callback = None):
        self.delete(documents,database_name, collection_name,callback)    
        
    def delete(
        self, documents : list, database_name: str, collection_name: str,callback
    ):
        """Insert data to the vector database.

        Args:
            data (list): The data to insert.
            database_name (str): The database name.
            collection_name (str): The collection name.
            # partition_name (str): The partition name.
        """
        # assert isinstance(documents, list)
        assert isinstance(database_name, str) and len(database_name) > 0
        assert isinstance(collection_name, str) and len(collection_name) > 0
        logger.warning(f'delete message dabase:{database_name},collection:{collection_name}')
        driver = self.driver_class(self.embeddings, database_name, collection_name)

        driver.vector_store.delete_document(document_id=documents,callback=callback)

    def retriever(self,database_name,collection_name,top_k=10,score_threshold=0.45, max_token_limit=512):
        if len(database_name) != len(collection_name):
            raise ValueError('数据库名称和集合名称列表长度必须相等')
        
        store_list=[]

        for dn,cn in zip(database_name,collection_name):
            driver= self.driver_class(self.embeddings, dn, cn)
            vector_store=driver.vector_store
            # 更新搜索参数，确保 ef 大于 top_k
            vector_store.search_params = {
                "metric_type": "L2",
                "params": {
                    "ef": max(top_k + 100, 200),  # ef 值动态调整
                    "nprobe": min(max(top_k // 10, 10), 256)  # nprobe 也相应调整
                }
            }
            store_list.append(vector_store)

        retriever = MultiVectorStoreRetriever(
            vectorstore_list=store_list,
            # vectorstore=law_vector_store,
            # search_type="similarity",
            # search_kwargs={"k": 2},
            # llm=llm,
            max_token_limit=max_token_limit,
            search_type="similarity_score_threshold",
            search_kwargs={"k": top_k,"score_threshold":score_threshold}
        )

        return retriever
    
    

