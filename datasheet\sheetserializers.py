from rest_framework import serializers
from .models import DataSheet,DataFrame,Page

# 格式化返回数据格式
class SheetSerializer(serializers.ModelSerializer):
    total = serializers.IntegerField(read_only=True)

    class Meta:
        model = DataSheet
        fields = "__all__"

class PageSerializer(serializers.ModelSerializer):

    class Meta:
        model = Page
        fields = "__all__"

class FrameSerializer(serializers.ModelSerializer):
    desc = serializers.SerializerMethodField()

    class Meta:
        model = DataFrame
        fields = '__all__'

    def get_desc(self,obj):
        return obj.get('desc', '')[:256] if isinstance(obj, dict) else (obj.desc[:256] if obj.desc else '')

class FrameInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = DataFrame
        fields = ['id','file_name','rename']