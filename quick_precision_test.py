#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速精确度测试

使用你的实际配置进行快速测试
"""

import requests
import json

def quick_test():
    """快速测试你的配置"""
    
    # 测试查询
    test_queries = [
        "交通违章处理",
        "文档编辑方法", 
        "系统操作指南"
    ]
    
    base_config = {
        'database': ['default'],
        'collection': ['chzngk228'],
        'top_k': 5,
        'score_threshold': 0.45
    }
    
    for query in test_queries:
        print(f"\n🔍 测试查询: '{query}'")
        print("=" * 50)
        
        # 1. 整个知识库检索
        print("📚 整个知识库检索:")
        full_payload = {**base_config, 'text': query}
        
        try:
            response = requests.post('http://localhost:8000/datasheet/vectorsearch/', 
                                   json=full_payload, timeout=10)
            if response.status_code == 200:
                full_results = response.json().get('data', [])
                print(f"   结果数量: {len(full_results)}")
                
                # 显示文件分布
                files = {}
                for result in full_results[:3]:  # 只看前3个
                    file_name = result.get('metadata', {}).get('file_name', 'Unknown')
                    score = result.get('metadata', {}).get('score', 0)
                    if file_name not in files:
                        files[file_name] = []
                    files[file_name].append(score)
                
                for file, scores in files.items():
                    avg_score = sum(scores) / len(scores)
                    print(f"   📄 {file}: {len(scores)}个结果, 平均分数: {avg_score:.3f}")
            else:
                print(f"   ❌ 错误: HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ 错误: {str(e)}")
        
        # 2. 文件过滤检索
        print("\n📁 文件过滤检索:")
        filtered_payload = {
            **base_config, 
            'text': query,
            'selected_files': ['新建 文本文档 (2).txt', '道路交通.txt']
        }
        
        try:
            response = requests.post('http://localhost:8000/datasheet/filesearch/', 
                                   json=filtered_payload, timeout=10)
            if response.status_code == 200:
                data = response.json().get('data', {})
                filtered_results = data.get('results', [])
                print(f"   结果数量: {len(filtered_results)}")
                print(f"   过滤表达式: {data.get('filter_expr', 'None')}")
                
                # 显示结果质量
                if filtered_results:
                    scores = [r.get('metadata', {}).get('score', 0) for r in filtered_results]
                    avg_score = sum(scores) / len(scores) if scores else 0
                    print(f"   平均分数: {avg_score:.3f}")
                    
                    # 显示最佳结果
                    best_result = filtered_results[0]
                    content = best_result.get('page_content', '')[:100] + '...'
                    print(f"   最佳匹配: {content}")
            else:
                print(f"   ❌ 错误: HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ 错误: {str(e)}")
        
        print("\n" + "-" * 50)

if __name__ == '__main__':
    print("🧪 快速精确度测试")
    print("使用你的配置: database=['default'], collection=['chzngk228']")
    print("选择文件: ['新建 文本文档 (2).txt', '道路交通.txt']")
    quick_test()
